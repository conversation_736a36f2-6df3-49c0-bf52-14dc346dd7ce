<template>
  <view v-if="step === 1">
    <view class="bind-check-form">
      <my-cell prefix="手机号" align="right" required>
        <template #content>{{ bankForm.tel_no }}</template>
      </my-cell>
      <my-cell prefix="验证码" required align="left" suffix-width="180rpx">
        <template #suffix>
          <text class="btn-text-small" v-if="!isSend" @tap="getSmsCode"
            >获取验证码</text
          >
          <text class="color-low" v-if="isSend">{{ timeout }}秒后重发</text>
        </template>
        <template #content
          ><input
            class="my-input-m"
            v-model="smsCode"
            type="number"
            placeholder="请输入"
        /></template>
      </my-cell>
    </view>
    <view class="footer-fixed">
      <view class="p20">
        <button class="btn-primary" @tap="onCheckSmsCode">下一步</button>
      </view>
    </view>
  </view>
  <view v-else>
    <view class="tabs">
      <view
        class="tab-item"
        :class="{ active: tabActiveIndex === 0 }"
        @tap="tabActiveIndex = 0"
      >
        个人
      </view>
      <view
        class="tab-item"
        :class="{ active: tabActiveIndex === 1 }"
        @tap="tabActiveIndex = 1"
      >
        企业
      </view>
    </view>
    <view v-show="tabActiveIndex == 0">
      <my-cell prefix="持卡人" align="right" required>
        <template #content
          ><input
            class="my-input-m"
            v-model="bankForm.card_name"
            :disabled="userInfoSouce?.user_name"
            readonly
            placeholder="请输入"
        /></template>
      </my-cell>
      <my-cell prefix="证件号" align="right" required>
        <template #content
          ><input
            class="my-input-m"
            v-model="bankForm.cert_id"
            readonly
            placeholder="请输入"
        /></template>
      </my-cell>
      <my-cell prefix="银行卡" align="right" required>
        <template #content
          ><input
            class="my-input-m"
            v-model="bankForm.card_id"
            placeholder="请输入"
        /></template>
      </my-cell>
      <my-cell
        prefix="卡类型"
        align="right"
        arrow
        required
        @tap="onShowBankList"
      >
        <template #content>{{ bankForm.bank_name || "请选择" }}</template>
      </my-cell>
      <my-cell prefix="银行预留手机号" prefixWidth="280" align="right" required>
        <template #content
          ><input
            class="my-input-m"
            v-model="bankForm.tel_no"
            placeholder="请输入"
        /></template>
      </my-cell>
    </view>
    <!-- 企业 -->
    <view class="qiye-box" v-show="tabActiveIndex === 1">
      <view class="top-card">
        <view class="title"> 上传法人身份证照片 </view>
        <view class="label">
          <view class="label-t"> 请确保证件边框完整、字体清晰、亮度均匀 </view>
          <view class="label-t"> 若上传复印件,请加盖彩色企业公章 </view>
        </view>
      </view>
      <view class="upload-box">
        <view class="card">
          <image :src="idRen" mode="aspectFill" @tap="uploadImage('0')"></image>
          <view
            class="close"
            @tap="delImgHandel('0')"
            v-if="idRen.includes('card-') == false"
          >
            <text class="iconfont icon-close"></text>
          </view>
        </view>
        <view class="card">
          <image :src="idBei" mode="aspectFill" @tap="uploadImage('1')"></image>
          <view
            class="close"
            @tap="delImgHandel('1')"
            v-if="idBei.includes('card-') == false"
          >
            <text class="iconfont icon-close"></text>
          </view>
        </view>
      </view>
      <view class="form-box">
        <view class="form-item" label="" prop="name">
          <view class="item-content">
            <view class="label">
              <text class="b">*</text>
              <text>法人姓名</text>
            </view>
            <input
              v-model="firmForm.name"
              customStyle="border:none;color:#B6BEC5;"
              placeholder="请输入法人姓名"
            />
          </view>
        </view>
        <view class="form-item" label="" prop="idCode">
          <view class="item-content">
            <view class="label">
              <text class="b">*</text>
              <text>身份证号码</text>
            </view>
            <input
              v-model="firmForm.idCode"
              customStyle="border:none;color:#B6BEC5;"
              placeholder="请输入身份证号码"
            />
          </view>
        </view>
        <view class="form-item" label="" prop="idDate">
          <view class="item-content">
            <view class="label">
              <text class="b">*</text>
              <text>证件有效期</text>
            </view>
            <text
              @tap="isShowSelectCus = true"
              class="select-date"
              :style="dateIndex !== null ? 'color:#000' : 'color:#ccc'"
              >{{
                dateIndex == 1
                  ? "长期"
                  : dateIndex == 0
                  ? dateValue
                  : "请输入证件有效期"
              }}</text
            >
          </view>
        </view>
        <view class="form-item" label="" prop="phone">
          <view class="item-content">
            <view class="label">
              <text class="b">*</text>
              <text>法人手机号</text>
            </view>
            <input
              v-model="firmForm.phone"
              maxlength="11"
              customStyle="border:none;color:#B6BEC5;"
              placeholder="请输入法人手机号"
            />
          </view>
        </view>
        <!-- 营业执照 -->
        <view class="yingye-box">
          <view class="top">
            <view class="title"> 营业执照 </view>
            <view class="upload-box-2">
              <view class="left" @tap="uploadImage('2')" v-if="!yingImg">
                <text class="iconfont icon-jiahao1"></text>
                <text>上传照片</text>
              </view>
              <view class="left" @tap="uploadImage('2')" v-else>
                <image :src="yingImg" mode="aspectFill"></image>
                <view
                  class="close"
                  @tap.stop="delImgHandel('2')"
                  v-if="yingImg"
                >
                  <text class="iconfont icon-close"></text>
                </view>
              </view>
              <view class="right">
                <view class="top">
                  请确保营业执照字体清晰、完整、不能有遮挡请不要上传过大的图片,避免上传超时若上传复印件,请加盖企业公章
                </view>
                <view class="bottom">
                  若经营范围中包含括号,请核对括号是否为中文括号,名称错误将导致到账失败
                  图片识别信息,可能识别错误,请仔细识别结果
                </view>
              </view>
            </view>
          </view>
          <view class="form-item" label="" prop="qiye_name">
            <view class="item-content">
              <view class="label">
                <text class="b">*</text>
                <text>企业名称</text>
              </view>
              <input
                v-model="firmForm.qiye_name"
                customStyle="border:none;color:#B6BEC5;"
                placeholder="请输入企业名称"
              />
            </view>
          </view>
          <view class="form-item" label="" prop="code">
            <view class="item-content">
              <view class="label" :style="isAp ? 'width: 300rpx' : ''" >
                <text class="b">*</text>
                <text>社会信用代码</text>
              </view>
              <input
                v-model="firmForm.code"
                customStyle="border:none;color:#B6BEC5;"
                placeholder="请输入社会信用代码"
              />
            </view>
          </view>
          <view class="form-item" label="" prop="idDate">
            <view class="item-content">
              <view class="label">
                <text class="b">*</text>
                <text>证件有效期</text>
              </view>
              <text
                @tap="isShowSelectCusYingYe = true"
                class="select-date"
                :style="dateIndexYingYe !== null ? 'color:#000' : 'color:#ccc'"
                >{{
                  dateIndexYingYe == 1
                    ? "长期"
                    : dateIndexYingYe == 0
                    ? dateValueYingYe
                    : "请输入证件有效期"
                }}</text
              >
            </view>
          </view>
          <view class="form-item" label="" prop="scope">
            <view class="item-content">
              <view class="label">
                <text class="b">*</text>
                <text>经营范围</text>
              </view>
              <input
                v-model="firmForm.scope"
                customStyle="border:none;color:#B6BEC5;"
                placeholder="请输入部分经营范围"
              />
            </view>
          </view>
          <view class="form-item" label="" prop="selectRegion">
            <view class="item-content">
              <view class="label">
                <text class="b">*</text>
                <text @tap="areaPickerShow = true">企业所在地</text>
              </view>
              <text
                v-if="isAp"
                style="font-size: 32rpx"
                :style="!selectRegion ? 'color:#ccc' : 'color:#000'"
                @tap="clickMySelect"
                >{{ selectRegion || "请选择企业所在地" }}</text
              >
              <!-- <picker  v-if="!isAp" mode="multiSelector" range-key="title" :range="wxPickerData"  :value="wxPickerIdx"  @change="changeHandler" @columnchange="columnchange">
									<text style="font-size: 32rpx;" :style="!selectRegion ? 'color:#ccc' : 'color:#000'"
										>{{ selectRegion || '请选择企业所在地'  }}</text>
								</picker> -->
                <picker  v-if="!isAp" mode="selector" range-key="title" :range="wxPickerData[0]"   @change="changeSelectHandler">
									<text style="font-size: 32rpx;" :style="!selectRegion ? 'color:#ccc' : 'color:#000'"
										>{{ selectRegion || '请选择企业所在地'  }}</text>
								</picker>
            </view>
          </view>
          <view class="form-item" label="" prop="address">
            <view class="item-content">
              <view class="label">
                <text class="b">*</text>
                <text>详细地址</text>
              </view>
              <input
                v-model="firmForm.address"
                customStyle="border:none;color:#B6BEC5;"
                placeholder="请输入详细地址"
              />
            </view>
          </view>
          <view class="form-item" label="" prop="card_no">
            <view class="item-content">
              <view class="label">
                <text class="b">*</text>
                <text>银行账户</text>
              </view>
              <input
                v-model="firmForm.card_no"
                customStyle="border:none;color:#B6BEC5;"
                placeholder="请输入银行账户"
              />
            </view>
          </view>
          <view class="form-item" label="" prop="cardType">
            <view class="item-content">
              <view class="label">
                <text class="b">*</text>
                <text>卡类型</text>
              </view>
              <view class="ri" @tap="onShowBankList">
                <!-- <text v-if="!store.state.user.selectBanck.bank_name">请选择银行</text>-->
                <text style="color: #000">
                  {{ bankForm.bank_name || "请选择银行" }}</text
                >
                <text class="iconfont icon-youjiantou1"></text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="footer-fixed">
      <view class="p20">
        <button class="btn-primary" @tap="onSubmitBindBank">确定绑卡</button>
      </view>
    </view>
  </view>
  <MyPopup title="选择银行" :show="showBankList" @close="onShowBankList">
    <template #header>
    <view class="search">
      <input type="text" placeholder="请输入银行名称" confirm-type="search" v-model="searchValue" @input="changeHandel"  @confirm="bindconfirm"/>
        <text class="iconfont" @tap="bindconfirm">搜索</text>
    </view>
    </template>
    <template #content>
      <scroll-view :scroll-y="true" style="height: 600rpx">
        <my-cell
          v-for="bank in bankList"
          :key="bank.code"
          :prefix="bank.name"
          prefix-width="300"
          arrow
          @tap="onChooseBank(bank)"
        >
        </my-cell>
        <text v-if="bankList.length === 0" class="emty-txt">暂无</text>
      </scroll-view>
    </template>
  </MyPopup>
  <!-- 自定义选择组件 -->
  <view class="select-custom-com-mask" v-show="isShowSelectCus">
    <view class="select-custom-com">
      <view class="li" @tap="bindPickerChange(0)">
        <picker
          mode="date"
          v-if="!isAp"
          :value="dateValue"
          @change="bindDateChange"
        >
          非长期
        </picker>
        <text v-if="isAp">非长期</text>
      </view>
      <view class="li" @tap="bindPickerChange(1)"> 长期 </view>
      <view class="li" @tap="bindPickerChange(2)"> 取消 </view>
    </view>
  </view>
  <!-- 自定义选择组件 -->
  <view class="select-custom-com-mask" v-show="isShowSelectCusYingYe">
    <view class="select-custom-com">
      <view class="li" @tap="bindPickerChangeYingYe(0)">
        <picker
          mode="date"
          v-if="!isAp"
          :value="dateValueYingYe"
          @change="bindDateChangeYingYe"
        >
          非长期
        </picker>
        <text v-if="isAp">非长期</text>
      </view>
      <view class="li" @tap="bindPickerChangeYingYe(1)"> 长期 </view>
      <view class="li" @tap="bindPickerChangeYingYe(2)"> 取消 </view>
    </view>
  </view>

  <!-- 选择城市 -->
  <view class="select-custom-com-mask" v-show="isShowSelectCusChengshi">
    <view class="select-custom-com">
      <view class="li" @tap="isShowSelectCusChengshi = false">
        <picker  v-if="!isAp" mode="selector" range-key="title" :range="cityValueSelect"  @change="changeSelectCityHandler">
							<text style="font-size: 32rpx;">点击继续选择所在城市</text>
				</picker>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, unref, computed, watch } from "vue";
import request from "@/utils/request";
import MyCell from "@/components/MyCell";
import MyPopup from "@/components/MyPopup";
import MyInput from "@/components/MyInput";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
import { useGlobalStore } from "@/stores";
definePageConfig({
  navigationBarTitleText: "绑定银行卡",
  navigationBarBackgroundColor: "#1352fd",
  navigationBarTextStyle: "white",
});

const globalStore = useGlobalStore();

const step = ref(1);

const tabActiveIndex = ref(0);

const bankForm = ref({
  card_name: "",
  cert_id: "",
});

const userInfoSouce = ref({})

const dateVVVVV = ref(0)

const smsCode = ref("");

const isSend = ref(false);
const timeout = ref(60);

const idRen = ref("https://yimits.oss-cn-beijing.aliyuncs.com/images/card-ren.png");

const idBei = ref("https://yimits.oss-cn-beijing.aliyuncs.com/images/card-bei.png");

import cityCard from "../../utils/city_card.js";

const yingImg = ref(null);

const isShowSelectCusChengshi = ref(false)

const selectRegion = ref(); //选择的地区

import upload from "@/utils/upload";

const isAp = process.env.TARO_ENV === "alipay";

const area_code = ref(null);

const prov_code = ref(null);

const wxPickerIdx = ref(0)
	
const wxPickerData = ref()

const citys = []
let cities = []
cityCard.forEach((item) => {
  citys.push({title:item.title,value:item.value})
})
cities = cityCard[unref(wxPickerIdx)].cities
wxPickerData.value =  [citys,cities]

const firmForm = reactive({
  name: '',
  idCode: '',
  card_no: '',
  phone: '',
  qiye_name: "",
  code: "",
  scope: "",
  selectRegion: "",
  address: "",
  cardType: "",
  // idDate: "",
  // bank_name: '',
  // bank_code:''
});

const isShowSelectCus = ref(false);
const isShowSelectCusYingYe = ref(false);

// 点击预览
const previewImage = (type) => {
  if (type == 0) {
    Taro.previewImage({
      urls: [idRen.value],
      current: idRen.value[0],
    });
  } else if (type == 1) {
    Taro.previewImage({
      urls: [idBei.value],
      current: idBei.value[0],
    });
  } else if (type == 2) {
    Taro.previewImage({
      urls: [yingImg.value],
      current: yingImg.value[0],
    });
  }
};

const imageDataHandel = (type, serverUrl) => {
		Taro.showLoading({
			title: '识别中',
			mask: true
		});
      request
      .post({
        url: "account/identifyImg",
        data: {
          img_url: serverUrl,
          api_name: type == 2 ? 'bizlicense' : 'idcard'
        },
        showLoading:false
      })
      .then(resImgData => {
			try {
				const data = JSON.parse(resImgData.data)
				console.log(data);
				if (type == 2) {
					// 营业执照
					firmForm.code = data.reg_num
					firmForm.qiye_name = data.enterprise_name
					firmForm.address = data.address
					if (data.valid_period.includes('长期')) {
						dateIndexYingYe.value = 1
					} else {
						dateValueYingYe.value = data.valid_period.split('至')[1].replace(/年|月|日/g, "")
					}
				} else if (type == 1) {
					// 反面
					if (data.type != 'Back') {
						return Taro.showToast({
							icon: "error",
							title: '请上传国徽面！',
							success() {
								idRen.value = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/card-bei.png'
							}
						})
					}
					if (data.valid_date.includes('长期')) {
						dateIndex.value = 1
					} else {
						dateIndex.value = 0
						dateValue.value = data.valid_date.split('-')[1]
					}
				} else if (type == 0) {
					// 正面
					if (data.type != 'Front') {
						return Taro.showToast({
							icon: "error",
							title: '请上传人像面！',
							success() {
								idRen.value = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/card-ren.png'
							}
						})
					}
					firmForm.idCode = data.id
					firmForm.name = data.name
				}
			} catch (e) {
				//TODO handle the exception
				console.log('识别失败！手动填写');
			}
			Taro.hideLoading()
		}).catch(() => {
			Taro.hideLoading()
		})
	}

const uploadImage = (type) => {
  if (type == 0 && !unref(idRen).includes("card")) return previewImage(type);
  if (type == 1 && !unref(idBei).includes("card")) return previewImage(type);
  if (type == 2 && unref(yingImg)) return previewImage(type);
  // type: 0是身份证人像  1是身份证背面 2是营业执照
  upload.chooseImage(1).then((images) => {
    images.map((img) => {
      upload.uploadFile(img).then((url) => {
        if (type == 0) {
          idRen.value = url;
        } else if (type == 1) {
          idBei.value = url;
        } else if (type == 2) {
          yingImg.value = url;
        }
        console.log(url);
        imageDataHandel(type, url)
      });
    });
  });
};

const getDate = (type, ap = false) => {
  const date = new Date();
  let year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();

  if (type === "start") {
    year = year - 60;
  } else if (type === "end") {
    year = year + 2;
  }
  month = month > 9 ? month : "0" + month;
  day = day > 9 ? day : "0" + day;
  if (ap) {
    return `${year}-${month}-${day}`;
  } else {
    return `${year}${month}${day}`;
  }
};

const startDate = computed(() => getDate("start"));
const endDate = computed(() => getDate("end"));
const dateIndex = ref(null);
const dateIndexYingYe = ref(null);
const dateValue = ref(
  getDate({
    format: true,
  })
);
const dateValueYingYe = ref(
  getDate({
    format: true,
  })
);

const bindDateChange = (e) => {
  dateValue.value = e.detail.value.replaceAll("-", "");
};

const bindDateChangeYingYe = (e) => {
  dateValueYingYe.value = e.detail.value.replaceAll("-", "");
};

const bindPickerChange = (idx) => {
  if (idx == 2) return (isShowSelectCus.value = false);
  isShowSelectCus.value = false;
  dateIndex.value = idx;
  if (idx == 0) {
    // 非长期
    if (isAp) {
      my.datePicker({
        currentDate: getDate(
          {
            format: true,
          },
          true
        ),
        startDate: getDate("start", true),
        endDate: getDate("end", true),
        success: (res) => {
          dateValue.value = res.date.replaceAll("-", "");
        },
      });
    }
  }
};

const bindPickerChangeYingYe = (idx) => {
  if (idx == 2) return (isShowSelectCusYingYe.value = false);
  isShowSelectCusYingYe.value = false;
  dateIndexYingYe.value = idx;
  if (idx == 0) {
    // 非长期
    if (isAp) {
      my.datePicker({
        currentDate: getDate(
          {
            format: true,
          },
          true
        ),
        startDate: getDate("start", true),
        endDate: getDate("end", true),
        success: (res) => {
          // my.alert({
          // 	content: '您选择的日期为: ' + res.date
          // });
          dateValueYingYe.value = res.date.replaceAll("-", "");
        },
      });
    }
  }
};

const transApp = ref([])

const transAp = () => {
  const arr = []
  cityCard.forEach(item => {
    arr.push({
      name: item.title,
      subList: [
        ...item.cities.map(sitem => {
          return {
            name: sitem.title,
          }
        }),
      ]
    })
  })
  transApp.value = arr
  // console.log(arr);
  return arr
}
transAp()

const clickMySelect = () => {
		my.multiLevelSelect({
			title: '选择地区', //级联选择标题
			list: transApp.value,
			success: (res) => {
				console.log(res);
				selectRegion.value = `${res.result[0].name}-${res.result[1].name}`
				// my.alert({
				// 	content: `您选择了 ${res.result[0].name}>${res.result[1].name}`
				// })
			}
		});
	}

  const cityValueSelect = ref('')

  // 省选择
  const changeSelectHandler = (e) => {
    console.log(e.detail.value);//索引
    const curent = wxPickerData.value[0][e.detail.value]
    console.log(cityCard);
    console.log(wxPickerData.value);
    console.log(curent);
    console.log(curent.title);//省名字
    console.log(curent.value);//省code
		prov_code.value = curent.value
    selectRegion.value = `${curent.title}-未选择`
    cityCard.filter(item => {
      if(item.value == curent.value) {
        cityValueSelect.value = item.cities
        console.log(cityValueSelect.value,"cityValueSelect.value");
        isShowSelectCusChengshi.value = true
      }
    })
}

  // 市选择
const changeSelectCityHandler = (e) => {
  console.log(e.detail.value);//索引
  const curent = cityValueSelect.value[e.detail.value]
  console.log(curent.title);//市名字
  console.log(curent.value);//市code
	area_code.value = curent.value
  selectRegion.value = selectRegion.value.split('-')[0] + '-' + curent.title
  }

	// 选中时执行
	const changeHandler = (e) => {
		console.log(e);
		const res = e.detail.value
		selectRegion.value = `${cityCard[res[0]].title}-${cityCard[res[0]].cities[res[1]].title}`
		console.log('城市名字和代码：'+cityCard[res[0]].cities[res[1]].title+cityCard[res[0]].cities[res[1]].value);
		console.log('省名字和代码：'+cityCard[res[0]].title+cityCard[res[0]].value);
		area_code.value = cityCard[res[0]].cities[res[1]].value
		prov_code.value = cityCard[res[0]].value
		// cityCard.forEach(city => {
		// 	if (city.title == res[0]) {
		// 		city.cities.forEach(cityc => {
		// 			if (res[1].includes(cityc.title)) {
		// 				console.log('城市代码：' + cityc.value);
		// 				area_code.value = cityc.value
		// 			}
		// 		})
		// 		console.log('省代码：' + city.value);
		// 		prov_code.value = city.value
		// 	}
		// })
	}

  const columnchange = (e) => {
		console.log(e);
    dateVVVVV.value = +new Date()
		let a = []
		let b = []
		if(!e.detail.column) {
		cityCard.forEach((item) => {
			a.push({title:item.title,value:item.value})
		})
		b = cityCard[e.detail.value].cities
		wxPickerData.value =  [a,b]
		}
	}

// 删除
const delImgHandel = (type) => {
  if (type == 0) {
    idRen.value = "https://yimits.oss-cn-beijing.aliyuncs.com/images/card-ren.png";
  } else if (type == 1) {
    idBei.value = "https://yimits.oss-cn-beijing.aliyuncs.com/images/card-bei.png";
  } else if (type == 2) {
    yingImg.value = null;
  }
};

useLoad((query) => {
  console.log(query);
  // 获取对公信息
  request
    .get({
      url: "account/bankDetail",
    })
    .then((res) => {
      console.log(res);
     if(res.data?.address) {
      // 有信息 回显列表
				firmForm.name = res.data.legal_person
				firmForm.idCode = res.data.cert_id
				dateValue.value = res.data.legal_cert_id_expires
				firmForm.phone = res.data.legal_mp
				
				idRen.value = res.data.card_front
				idBei.value = res.data.card_back
				
				if(!res.data.card_front) {
					idRen.value = idRen
				}
				if(!res.data.card_back) {
					idRen.value = idBei.value = idRen
				}
				
				if(res.data.legal_cert_id_expires >= '********') {
					dateIndex.value = 1
				}else {
					dateIndex.value = 0
				}
				// 
				firmForm.qiye_name = res.data.name
				prov_code.value = res.data.prov_code
				area_code.value = res.data.area_code
				firmForm.code = res.data.social_credit_code
				firmForm.scope = res.data.business_scope
				firmForm.address = res.data.address
				firmForm.card_no =  res.data.card_id

				bankForm.value.bank_name = res.data.bank_name
				bankForm.value.bank_code  = res.data.bank_code

				if(res.data.bank_code) {
					firmForm.cardType = res.data.bank_code
				}
				if(res.data.prov_code && res.data.area_code) {
					// 回显地区
					cityCard.forEach(item => {
						if(item.value == res.data.prov_code) {
							item.cities.forEach(obj => {
								if(obj.value == res.data.area_code) {
									selectRegion.value = `${item.title}-${obj.title}`
								}
							})
						}
					})
				}
				yingImg.value = res.data.bank_img
				if(res.data.social_credit_code_expires >= '********') {
					dateIndexYingYe.value = 1
				}else {
					dateIndexYingYe.value = 0
				}
     }
    })
    .catch((e) => {
    });
  if (query.type == '1') {
    tabActiveIndex.value = 1
  }
  request
    .get({
      url: "user/info",
    })
    .then((res) => {
      userInfoSouce.value = res.data.fdd
      bankForm.value.card_name = res.data.fdd?.user_name;
      bankForm.value.cert_id = res.data.fdd?.ident_no;
      bankForm.value.tel_no = res.data.mobile;
    })
    .catch((e) => {
      Taro.clearStorageSync("token");
    });
  getBankCode();
});

const getSmsCode = () => {
  request
    .post({
      url: "account/sendSmsCode",
      data: {
        mobile: globalStore.userInfo.mobile,
      },
    })
    .then((res) => {
      isSend.value = true;
      timeoutCountdown();
    });
};

const onJump = (url) => {
  Taro.navigateTo({
    url: url,
  });
};

const timeoutCountdown = () => {
  setTimeout(() => {
    timeout.value = timeout.value - 1;

    if (timeout.value > 0) {
      timeoutCountdown();
    } else {
      isSend.value = false;
      timeout.value = 60;
    }
  }, 1000);
};

const onCheckSmsCode = () => {
  if (!smsCode.value) {
    Taro.showToast({
      title: "请输入短信验证码",
      icon: "none",
    });
  }
  request
    .post({
      url: "account/checkSmsCode",
      data: {
        sms_code: smsCode.value,
      },
    })
    .then((res) => {
      step.value = 2;
    });
};

const bankList = ref([]);
const searchValue = ref('')

const userBankList = ref([]);
const getBankCode = (flag=false) => {
  request
    .get({
      url: "account/bankCode",
      data: {
        keyword:!flag ? searchValue.value : ''
      }
    })
    .then((res) => {
      bankList.value = res.data;
      userBankList.value = res.data;
    });
};
const showBankList = ref(false);
const onShowBankList = () => {
  showBankList.value = !showBankList.value;
};

const qiyeBanList = computed(() => bankList.value.filter(item => item.code != '********'))

watch(() => tabActiveIndex.value, (val) => {
  if (val == 0) {
  bankList.value = userBankList.value
  } else {
    bankList.value = qiyeBanList.value
  }
})


const bindconfirm = (e) => {
  console.log(searchValue.value);
  getBankCode()
}

const changeHandel = (e) => {
  console.log(e.detail.value);
  if(!e.detail.value) getBankCode(true)
}

const onChooseBank = (bank) => {
  bankForm.value.bank_code = bank.code;
  bankForm.value.bank_name = bank.name;
  onShowBankList();
};


const onSubmitBindBank = () => {
  // 企业提交
  if (tabActiveIndex.value == 1) {
    if (idRen.value.includes("card-") || idBei.value.includes("card-"))
      return Taro.showToast({
        title: "请先上传身份证照片！",
        icon: "none",
      });
    if (!yingImg.value)
      return Taro.showToast({
        title: "请先上传营业执照！",
        icon: "none",
      });
    if (
      !firmForm.name ||
      !firmForm.idCode ||
      !firmForm.card_no ||
      !firmForm.phone ||
      !firmForm.qiye_name ||
      !firmForm.code ||
      !firmForm.scope ||
      !selectRegion.value ||
      !prov_code.value ||
      !area_code.value ||
      !firmForm.address ||
      // !firmForm.bank_name ||
      !bankForm.value.bank_code ||
      !dateValueYingYe.value ||
      !dateValue.value ||
      !yingImg.value
    ) {
      Taro.showToast({
        title: "表单填写不全",
        icon: "error",
      });
      return
    }
    request
    .post({
      url: "account/business",
      data: {
        legal_person: firmForm.name,
        cert_id: firmForm.idCode,
        legal_cert_id_expires: dateIndex.value != 1 ? dateValue.value : '********',
        legal_mp: firmForm.phone,
        card_front: idRen.value,
        card_back: idBei.value,
        name: firmForm.qiye_name,
        prov_code: unref(prov_code),
        area_code: unref(area_code),
        social_credit_code: firmForm.code,
        social_credit_code_expires: dateIndexYingYe.value != 1 ? unref(
          dateValueYingYe) : '********',
        business_scope: firmForm.scope,
        address: firmForm.address,
        bank_code: bankForm.value.bank_code,
        card_id: firmForm.card_no,
        card_name: firmForm.qiye_name,
        bank_img: yingImg.value,
        bank_name: bankForm.value.bank_name
      },
    })
    .then((res) => {
      request
      .get({
        url: "account/submit",
      })
      .then((res) => {
        console.log(res);
        Taro.showToast({
          title: "绑卡成功",
          duration: 2000,
        }).then((_) => {
          setTimeout(() => {
            request
            .get({
              url: "user/info",
              showLoading:false
            })
            .then((res) => {
              globalStore.setUserInfo(res.data);
              Taro.navigateBack();
            });
          },1300)
        });
      })
      .catch((e) => {
        console.log(e);
        Taro.showToast({
          icon: "none",
          title: e.data?.message?.error_msg || e.data?.message || '绑卡失败！',
        })
      });

    });
    return;
  }
  // 个人提交
  if (
    !bankForm.value.card_id ||
    !bankForm.value.card_name ||
    !bankForm.value.cert_id ||
    !bankForm.value.bank_code ||
    !bankForm.value.tel_no
  ) {
    Taro.showToast({
      title: "表单填写不全",
      icon: "error",
    });
    return;
  }
  request
    .post({
      url: "account/bindBank",
      data: bankForm.value,
    })
    .then((res) => {
      Taro.showToast({
        title: "绑卡成功",
        duration: 2000,
      }).then((_) => {
        request
          .get({
            url: "user/info",
          })
          .then((res) => {
            globalStore.setUserInfo(res.data);
            Taro.navigateBack();
          });
      });
    });
};
</script>

<style lang="scss">
page {
  background-color: #f7f9ff;
  background-color: #fff;
  position: relative;
  .search {
    // border-top: 1px solid #000;
    display: flex;
    align-items: center;
    justify-content: center;
    .iconfont {
      font-size: 30px;
    }
    input {
      font-size: 30px;
    }
  }
  .emty-txt {
    text-align: center;
    padding-top: 200rpx;
    color: #999;
    width: 750rpx;
    display: inline-block;
  }
  .select-custom-com-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    overflow: hidden;
    z-index: 999999999;

    .select-custom-com {
      background-color: #fff;
      width: 100%;
      max-height: 300rpx;
      position: fixed;
      bottom: 0;
      left: 0;
      z-index: 99999;

      .li {
        border-bottom: 1px solid #ccc;
        text-align: center;
        height: 100rpx;
        line-height: 100rpx;

        &:active {
          background-color: #e3e3e3;
        }
      }
    }
  }
}
.bind-check-form {
  background: #ffffff;
}
.tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 150rpx;
  padding-top: 50rpx;
  box-sizing: border-box;
  color: #fff;
  background-color: #1352fc;
  height: 120rpx;

  .tab-item {
    position: relative;
    // color: #1352FD;
    color: rgba(255, 255, 255, 0.6);

    &.active {
      color: #fff;

      &::before {
        position: absolute;
        left: 50%;
        bottom: -17rpx;
        background-color: #fff;
        width: 40rpx;
        height: 10rpx;
        display: block;
        content: "";
        transform: translateX(-50%);
        border-radius: 5rpx;
      }
    }
  }
}

.qiye-box {
  padding-bottom: calc(env(safe-area-inset-bottom) + 204rpx);
  background-color: #fff;
  .form-item {
    border-bottom: 1px solid #dfdfdf;
    height: 120rpx;
    justify-content: center;
    background-color: #fff;
    line-height: 119rpx;
    // padding-top: 20rpx;
    box-sizing: border-box;
    //   display: flex;
    // box-sizing: border-box;
    // flex-direction: column;
    // justify-content: center;

    .u-form-item__body__right__message {
      // margin-bottom: 10rpx;
      transform: translateY(-15rpx);
    }
  }

  .item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    padding-left: 30px;
    position: relative;
    padding-right: 30px;

    .select-date {
      font-size: 32rpx;
      color: #ccc;
    }

    .ri {
      color: #b6bec5;
      font-size: 32rpx;
    }

    input {
      text-align: right !important;
      font-size: 32rpx !important;
      z-index: 0;
    }

    // padding: 20rpx;
    // border-bottom: 1px solid #DFDFDF;
    // margin-top: -40rpx;
    .phone {
      color: #b6bec5;
      position: absolute;
      right: 40rpx;
      font-size: 30rpx;
    }

    .label {
      margin-right: 40rpx;
      font-size: 32rpx;

      .b {
        color: red;
        margin-right: 10rpx;
      }
    }

    .wrap {
      position: absolute;
      right: 40rpx;
      font-size: 32rpx;
    }
  }

  .top-card {
    padding: 25rpx;

    .title {
      font-size: 40rpx;
      font-weight: 700;
    }

    .label {
      color: #999;
      font-size: 30rpx;

      .label-t {
        margin-top: 10rpx;
      }
    }
  }

  .upload-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 25rpx;
    width: 100%;
    box-sizing: border-box;

    .card {
      width: 327rpx;
      height: 200rpx;
      border-radius: 15rpx;
      overflow: hidden;
      position: relative;

      image {
        width: 100%;
        height: 100%;
        // width: 397rpx;
        object-fit: contain;
      }

      .close {
        position: absolute;
        top: 15rpx;
        right: 15rpx;
        color: #fff;
        width: 50rpx;
        height: 50rpx;
        background-color: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        text-align: center;
        line-height: 50rpx;
        font-size: 32rpx;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .yingye-box {
    padding: 25rpx;

    .top {
      .title {
        font-size: 40rpx;
        font-weight: 700;
      }
    }

    .upload-box-2 {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left {
        width: 150rpx;
        height: 200rpx;
        flex-shrink: 0;
        background-color: #f5f8ff;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        color: #999;
        margin: 15rpx 0;
        margin-right: 20rpx;
        position: relative;

        .close {
          position: absolute;
          top: 15rpx;
          right: 15rpx;
          color: #fff;
          width: 50rpx;
          height: 50rpx;
          background-color: rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          text-align: center;
          line-height: 50rpx;

          .iconfont {
            font-size: 32rpx !important;
          }
        }

        image {
          width: 100%;
          height: 100%;
        }

        text {
          font-size: 25rpx;
        }

        .iconfont {
          font-size: 60rpx;
        }
      }

      .right {
        font-size: 26rpx;

        .top {
          color: #999;
        }

        .bottom {
          color: red;
          height: auto;
        }
      }
    }
  }
}
</style>
