<script setup>
import { ref, unref, reactive } from "vue";
import dayjs from "dayjs";
import Taro, {
  useDidShow,
  useLoad,
  useReachBottom,
  usePullDownRefresh,
} from "@tarojs/taro";
import request from "@/utils/request";

const date = ref(dayjs().format("YYYY-MM"));

const itemObj = ref(null);

const pages = reactive({
  limit: 5,
  page: 1,
  status: "", //0=分摊中 1=成功 3=失败
});

const total = ref(0);

const list = ref([]);

useLoad((option) => {
  if (!option.item) return;
  const obj = JSON.parse(option.item);
  itemObj.value = obj;
  getLogList();
});

usePullDownRefresh(() => {
  console.log("refresh");
  pages.page = 1;
  getLogList();
  Taro.stopPullDownRefresh();
});

useReachBottom(() => {
  console.log("bottom");
  if (list.value.length >= total.value) return;
  pages.page += 1;
  getLogList(true);
});

const getLogList = (bol) => {
  request
    .get({
      url: "device/busLog",
      data: {
        ...pages,
        class_id: itemObj.value?.id,
        date: date.value,
        device_id: itemObj.value?.device_id
      },
    })
    .then((res) => {
      if (res.code != 200) return;
      total.value = res.data.total;
      if (bol) {
        list.value = list.value.concat(res.data.list);
      } else {
        list.value = res.data.list;
      }
    });
};

/**
 * 将日期字符串转换为前一天的 YYYY-MM-DD 格式字符串
 *
 * @param {string} dateString - 原始日期字符串 (YYYY-MM-DD HH:mm:ss 或 YYYY-MM-DD 格式)
 * @returns {string} - 前一天的 YYYY-MM-DD 格式字符串，如果输入无效则返回 null
 */
function getPreviousDay(dateString) {
  try {
    const date = new Date(dateString);

    if (isNaN(date.getTime())) {
      return null; // 无效的日期字符串
    }

    date.setDate(date.getDate() - 1);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;

  } catch (error) {
    console.error("日期转换错误:", error);
    return null; // 处理可能出现的错误
  }
}

const leftHandel = () => {
  date.value = dayjs(date.value).subtract(1, "month").format("YYYY-MM");
  getLogList();
};

const rightHandel = () => {
  date.value = dayjs(date.value).add(1, "month").format("YYYY-MM");
  getLogList();
};

const toLogHandel = (item) => {
  const obj = {
    id: item.id,
    class_id: itemObj.value?.id,
    p_sn: itemObj.value?.sn,
  };
  Taro.navigateTo({
    url:
      "/pages/other/publicLogDetail/publicLogDetail?item=" +
      JSON.stringify(obj),
  });
};
</script>

<template>
  <view class="public-log-container">
    <view class="date-box">
      <view class="left" @tap="leftHandel">
        <text
          class="iconfont icon-youjiantou1"
          :style="{ transform: 'rotate(180deg)', display: 'block' }"
        ></text>
      </view>
      <text class="date">{{ date }}</text>
      <view class="right" @tap="rightHandel">
        <text class="iconfont icon-youjiantou1"></text>
      </view>
    </view>
    <view v-if="list.length <= 0" class="list-emty"> 暂无数据~ </view>
    <view class="list" v-for="item in list" :key="item.id">
      <view class="li">
        <view class="le"> 公摊电量为{{ getPreviousDay(item.created_at) }}耗电量 </view>
        <!-- <view class="ri"> {{ item?.before_time }} </view> -->
      </view>
      <view class="li">
        <view class="le"> 公摊时间： </view>
        <view class="ri"> {{ item?.created_at }} </view>
      </view>
      <view class="li">
        <view class="le"> 开始电量(kWh)： </view>
        <view class="ri"> {{ (item?.main_du - item?.du).toFixed(2)}} </view>
      </view>
      <view class="li">
        <view class="le"> 结束电量(kWh)： </view>
        <view class="ri"> {{ item?.main_du }} </view>
      </view>
      <view class="li">
        <view class="le"> 使用电量(kWh)： </view>
        <view class="ri"> {{ item?.du }} </view>
      </view>
      <view class="li">
        <view class="le"> 任务结果： </view>
        <view
          class="ri"
          :style="
            item?.status == 1
              ? 'color: #298E31'
              : tem?.status == 0
              ? 'color: #158FFF'
              : 'color: #ff0000'
          "
        >
          {{
            item?.status == 1 ? "成功" : tem?.status == 0 ? "分摊中" : "失败"
          }}
        </view>
      </view>
      <view class="detail">
        <text @tap="toLogHandel(item)"> 公摊明细</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
page {
  background-color: #f8f9fe;
}
.public-log-container {
  .list-emty {
    text-align: center;
    margin-top: 20px;
    color: #999;
    font-size: 28px;
  }
  .date-box {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 20rpx;
    height: 80rpx;
    // background-color: #fff;
    text {
      font-size: 28rpx;
      color: #214fcb;
    }
    .date {
      margin: 0 40px;
    }
    .left,
    .right {
      width: 48px;
      height: 29px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #494b5c;
      border-radius: 20px;
      .iconfont {
        font-size: 24px;
        color: #fff;
      }
    }
  }
  .list {
    margin-top: 20rpx;
    background-color: #fff;
    .li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 25rpx;
      height: 60rpx;
      // border-bottom: 1px solid #f8f9fe;
      font-size: 30rpx;
      margin-bottom: 25px;
      .le {
        color: #000;
      }
      .ri {
        color: #333;
      }
    }
    .detail {
      text-align: right;
      color: #214fcb;
      padding-bottom: 20px;
      border-top: 1px solid #f8f9fe;
      padding-top: 20px;
      padding-right: 20px;

      text {
        border-bottom: 1px solid #214fcb;
        padding-bottom: 1px;
      }
    }
  }
}
</style>
