<script setup>
import unSelect from "@/assets/pic/yuan.png";
import select from "@/assets/pic/gouxuan.png";
import del from "@/assets/pic/del.png";
import { AtCheckbox } from "taro-ui-vue3";
import { ref } from "vue";
import MyPopup from "@/components/MyPopup";
import request from "@/utils/request";
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import { watch } from "vue";
import { useGlobalStore } from "@/stores";
import GlobDialog from "@/components/globDialog/index.vue";
import YModal from "@/components/YModal";
const totalD = 'https://yimits.oss-cn-beijing.aliyuncs.com/miniProgram/223531730875525_.pic.jpg'
const fenD = 'https://yimits.oss-cn-beijing.aliyuncs.com/miniProgram/223541730875525_.pic.jpg'

const globalStore = useGlobalStore();

const checkedList = ref([]);

const showTipModal = ref(false)

const curType = ref()

const tipImg = ref(totalD)

const checkedDevIceList = ref([]);

const searchVal = ref("");

const isJun = ref(true);

const editPop = ref(false);

const delPop = ref(false);

const rate = ref();

const shareDetail = ref();

const curentObj = ref();

const curentDevice = ref();

const curentCopyDevice = ref();

const allSonDevices = ref([]);

const curentEdit = ref({
  bus_device: {
    rate: "",
  },
});

useLoad((opt) => {
  console.log(opt.obj);
  const obj = JSON.parse(opt.obj);
  console.log(obj);
  curentObj.value = obj;
  getDetail(obj.device_id);
});

useDidShow(() => {
  getCurList();
  if (curentDevice.value) {
    getDetail(curentObj.value.device_id);
  }
});

// 获取当前分类
const getCurList = () => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "houseClass/list",
        data: {
          type: "edit",
        },
      })
      .then((res) => {
        if (res.code != 200) return;
        console.log(res);
        allSonDevices.value.splice(0);
        res.data.forEach((item) => {
          if (item.id == curentObj.value.tag_id) {
            curentDevice.value = item;
            curentCopyDevice.value = item;
            console.log(curentCopyDevice.value, "curentCopyDevice.value");
            console.log(curentDevice.value, "curentDevice.value");
            item.houses.forEach((item2) => {
              item2.device.forEach((item3) => {
                if (
                  item3?.bus_device &&
                  item3.bus_device?.type == 2 &&
                  item3?.bus_device?.status
                ) {
                  allSonDevices.value.push(item3);
                }
              });
            });
          }

        });
        console.log(allSonDevices.value, "allSonDevices.value");

        resolve();
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const getDetail = (device_id) => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "device/busDetail",
        data: {
          device_id,
        },
      })
      .then((res) => {
        if (res.code != 200) return;
        shareDetail.value = res.data;
        resolve()
      }).catch((err) => {
        reject(err)
      })
  })
};

const handleChange = (val, devId) => {
  // 查找 val 和 devId 在列表中的索引
  const idxVal = checkedList.value.indexOf(val);
  const idxDevId = checkedDevIceList.value.indexOf(devId);

  // 如果 val 已经在 checkedList 中，则移除，否则添加
  if (idxVal !== -1) {
    checkedList.value.splice(idxVal, 1);
  } else {
    checkedList.value.push(val);
  }

  // 如果 devId 已经在 checkedDevIceList 中，则移除，否则添加
  if (idxDevId !== -1) {
    checkedDevIceList.value.splice(idxDevId, 1);
  } else {
    checkedDevIceList.value.push(devId);
  }

  // 打印最终的列表
  console.log(checkedList.value, "checkedList");
  console.log(checkedDevIceList.value, "checkedDevIceList");
};

const setMode = (mac) => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "device/setMode",
        data: {
          mac,
          type: "1", //模式1-预付费模式 2-后付费模式
        },
        showLoading: false,
      })
      .then((res) => {
        if (res.code != 200) return reject();
        if (res.data?.pay_type == 2) {
          // 重试一次
          request
            .get({
              url: "device/setMode",
              data: {
                mac,
                type: "1", //模式1-预付费模式 2-后付费模式
              },
              showLoading: false,
            })
            .then((res) => {
              if (res.code != 200) return;
              if (res.data?.pay_type == 2) {
                Taro.showToast({
                  title: "设置模式失败请重试！",
                  icon: "none",
                  duration: 2000,
                });
                return reject();
              } else {
                resolve(res.data);
              }
            });
          return;
        }
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const delShareDeviceHandel = async () => {
  // Taro.showLoading({
  //   title: "解绑中...",
  //   mask: true,
  // });
  globalStore.setGlobDialog({ show: true, type: "setModeTimeLoading" });
  await setMode(curentObj.value?.device_mac);
  request
    .get({
      url: "device/removeBus",
      data: {
        bus_id: shareDetail.value?.id,
      },
      showLoading: true,
    })
    .then(() => {
      Taro.showToast({
        title: "解绑成功",
        icon: "success",
        duration: 2000,
      });
      globalStore.setGlobDialog({ show: false, type: "" });
      setTimeout(() => {
        Taro.navigateBack();
      }, 2000);
    });
};

let flagAdd = false
const addBusHandel = (item) => {
  console.log(item);
  if (flagAdd) return
  flagAdd = true
  // return
  request
    .post({
      url: "device/joinBus",
      data: {
        device_id: item.id,
        pid: shareDetail.value?.id,
        clear: 1,
        recharge: 1,
        status: 1,
      },
      showLoading: true,
    })
    .then(() => {
      Taro.showToast({
        title: "添加成功！",
        icon: "success",
        duration: 2000,
      });
      setTimeout(() => {
        getDetail(curentObj.value.device_id).finally(() => {
          getCurList().finally(() => {
            flagAdd = false
          })
        })
      }, 2000);
    })
    .finally(() => {});
};

let flag = false

const delBusHandel = (item) => {
  console.log(item);
  if(flag) return
  flag = true
  Taro.showModal({
    title: "提示",
    confirmText: "确定",
    cancelText: "直接移除",
    content: "是否要移除该设备并重新平均权重？",
    success: (res) => {
      if (res.confirm) {
        request
          .get({
            url: "device/removeBus",
            data: {
              bus_id: item?.bus_device?.id,
            },
            showLoading: true,
          })
          .then((res) => {
            if (res.code != 200) return;
            request
              .get({
                url: "device/normalRate",
                data: {
                  bus_id: shareDetail.value?.id,
                },
                showLoading: true,
              })
              .then((res) => {
                if (res.code != 200) return;
                Taro.showToast({
                  title: "退出成功！",
                  icon: "success",
                  duration: 2000,
                });
                setTimeout(() => {
                  getDetail(curentObj.value.device_id).finally(() => {
                    getCurList().finally(() => {
                      flag = false
                    })
                  })
                }, 1000);
              })
              .finally(() => {});
          })
          .finally(() => {});
      } else {
        request
          .get({
            url: "device/removeBus",
            data: {
              bus_id: item?.bus_device?.id,
            },
            showLoading: true,
          })
          .then((res) => {
            if (res.code != 200) return;
            Taro.showToast({
              title: "退出成功！",
              icon: "success",
              duration: 2000,
            });
            setTimeout(() => {
              getDetail(curentObj.value.device_id);
              getCurList();
            }, 1000);
          })
          .finally(() => {});
      }
    },
  });
};

const juanHandel = () => {
  // isJun.value = !isJun.value;
  // console.log(isJun.value);
  Taro.showModal({
    title: "提示",
    content: "确定要平均权重吗？",
    success: (res) => {
      if (res.confirm) {
        request
          .get({
            url: "device/normalRate",
            data: {
              bus_id: shareDetail.value?.id,
            },
            showLoading: true,
          })
          .then((res) => {
            if (res.code != 200) return;
            Taro.showToast({
              title: "开启成功！",
              icon: "success",
              duration: 2000,
            });
            setTimeout(() => {
              getDetail(curentObj.value.device_id);
              getCurList();
            }, 1000);
          })
          .finally(() => {});
      } else {
      }
    },
  });
};

const editHandel = (sitem) => {
  editPop.value = true;
  console.log(sitem);
  curentEdit.value = sitem;
  rate.value = sitem?.bus_device?.rate;
  const totalRate = allSonDevices.value.reduce((pre, cur) => {
    return pre + Number(cur.bus_device.rate);
  }, 0);
  console.log(totalRate, "totalRate");
};

const confirmCreateHandel = () => {
  const totalRate = allSonDevices.value.reduce((pre, cur) => {
    return pre + Number(cur.bus_device.rate);
  }, 0);

  console.log(totalRate, "totalRate");
  console.log(rate.value, "rate.value");
  console.log(
    curentEdit.value?.bus_device.rate,
    " curentEdit.value?.bus_device.rate"
  );

  console.log(
    totalRate - Number(curentEdit.value?.bus_device.rate) + Number(rate.value)
  );

  if (
    totalRate - Number(curentEdit.value?.bus_device.rate) + Number(rate.value) >
    100
  ) {
    Taro.showToast({
      title: "分表总比例不能超过100%",
      icon: "none",
      duration: 2000,
    });
    return;
  }
  request
    .post({
      url: "device/joinBus",
      data: {
        device_id: curentEdit.value?.bus_device?.device_id,
        pid: shareDetail.value?.id,
        rate: rate.value,
        clear: curentEdit.value?.bus_device?.clear ? 1 : 0,
        recharge: curentEdit.value?.bus_device?.recharge ? 1 : 0,
        status: curentEdit.value?.bus_device?.status ? 1 : 0,
        remark: curentEdit.value?.bus_device?.remark,
      },
      showLoading: true,
    })
    .then(() => {
      editPop.value = false;
      Taro.showToast({
        title: "修改成功！",
        icon: "none",
        duration: 2000,
      });
      setTimeout(() => {
        getDetail(curentObj.value.device_id);
        getCurList();
      }, 1000);
    });
};

const saveHandel = () => {
  if (checkedDevIceList.value.length == 0) {
    Taro.showToast({
      title: "请先选择分表",
      icon: "none",
      duration: 2000,
    });
    return;
  }
};

const toDetail = (item) => {
  if (!item?.bus_device) {
    Taro.showToast({
      title: "请先加入公摊",
      icon: "none",
    });
    return;
  }
  let arr = [];
  if (item?.bus_device?.type == 1) return;

  curentDevice.value.houses.forEach((house) => {
    house.device.forEach((dev) => {
      if (
        dev.bus_device &&
        dev.bus_device?.type == 1 &&
        !arr.some((existingDev) => existingDev.id === dev.id) // 检查是否已经存在
      ) {
        console.log(dev, "maindev");
        arr.push(dev);
      }
    });
  });

  console.log(arr, "Arr");

  const obj = {
    sn: item?.sn,
    device_id: item?.id,
    public_sn: arr[0]?.sn,
    public_device_id: arr[0]?.id,
    // bus_id: item?.bus_device?.id,
    status: item?.bus_device?.status,
    tag_name: curentDevice.value?.label,
    rate: item?.bus_device?.rate,
    recharge: item?.bus_device?.recharge,
    remark: item?.bus_device?.remark,
    clear: item?.bus_device?.clear,
    tag_id: curentDevice.value?.id,
  };
  console.log(obj);
  Taro.navigateTo({
    url:
      "/pages/other/publicShareSonSetting/publicShareSonSetting?item=" +
      JSON.stringify(obj),
  });
};

const editBusModeHandel = () => {
  Taro.showActionSheet({
    itemList: ["子表模式", "总表模式"],
    success: async function (res) {
      let type = res.tapIndex == 0 ? 1 : 2;
      if (process.env.TARO_ENV === "alipay") {
       type = res.index == 0 ? 1 : 2;
      }
      showTipModal.value = true
      if (type == 1) {
        tipImg.value = fenD
      } else {
        tipImg.value = totalD
      }
      curType.value = type
    },
  });
};

const searchHandle = (e) => {
  return;
  console.log(e.detail.value);
  console.log(curentCopyDevice.value);

  // 如果输入为空，恢复原始数据
  if (!e.detail.value) {
    curentDevice.value.houses = JSON.parse(
      JSON.stringify(curentCopyDevice.value.houses)
    ); // 深拷贝，避免引用影响
    return;
  }

  // 对 houses 进行筛选，确保 curentCopyDevice 不受影响
  curentDevice.value.houses = JSON.parse(
    JSON.stringify(curentCopyDevice.value.houses)
  ).map((item) => {
    return {
      ...item,
      device: item.device.filter((devItem) =>
        devItem.sn.includes(e.detail.value)
      ), // 仅保留符合条件的设备
    };
  });
};

const tipConfirmHandel = () => {
  showTipModal.value = false
  Taro.showModal({
    title: "提示",
    content: `确定要修改为${
      curType.value == 1 ? "子表模式" : "总表模式"
    }吗？`,
    success: async function (res) {
      if (res.confirm) {
        console.log("用户点击确定");
        request
          .get({
            url: "device/editBus",
            data: {
              bus_id: shareDetail.value?.id,
              count_type: curType.value,
            },
            showLoading: true,
          })
          .then((res) => {
            if (res.code != 200) return;
            console.log("修改成功");
            getDetail(curentObj.value.device_id);
            getCurList();
            setTimeout(() => {
              Taro.showToast({
                title: "修改成功！",
                icon: "none",
                duration: 2000,
              });
            }, 300);
          })
          .finally(() => {
            Taro.hideLoading();
          });
      } else {
        console.log("用户点击取消");
      }
    },
  });
}

const previewImage = (url) => {
  Taro.previewImage({
    urls: [url],
  });
};
</script>

<template>
  <view class="public-share-setting">
    <view class="top-detail">
      <view class="top-box">
        <view class="title">
          <text class="iconfont icon-dianbiao_shiti"></text>
          <text>公摊主表：{{ shareDetail?.sn }} </text>
          <image
            :src="del"
            mode="scaleToFill"
            class="del"
            @tap="delPop = true"
          />
          <text
            class="iconfont icon-bianji1"
            @tap.stop="editBusModeHandel"
          ></text>
        </view>
        <view class="desc"
          >({{ curentObj?.label }}_公摊_{{
            shareDetail?.count_type == 1 ? "子表模式" : "总表模式"
          }})</view
        >
      </view>
      <view class="bot-box">
        <view class="left">
          <view>选择分表数量：{{ shareDetail?.all_device }}</view>
          <view>启用分表数量：{{ shareDetail?.start_device }}</view>
          <view>启用表总权重：{{ shareDetail?.all_rate }}%</view>
        </view>
        <view class="right" @tap="juanHandel">
          <image
            :src="isJun ? select : unSelect"
            mode="scaleToFill"
            class="icon-s"
          />
          <text>平均权重</text>
        </view>
      </view>
    </view>
    <view class="search-box">
      <view class="search">
        <text class="iconfont icon-search2"></text>
        <input type="text" placeholder="查询电表号码" v-model="searchVal" />
      </view>
    </view>
    <view class="section">
      <view class="li"> 启用情况 </view>
      <view class="li"> 设备名称 </view>
      <view class="li"> 权重(%) </view>
      <view class="li"> 操作 </view>
    </view>
    <view class="detail">
      <view class="label">{{ curentDevice?.label }}</view>
      <view
        v-if="
          curentDevice?.houses.filter((item) =>
            item.device.some(
              (dev) => dev?.sn != shareDetail?.sn && dev.sn.includes(searchVal) && dev.type != 2
            )
          ).length <= 0
        "
        style="color: #ccc; font-size: 30rpx; text-align: center"
        >暂无可用设备~</view
      >
      <view
        class="li"
        v-for="oitem in curentDevice?.houses.filter((item) =>
          item.device.some(
            (dev) => dev?.sn != shareDetail?.sn && dev.sn.includes(searchVal) && dev.type != 2
          )
        )"
        :key="oitem.id"
      >
        <view class="title">
          <image
            src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
            mode="scaleToFill"
            class="icon-s"
          />
          <text>{{ oitem?.name }}</text>
        </view>
        <view
          v-if="
            oitem.device.filter(
              (dev) => dev?.sn != shareDetail?.sn && dev.sn.includes(searchVal) && dev.type != 2
            ).length <= 0
          "
          style="color: #ccc; font-size: 30rpx; text-align: center"
          >暂无可用设备~</view
        >
        <view
          class="box"
          v-for="(sitem, idx) in oitem.device.filter((dev) =>
            dev.sn.includes(searchVal) && dev.type != 2
          )"
          :key="idx"
        >
          <template v-if="sitem?.sn != shareDetail?.sn">
            <view
              class="name"
              @tap="handleChange(sitem?.bus_device.id, sitem?.id)"
            >
              <!-- <at-checkbox
                :options="[{ label: '', value: sitem?.bus_device.id }]"
                :selectedList="checkedList"
                v-if="sitem?.bus_device && sitem?.bus_device.pid"
              /> -->
              <text
                class="to"
                v-if="
                  sitem?.bus_device &&
                  sitem?.bus_device.pid &&
                  sitem?.bus_device.status == 1
                "
                >分摊中</text
              >
              <text
                class="to"
                style="color: #0072ff; border-color: #0072ff"
                v-if="
                  sitem?.bus_device &&
                  sitem?.bus_device.pid &&
                  !sitem?.bus_device.status
                "
                >分摊关闭</text
              >
              <text
                class="to"
                style="color: #0072ff; border-color: #0072ff"
                v-if="!sitem?.bus_device"
                >未加入</text
              >
              <text class="iconfont icon-dianbiao_shiti"></text>
              <text
                class="sn"
                @tap="toDetail(sitem)"
                :style="
                  sitem?.net_type == 1 && sitem?.signal_num <= 0
                    ? 'color: #98A6C3;'
                    : ''
                "
                >{{ sitem?.net_type == 1 ? "(电表)" : "(电表)" }}-{{
                  sitem?.sn
                }}</text
              >
            </view>
            <view class="desc" v-if="sitem?.bus_device">
              <text
                class="iconfont icon-setting"
                @tap.stop="editHandel(sitem)"
                v-if="isJun"
              ></text>
              <text @tap.stop="editHandel(sitem)" v-if="sitem?.bus_device?.rate"
                >{{ sitem?.bus_device?.rate }}%</text
              >
              <text v-else>0.00</text>
            </view>
            <view class="desc" v-else> 0% </view>
            <view
              class="action"
              v-if="sitem?.bus_device && sitem?.bus_device.pid"
              :style="{ color: 'red' }"
            >
              <text @tap="delBusHandel(sitem)">退出公摊</text>
            </view>
            <view
              class="action"
              v-if="!sitem?.bus_device"
              :style="{ color: '#5BC650' }"
              @tap="addBusHandel(sitem)"
              >加入公摊</view
            >
          </template>
        </view>
      </view>
    </view>
    <!-- utils -->
    <!-- <view class="footer-fixed">
      <button class="btn-add m33" @tap="saveHandel">保存设置</button>
    </view> -->
    <!-- 修改权重popup -->
    <MyPopup :show="editPop" @close="editPop = false">
      <template #content>
        <view class="edit-pop-content">
          <view class="top">
            <view class="pop-title"> 修改权重</view>
            <view class="ipt">
              <input placeholder="请输入权重比例数字" v-model="rate" />
              <text>%</text>
            </view>
            <view class="tip">
              <text class="iconfont icon-tanhao"></text>
              <text>您正在修改的权重电表为：</text>
            </view>
            <view class="tip-sn"> （电表）：“{{ curentEdit?.sn }}” </view>
          </view>
          <view class="utils">
            <view @tap="editPop = false">取消</view>
            <button @tap="confirmCreateHandel">保存</button>
          </view>
        </view>
      </template>
    </MyPopup>

    <!-- confirm -->
    <MyPopup :show="delPop" @close="delPop = false">
      <template #content>
        <view class="confirm-pop-content">
          <view class="top">
            <view class="pop-title"> 确认解绑吗？将会同时移除子表</view>
          </view>
          <view class="utils">
            <view @tap="delPop = false">取消</view>
            <button @tap="delShareDeviceHandel">确定</button>
          </view>
        </view>
      </template>
    </MyPopup>
    <GlobDialog
      @confirm="confirmGlobHandel"
      setModeMsg="正在解绑中...请稍等!"
    />

    <YModal
      title="提示"
      confirmText="继续"
      :show="showTipModal"
      @close="showTipModal = false"
      @confirm="tipConfirmHandel"
      showCancel
    >
      <template #content>
        <view class="content-modal-pubilc-share">
          <view>
            <image
              :src="tipImg"
              @tap="previewImage(tipImg)"
              mode="aspectFill"
            />
          </view>
        </view>
      </template>
    </YModal>
  </view>
</template>

<style lang="scss">
@import "taro-ui-vue3/dist/style/components/checkbox.scss";

page {
  background-color: #f7f9ff;
}
.public-share-setting {
  // padding-bottom: 180px;
  .content-modal-pubilc-share {
    padding: 20px;
    overflow: hidden;
    image {
      width: 100%;
      height: 460px;
      object-fit: contain;
      transform: translateY(65px);
    }
    >view{
      margin-bottom: 20px;
      text-align: center;
      // font-weight: 700;
      font-size: 35px;
    }
  }
  .confirm-pop-content {
    .top {
      padding: 80px 60px;
    }
    .pop-title {
      font-size: 35px;
      text-align: center;
      // padding-top: 53px;
    }
    .utils {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 0;
      border-top: 2px solid #e5e5e5;
      box-sizing: border-box;
      view,
      button {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 220px;
        height: 89px;
        background: #fbfcfd;
        border-radius: 20px;
        margin: 0;
        color: #000;
      }
      button {
        color: #fff;
        background-color: #2e4dcf;
        margin-left: 20px;
        width: 420px;
      }
    }
  }
  .edit-pop-content {
    background-color: #fff;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    .top {
      padding: 0 60px;
      padding-bottom: 180px;
    }
    .pop-title {
      font-size: 40px;
      text-align: center;
      font-weight: 700;
      padding-top: 53px;
    }
    .ipt {
      display: flex;
      align-items: center;
      border: 1px solid #e5e5e5;
      padding: 20px 35px;
      border-radius: 14px;
      margin-top: 48px;
      margin-bottom: 20px;
      input {
        width: 100%;
        font-size: 32px;
        padding-left: 0px;
      }
      text {
        font-size: 32px;
        font-weight: 700;
      }
    }
    .tip {
      display: flex;
      align-items: center;
      font-size: 26px;
      .iconfont {
        color: #ee742f;
        font-size: 28px;
        margin-right: 5px;
      }
    }
    .tip-sn {
      font-size: 26px;
      margin-top: 10px;
      color: #1352fd;
    }
    .utils {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 0;
      border-top: 2px solid #e5e5e5;
      view,
      button {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 320px;
        height: 89px;
        background: #fbfcfd;
        border-radius: 20px;
        border: 2px solid #2e4dcf;
        margin: 0;
        color: #2e4dcf;
      }
      button {
        color: #fff;
        background-color: #2e4dcf;
        margin-left: 20px;
      }
    }
  }
  .top-detail {
    // height: 325px;
    background-color: #1352fd;
    .top-box {
      padding-top: 20px;
      border-bottom: 1px solid #4c77ed;
      padding-bottom: 40px;
      box-sizing: border-box;

      .desc {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #fff;
        opacity: 0.5;
        margin-top: 16px;
      }
      .title {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36px;
        color: #fff;
        .icon-dianbiao_shiti {
          color: #fff;
          font-size: 36px;
          margin-right: 5px;
        }
        .icon-bianji1 {
          color: #fff;
          font-size: 36px;
          margin-left: 15px;
        }
        .del {
          width: 35px;
          height: 35px;
          margin-left: 10px;
        }
      }
    }
    .bot-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 35px 25px 35px 40px;
      box-sizing: border-box;
      .left {
        color: #bbc6e2;
        font-size: 26px;
      }
      .right {
        width: 130px;
        height: 127px;
        background: rgba(255, 255, 255, 0);
        border-radius: 10px;
        border: 2px solid #ffffff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 26px;
        color: #fff;
        image {
          width: 47px;
          height: 47px;
          margin-bottom: 8px;
        }
      }
    }
  }
  .search-box {
    background-color: #fff;
    padding: 19px 30px;
    .search {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 70px;
      border-radius: 10px;
      background: #f5f7fa;
      border-radius: 34px;
      border: 1px solid #e8ecf5;
      padding-left: 15px;
      .icon-search2 {
        color: #a2aec7;
        font-size: 30px;
        margin-right: 13px;
      }
      input {
        width: 100%;
        height: 100%;
        padding-left: 10px;
        box-sizing: border-box;
      }
    }
  }
  .section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 15px;
    box-sizing: border-box;
    background-color: #fff;
    margin-top: 5px;
    .li {
      color: #a2aec7;
      font-size: 30px;
    }
  }
  .detail {
    margin-top: 5px;
    .label {
      padding: 20px 30px;
      font-size: 30px;
      color: #000;
    }
    .li {
      padding: 20px 20px;
      border-bottom: 1px solid #e8ecf5;
      box-sizing: border-box;
      background-color: #fff;

      .title {
        position: relative;
        display: flex;
        align-items: center;
        font-size: 32px;
        color: #204eca;
        font-weight: 700;
        margin-bottom: 35px;
        .lebel {
          position: absolute;
          right: 120px;
          font-size: 24px;
          color: #333;
          display: inline-block;
          transform: translateY(-2px);
          font-weight: normal;
        }
        .icon-s {
          width: 32px;
          height: 32px;
          vertical-align: middle;
          margin-right: 10px;
        }
      }
      .box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 26px;
        margin-bottom: 15px;
        .name {
          display: flex;
          align-items: center;
          .to {
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            padding: 1px 8px;
            border-radius: 20px;
            color: #0072ff;
            font-size: 22px;
            border: 1px solid #0072ff;
            margin-right: 15px;
          }
          .at-checkbox {
            &::after {
              content: none;
            }
            &::before {
              content: none;
            }
          }
          .at-checkbox__title {
            font-size: 28px;
            color: #204eca;
            // margin-left: 40px;
            font-size: 32px;
            font-weight: 700;
          }
          .at-checkbox__option-wrap {
            padding: 0;
          }
          .at-checkbox__option {
            padding-left: 0;
          }
          .at-checkbox__option-cnt {
            background-color: #fff !important;
            align-items: center;

            &:active {
              background-color: #fff;
            }
          }

          .at-checkbox__option--selected {
            .at-checkbox__icon-cnt {
              background-color: #5bc650;
            }
          }

          .at-checkbox__icon-cnt {
            // width: 25px;
            // height: 25px;
            // min-width: 10px;
            margin-right: 10px;
          }
          .icon-dianbiao_shiti {
            font-size: 26px;
            color: #6f7c9a;
            margin-right: 10px;
            flex-shrink: 0;
          }
          .sn {
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 250px;
            white-space: nowrap;
          }
        }
        .desc {
          .icon-setting {
            font-size: 26px;
            margin-right: 10px;
            color: #333;
          }
        }
        .action {
          text {
            &:nth-child(1) {
              margin-right: 8px;
            }
          }
        }
      }
    }
  }
}
</style>
