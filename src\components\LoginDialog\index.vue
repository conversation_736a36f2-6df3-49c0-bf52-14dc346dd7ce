<template>
  <view class="login-popup" v-show="show">
    <view class="login-popup-mark"></view>
    <view class="login-popup-content login-popup-position-bottom" :style="{paddingBottom:`${ /(iPhone|iPad|iPod|iOS)/i.test(globalStore.systemInfoSync.system)? `${((globalStore.systemInfoSync.screenHeight - globalStore.systemInfoSync.safeArea.bottom) * 2)+180}rpx` : '180'}rpx`}">
      <view class="login-popup-title"><MyIcon icon="mp_login_ban" width="750rpx" height="398rpx"></MyIcon></view>
      <view class="p36 privacy-flex">
        <MyIcon icon="icon-radio" width="32rpx" height="32rpx" v-if="!privacy" @tap="onChangePrivacy"></MyIcon>
        <MyIcon icon="icon-radio-checked" width="32rpx" height="32rpx" v-if="privacy" @tap="onChangePrivacy"></MyIcon>
        <view class="privacy-text" @tap="onChangePrivacy">阅读并同意</view> <view class="link" @tap="onJump('/pages/article/article?key=yonghuxieyi')">用户手册</view> 和
        <view class="link" @tap="onJump('/pages/article/article?key=yinsizhengce')">隐私政策</view>
      </view>
      <view class="p36">
        <button v-if="!privacy" class="login-btn-wx" @tap="onHandleGetPhone" :class="{ap:isAp}">一键登录</button>
        <button v-if="privacy && !isAp" class="login-btn-wx" open-type="getPhoneNumber" @getphonenumber="onGetPhone">一键登录</button>
        <button
        open-type="getAuthorize"
        v-if="isAp && privacy"
        class="login-btn-wx ap"
        scope="phoneNumber"
        @GetAuthorize="onGetPhone"
        onError="handleAuthError"
      >
      一键登录
      </button>
      </view>
      <view class="p36 login2-link" @tap="onPasswordLogin">
        输入手机号码登录/注册
      </view>
      <view class="ld-btn-close">
        <MyIcon icon="icon-close" width="62rpx" height="62rpx" @tap="onClose"></MyIcon>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref } from "vue";
import MyIcon from '../MyIcon'
import Taro from "@tarojs/taro";
import request from '@/utils/request'

import { active, role } from "@/utils/tabActive";

const info =  Taro.getSystemInfoSync()
const isAp = process.env.TARO_ENV === 'alipay'
const isIos = info.platform === "iOS"

const safeAreaBottom = ref(0)



import { useGlobalStore } from '@/stores'
const globalStore = useGlobalStore()

const props = defineProps({
  position: {
    type: String,
    required: false,
    default: 'bottom'
  },
  show: {
    type: Boolean,
    required: false,
    default: false
  },
  title: {
    type: String,
    required: false,
    default: ''
  }
})

const privacy = ref(false)
const onChangePrivacy = () => {
  privacy.value = !privacy.value
}

const emit = defineEmits(['close'])

const onClose = () => {
  emit('close')
}

const onJump = (path) => {
  Taro.navigateTo({
    url: path
  })
}

const onGetPhone = (res) => {
  if (process.env.TARO_ENV === 'weapp') {
    if (!res.detail.code) {
      return
    }
    request.post({
      url: 'user/mpReg',
      data: {
        code: res.detail.code,
        who: globalStore.who
      }
    }).then(res => {
      Taro.setStorageSync('token', res.data.token)
      globalStore.setUserInfo(res.data)
      globalStore.homeHasChange = true
          // 判断缓存是否有设备 有设备去绑定
      // 接口存进登录态
      if(!globalStore.tempDeviceList[globalStore.tempDeviceList.length - 1]?.sn) return updateOpenId()
      request.get({
          url: 'user/bindDevice',
          data: {
            sn:globalStore.tempDeviceList[globalStore.tempDeviceList.length - 1]?.sn
          }
        }).then((_res) => {
        }).finally(() => {
          updateOpenId()
        })
    })
  } else {
      my.getSetting({
        withSubscriptions: true,
        success: res => {
          const { album = false, phoneNumber = false } = res.authSetting;
          // my.alert({ content: `相册已授权：${album}；手机已授权：${phoneNumber}` });
          if (phoneNumber) {
            my.getPhoneNumber({
              success: (res) => {
                let encryptedData = res.response;
                console.log(encryptedData);
                const {response, sign} = JSON.parse(encryptedData)
                console.log(response, sign);
                request.post({
                  url: 'user/aliLogin',
                  data: {
                    response
                  }
                }).then(res => {
                  Taro.setStorageSync('token', res.data.token)
                  globalStore.setUserInfo(res.data)
                  globalStore.homeHasChange = true
                      // 判断缓存是否有设备 有设备去绑定
                  // 接口存进登录态
                  if(!globalStore.tempDeviceList[globalStore.tempDeviceList.length - 1]?.sn) return updateOpenId()
                  request.get({
                      url: 'user/bindDevice',
                      data: {
                        sn:globalStore.tempDeviceList[globalStore.tempDeviceList.length - 1]?.sn
                      }
                    }).then((_res) => {
                    }).finally(() => {
                      updateOpenId()
                    })
                })
              },
              fail: (res) => {
                console.log(res);
              },
            });
          } else {
            Taro.showToast({
              title: '请授权手机号',
              icon: 'none'
            });
            // my.openSetting({
            //   success: (res) => {
            //     console.log(res);
            //   },
            //   fail: (res) => {
            //     console.log(res);
            //   },
            // });
          }
        },
        fail: (res) => {
          console.log(res);
          console.log('失败');
        },
      });
  }
}

const updateOpenId = () => {
  if (isAp) {
    emit('login')
    // active.value = 0
    return
  }
  wx.login({
    success(res) {
      if (res.code) {
        request.post({
          url: 'user/updateOpenId',
          data: {
            code: res.code
          }
        }).then(_ => {
          emit('login')
        //  active.value = 0
        })
      }
    },
    fail() {
      Taro.showToast({
        title: '获取openid出错',
        icon: 'error'
      })
    }
  })
}

const onPasswordLogin = () => {
  if (!privacy.value) {
    onHandleGetPhone()
    return
  }
  emit('close')
  onJump('/pages/login/login')
  active.value = 0
}

const onHandleGetPhone = () => {
  Taro.showModal({
    title: '温馨提示',
    content: '阅读并同意闪租婆《用户手册》和《隐 私政策》',
    cancelText: '不同意',
    confirmText: '同意',
    success: function (res) {
      if (res.confirm) {
        privacy.value = true
      } else if (res.cancel) {
        console.log('用户点击取消')
      }
    }
  })
}

</script>
<style lang="scss">
.login-popup {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 3;
}
.login-popup-mark {
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0, 0.6);
  z-index: 99998;
  position: fixed;
}
.login-popup-title {
  font-size: 36px;
  font-family: OPPOSans;
  font-weight: 500;
  color: #000000;
  text-align: center;
  padding:  30px 0;
}
.login-popup-content {
  width: 100%;
  background: #FFFFFF;
  z-index: 9999999999999999;
  position: fixed;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 120px);
  padding-bottom: calc(env(safe-area-inset-bottom) + 120px);
}
.login-popup-position-bottom {
  bottom: 0;
  border-radius: 30px 30px 0 0;
}
.ld-btn-close {
  position: absolute;
  top: 33px;
  right: 34px;
}
.p36 {
  padding: 36px;
}
.privacy-flex {
  color: #B6BEC5;
  font-size: 32px;
  display: flex;
  flex: 1;
}
.privacy-text {
  margin-left: 10px;
}
.link {
  color: #6B8ADF;
}
.login-btn-wx {
  width: 100%;
  height: 90px;
  background: #18AC18;
  border-radius: 6px;
  color: #FFFFFF;
  text-align: center;
  &.ap {
  background: #226bf3;
  }
}
.login2-link {
  font-size: 26px;
  font-weight: 500;
  color: #A1A1A1;
  text-align: center;
}
</style>
