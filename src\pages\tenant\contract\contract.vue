<!--
 * @Autor: lisong
 * @Date: 2023-08-05 15:10:26
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-13 15:24:12
-->
<template>
  <MySearch placeholder="关健词搜索" @search="onSearch"></MySearch>
  <view class="list">
    <view
      class="list-item"
      v-for="item in items"
      :key="item.id"
      @tap="onDetail(item)"
    >
      <view class="list-item_title">
        <text>{{ item.sn }}</text>
        <view
          class="list-item_status house-status-loading"
          v-if="item.status == 1"
          >签订中</view
        >
        <view
          class="list-item_status house-status-loading"
          v-if="item.status == 10"
          >待签约</view
        >
        <view class="list-item_status" v-if="item.status == 20">正常</view>
        <view
          class="list-item_status house-status-signing"
          v-if="item.status == 30"
          >已退房</view
        >
        <view
          class="list-item_status house-status-signing"
          v-if="item.status == 20 && item.expire_status == 1"
          >7天内到期</view
        >
        <view
          class="list-item_status house-status-signing"
          v-if="item.status == 20 && item.expire_status == 2"
          >已到期</view
        >
      </view>
      <view class="list-item_content">
        <view class="list-item_name">
          <text>{{ item.house.estate_name }}</text>
          <view class="list-item_price" v-if="item.contract_rent"
            >{{ item.contract_rent.amount }}元 / 月</view
          >
        </view>
        <view class="list-item_address">{{ item.house.name }}</view>
        <view class="list-item_labels">
          <view class="list-item_label">{{
            item.house.is_whole ? "整租" : "合租"
          }}</view>
          <view class="list-item_label2" v-if="item.type == 1">纸质合同</view>
          <text class="list-item_yq" v-if="item.bills_count > 0"
            >逾期：{{ item.bills_count }}单</text
          >
        </view>
        <view class="list-item_time" v-if="item.expired_at">
          {{ item.start_at }} ~ {{ item.expired_at }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from "vue";
import MySearch from "@/components/MySearch";
import MyFilter from "@/components/MyFilter";
import request from "@/utils/request";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
definePageConfig({
  navigationBarTitleText: "我的合同",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});
//  constant

const onChangeFilter = (child, key) => {
  console.log(child, key);
  if (key === "sort") {
    const value = child.value;
    if (value == 1) {
      child.value = {
        sort_by: "start_at",
        sort_order: "desc",
        sort_value: "1",
      };
    }
    if (value == 2) {
      child.value = {
        sort_by: "start_at",
        sort_order: "asc",
        sort_value: "2",
      };
    }
    if (value == 3) {
      child.value = {
        sort_by: "expired_at",
        sort_order: "desc",
        sort_value: "3",
      };
    }
    Object.assign(params.value, child.value);
  } else {
    params.value[key] = child.value;
  }
  fetch();
};

const onSearch = (keyword) => {
  params.value.keyword = keyword;
  fetch();
};

const items = ref([]);

const params = ref({
  sort_by: "id",
  sort_order: "desc",
  keyword: ''
});

const fetch = () => {
  items.value = [];
  params.value.page = 1;
  getList();
};

const getList = () => {
  request
    .get({
      url: 'tenant/contract',
      data: {
        ...params.value,
      },
    })
    .then((res) => {
      items.value = res.data;
    });
};

useDidShow(() => {
  fetch();
});


const handleClick = () => {
  Taro.navigateTo({
    url: "/pages/contract/add/add?step=1",
  });
};

const onDetail = (item) => {
  Taro.navigateTo({
    url: "/pages/contract/detail/detail?id=" + item.id,
  });
};
</script>

<style lang="scss">
page {
  background: #f1f3f7;
}

.list {
  padding-bottom: 170rpx;
}

.list-item {
  width: 700rpx;
  background: #ffffff;
  border-radius: 14rpx;
  margin: 20rpx auto;
  padding-bottom: 38rpx;
  .list-item_title {
    height: 70rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16rpx 0 27rpx;
    border-bottom: 1rpx dashed #dee5f4;
    font-size: 24rpx;
    font-weight: 500;
    color: #a8b1ca;
  }
  .list-item_status {
    font-size: 26rpx;
    display: flex;
    align-items: center;
    color: #000000;
    &::after {
      content: "";
      width: 10rpx;
      height: 17rpx;
      background-color: #a8b1ca;
      clip-path: polygon(100% 50%, 0 0, 0 100%);
      margin-left: 10rpx;
    }
  }

  .house-status-signing {
    color: #ff4200;
  }

  .house-status-loading {
    color: #1352fd;
  }

  .list-item_content {
    padding: 27rpx 20rpx 0 30rpx;
    .list-item_name {
      font-size: 36rpx;
      font-weight: 500;
      color: #000000;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .list-item_price {
        font-size: 26rpx;
        font-family: OPPOSans;
        font-weight: 500;
        color: #a8b1ca;
      }
    }
    .list-item_address {
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .list-item_labels {
      display: flex;
      margin-top: 15rpx;
      align-items: center;
      .list-item_label {
        width: 110rpx;
        height: 34rpx;
        background: #e4ebff;
        border-radius: 6rpx;
        font-size: 22rpx;
        font-family: OPPOSans;
        font-weight: 400;
        color: #1352fd;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .list-item_label2 {
        width: 110rpx;
        height: 34rpx;
        background: #f9e8d6;
        border-radius: 6rpx;
        font-size: 22rpx;
        font-family: OPPOSans;
        font-weight: 400;
        color: #f3560a;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 15rpx;
      }

      .list-item_yq {
        margin-left: 15rpx;
        color: #ff4200;
        font-size: 22rpx;
        font-family: OPPOSans;
        font-weight: 400;
      }
    }
    .list-item_time {
      font-size: 26rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #a8b1ca;
      margin-top: 20rpx;
    }
  }
}

.list-bottom {
  width: 750rpx;
  height: 154rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(54, 69, 193, 0.24);
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  .list-bottom_btn {
    width: 700rpx;
    height: 88rpx;
    background: #1352fd;
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #ffffff;
  }
}
</style>
