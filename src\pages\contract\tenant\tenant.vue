<template>
  <view class="top">
    <view
      :class="[
        'top-item',
        {
          'top-item_select': form.type === item.status,
        },
      ]"
      v-for="item in tabList"
      :key="item.status"
      @tap="handleChange(item.status)"
    >
      {{ item.name }}
      <view class="top-line" v-if="form.type === item.status"></view>
    </view>
  </view>
  <template v-if="form.type === 1">
    <view class="photo-box">
      <view class="photo-list" @tap="handleUploadImage('id_card_image1')">
        <view class="photo-list_left">
          <view>头像面</view>
          <view class="photo-list_sub">上传您的证件头像面</view>
        </view>
        <image class="head" v-if="form.id_card_image1" :src="form['id_card_image1']"></image>
        <image class="head"  v-if="!form.id_card_image1" :src="defaultImg1"></image>
      </view>
      <view class="photo-list" @tap="handleUploadImage('id_card_image2')">
        <view class="photo-list_left">
          <view>国徽面</view>
          <view class="photo-list_sub">上传您的证件国徽面</view>
        </view>
        <image class="head" v-if="form.id_card_image2" :src="form['id_card_image2']"></image>
        <image class="head"  v-if="!form.id_card_image2" :src="defaultImg2"></image>
      </view>
    </view>
    <view class="list-box">
      <view class="list">
        <view class="list-label">联系电话</view>
        <input
          v-model="form.mobile"
          type="number"
          placeholder="请输入"
          class="list-right"
          maxlength="11"
        />
      </view>
      <view class="list">
        <view :class=" contractDetail?.type == 1 ? 'list-label-no-required' : 'list-label'">承租人（乙方）</view>
        <input
          v-model="form.name"
          type="type"
          placeholder="请输入"
          class="list-right"
        />
      </view>
      <view class="list">
        <view :class=" contractDetail?.type == 1 ? 'list-label-no-required' : 'list-label'">身份证号</view>
        <input
          v-model="form.id_no"
          type="idcard"
          placeholder="请输入"
          class="list-right"
        />
      </view>
    </view>
  </template>
  <template v-if="form.type === 2">
    <view class="list-box">
      <view class="list">
        <view class="list-label">承租人电话</view>
        <input
          v-model="form.mobile"
          type="number"
          placeholder="请输入"
          class="list-right"
          maxlength="11"
        />
      </view>
      <view class="list">
        <view class="list-label">企业名称</view>
        <input
          v-model="form.name"
          type="type"
          placeholder="请输入"
          class="list-right"
        />
      </view>
      <view class="list">
        <view class="list-label">证件</view>
        <view>社会统一代码</view>
      </view>
      <view class="list">
        <view class="list-label">代码</view>
        <input
          v-model="form.credit_no"
          type="type"
          placeholder="请输入"
          class="list-right"
        />
      </view>
      <view class="list" @tap="handleUploadImage('credit_image')">
        <view class="list-label">营业执照</view>
        <view class="list-more">{{
          form.credit_image ? "已上传" : "未上传"
        }}</view>
      </view>
    </view>
    <view class="list-box">
      <view class="list">
        <view class="list-label no-required">法人代表</view>
        <input
          v-model="form.legal_person"
          placeholder="请输入"
          class="list-right"
        />
      </view>
      <view class="list">
        <view class="list-label no-required">法人身份证号</view>
        <input
          v-model="form.id_no"
          type="idcard"
          placeholder="请输入"
          class="list-right"
        />
      </view>
      <view class="list">
        <view class="list-label no-required">联系电话</view>
        <input
          v-model="form.legal_mobile"
          placeholder="请输入"
          class="list-right"
          type="number"
        />
      </view>
    </view>
  </template>
  <MyBottomBtn btnTxt="确认保存" @click="handleSubmit"> </MyBottomBtn>
</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import MyBottomBtn from "@/components/MyBottomBtn/MyBottomBtn";
import upload from '@/utils/upload';
import {validation} from "@/utils/validation"
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
definePageConfig({
  navigationBarTitleText: "承租人信息",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

const tabList = [
  {
    name: "个人租赁",
    status: 1,
  },
  {
    name: "企业租赁",
    status: 2,
  },
];
const defaultImg1 =
  "https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/head.png";
const defaultImg2 =
  "https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/card.png";
const form = ref({
  type: 1,
  id_card_image1: '',
  id_card_image2: '',
  is_master: 1,
});

const contractDetail = ref();

useLoad((options) => {
  form.value.contract_id = options.contract_id;
  getContractDetail();
});

const getContractDetail = () => {
  request
    .get({
      url: "contract/" + form.value.contract_id,
    })
    .then((res) => {
      contractDetail.value = res.data;
      res.data.contract_tenant.map((tenant) => {
        if (tenant.is_master) {
          form.value = tenant;
        }
      });
    });
};

const handleChange = (type) => {
  form.value.type = type;
};

const handleUploadImage = (type) => {
  upload.chooseImage(1).then(images => {
    upload.uploadFile(images[0]).then(url => {
      console.log(url, 'uploaded')
      form.value[type] = url;
      if (type === "id_card_image1") {
        getIdCardInfo(url);
      }
    })
  })
};

const getIdCardInfo = (url) => {
  request
    .post({
      url: "ocr",
      data: {
        url: url,
      },
    })
    .then((res) => {
      if (res.data) {
        form.value.id_no = res.data.idNumber;
        form.value.name = res.data.name;
      } else {
        Taro.showToast({
          title: "查不到该身份证的信息，请重新上传",
          icon: "none",
          duration: 3000,
        });
      }
    });
};

const smsCountDown = ref(60);
const canSend = ref(true);
const handleSendSmsCode = () => {
  if (!form.value.mobile) {
    Taro.showToast({
      title: "请输入手机号码",
      icon: "none",
    });
    return;
  }
  request
    .post({
      url: "sendSmsCode",
      data: {
        mobile: form.value.mobile,
      },
    })
    .then((res) => {
      canSend.value = false;
      smsCountDown.value = 59;
      handleCountDown();
    });
};
const handleCountDown = () => {
  setTimeout(() => {
    if (smsCountDown.value <= 0) {
      return;
    }
    smsCountDown.value -= 1;
    this.handleCountDown();
  }, 1000);
};

const handleSubmit = () => {
  const data = form.value;
  let rules = {
    id_card_image1: {
      type: 'required',
      message: '请上传身份证头像面'
    },
    id_card_image2: {
      type: 'required',
      message: '请上传身份证国徽面'
    },
    name: {
      type: 'required',
      message: '请输入姓名'
    },
    mobile: {
      type: 'required',
      message: '请输入手机号'
    },
    id_no: {
      type: 'required',
      message: '请输入身份证号码'
    }
  }
  if (contractDetail.value && contractDetail.value.type == 1) {
    rules = {
      mobile: {
        type: 'required',
        message: '请输入手机号'
      },
    }
  }
  if (data.type === 2) {
    rules = {
      name: {
        type: 'required',
        message: '请输入企业名称'
      },
      credit_no: {
        type: 'required',
        message: '请输入统一社会信用代码'
      },
      credit_image: {
        type: 'required',
        message: '请上传营业执照副本照片'
      },
      legal_person: {
        type: 'required',
        message: '请输入法人姓名'
      },
      id_no: {
        type: 'required',
        message: '请输入法人身份证号码'
      },
      mobile: {
        type: 'required',
        message: '请输入联系电话'
      }
    }
  }
  
  validation(data, rules).then(() => {
    console.log('okoko');
    console.log(form.value);
    if (String(form.value.mobile).trim().length != 11) {
      return Taro.showToast({
        title: "请输入正确的手机号",
        icon: "none",
      });
    }
    request
    .post({
      url: "contract/updateTenant",
      data: {
        ...data,
      },
    })
    .then((res) => {
      Taro.navigateBack();
    });
  }).catch((err) => {
      Taro.showToast({
        title: err,
        icon: 'error'
      })
  })
 
};
</script>

<style lang="scss">
page {
  background: #f7f9ff;
}

.top {
  height: 100rpx;
  background-color: #1352fd;
  display: flex;
  align-items: center;
  .top-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    font-size: 34rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: rgba($color: #fff, $alpha: 0.3);
    height: 100%;
    position: relative;
  }
  .top-item_select {
    color: #fff;
  }
  .top-line {
    width: 31rpx;
    height: 7rpx;
    background: #f0f5fe;
    border-radius: 4rpx;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }
}

.photo-box {
  width: 750rpx;
  height: 626rpx;
  background: #ffffff;
  box-sizing: border-box;
  padding: 75rpx 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .photo-list {
    display: flex;
    align-items: center;
    justify-content: center;
    .photo-list_left {
      font-size: 30rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
      .photo-list_sub {
        font-size: 24rpx;
        font-family: OPPOSans;
        font-weight: 500;
        color: rgba(122, 131, 157, 0.3);
        margin-top: 10rpx;
      }
    }
    .head {
      width: 294rpx;
      height: 193rpx;
      margin-left: 82rpx;
    }
  }
}

.list-box {
  margin-top: 32rpx;
}

.list {
  height: 120rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #dfdfdf;
  padding: 0 40rpx;
  .list-label {
    font-size: 32rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
    flex-shrink: 0;
    &::before {
      content: "*";
      color: #ff1616;
      margin-right: 4rpx;
    }
  }
    .list-label-no-required {
    font-size: 32rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
    flex-shrink: 0;
  }
  .no-required {
    &::before {
      content: "";
    }
  }
  .list-right {
    font-size: 30rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000;
    text-align: right;
    flex: 1;
  }
  .list-more {
    padding-right: 30rpx;
    background: url("https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/list-more.png")
      right center no-repeat;
    background-size: 15rpx auto;
  }
}

.list-right_box {
  display: flex;
  align-items: center;
  flex: 1;
  .sendCode {
    margin-left: 40rpx;
  }
}
</style>
