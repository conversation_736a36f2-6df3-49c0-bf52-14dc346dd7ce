<script setup>
import { ref, unref } from "vue";
import emtyImg from "@/assets/pic/emty.png";
import MyPopup from "@/components/MyPopup";
import request from "@/utils/request";
import Taro, { useDidShow, useLoad, usePullDownRefresh } from "@tarojs/taro";
import { AtCheckbox } from "taro-ui-vue3";
const isMore = ref(false);

const crePopShow = ref(false);

const tagName = ref("");

const saveHandel = () => {
  if (checkedList.value.length == 0) {
    return Taro.showToast({ title: "请选择房源", icon: "none" });
  }
  request
    .post({
      url: "houseClass/batch",
      data: {
        id: curItem.value.id,
        houses: unref(checkedList),
      },
    })
    .then((res) => {
      console.log(res, "res");
      if (res.code != 200) return;
      Taro.showToast({
        title: "批量添加成功",
        icon: "none",
      });
      checkedList.value = [];
      getOneList();
      getHouseList();
    });
};

const checkedList = ref([]);

const checkboxOption = ref([{ label: "我是房源名字", value: "0" }]);

const curItem = ref(null);

const unhouseList = ref([]);

const copyunhouseList = ref([]);

const oneList = ref([]);

const search = ref("");

useLoad((options) => {
  console.log(options, "options");

  curItem.value = JSON.parse(options.item);
  getHouseList();
  getOneList();
});

const changeHandel = (e) => {
  console.log(val, "val");
  const val = e.detail.value;
  if (!val.trim()) {
    unhouseList.value = copyunhouseList.value;
    return;
  }
  unhouseList.value = copyunhouseList.value.filter((item) => {
    return item.name.indexOf(val) != -1;
  });
};

const handleChange = (val) => {
  const idx = checkedList.value.findIndex((item) => item == val);
  if (idx != -1) {
    checkedList.value.splice(idx, 1);
    console.log(checkedList.value, "arrset");
    return;
  }

  console.log(val);

  checkedList.value.push(val);

  const arr = new Set(checkedList.value);
  checkedList.value = Array.from(arr);

  console.log(checkedList.value, "arrset");
};

const getHouseList = async () => {
  request
    .get({
      url: "houseClass/getSelfHouses",
    })
    .then((res) => {
      console.log(res, "res");
      if (res.code != 200) return;
      unhouseList.value = res.data.filter((o) => o.device.length > 0);
      copyunhouseList.value = res.data.filter((o) => o.device.length > 0);
    });
};

const getOneList = () => {
  request
    .get({
      url: "houseClass/getOne",
      data: {
        id: curItem.value.id,
      },
    })
    .then((res) => {
      console.log(res, "res");
      if (res.code != 200) return;
      oneList.value = res.data;
    });
};

const addHandel = (item) => {
  console.log(item, "item");
  // Taro.showModal({
  //   title: "提示",
  //   content: "确定要添加吗？",
  //   success: (res) => {
  //     if (res.confirm) {
  request
    .post({
      url: "houseClass/batch",
      data: {
        id: curItem.value.id,
        houses: [item.id],
      },
    })
    .then((res) => {
      console.log(res, "res");
      if (res.code != 200) return;
      Taro.showToast({
        title: "添加成功",
        icon: "none",
      });
      getOneList();
      getHouseList();
    });
  // } else {
  //   console.log("用户点击取消");
  // }
  // },
  // });
};

const delHandel = (item) => {
  console.log(item, "item");
  Taro.showModal({
    title: "提示",
    content: "确定要移除吗？",
    success: (res) => {
      if (res.confirm) {
        request
          .get({
            url: "houseClass/clearClass",
            data: {
              house_id: item.id,
            },
          })
          .then((res) => {
            console.log(res, "res");
            if (res.code != 200) return;
            Taro.showToast({
              title: "移除成功",
              icon: "none",
            });
            getOneList();
            getHouseList();
          });
      } else {
        console.log("用户点击取消");
      }
    },
  });
};

usePullDownRefresh(() => {
  getHouseList();
  getOneList();
  Taro.stopPullDownRefresh();
});
</script>

<template>
  <view class="house-tag-edit">
    <view class="house_name">{{ curItem?.label }}</view>

    <view class="emty-box" v-if="oneList.length == 0">
      <image :src="emtyImg" class="emty-img" mode="aspectFill"></image>
      <view>暂无数据</view>
    </view>
    <!-- 列表 -->
    <view class="list" v-if="oneList.length > 0">
      <view class="item">
        <view class="_bottom" v-for="item in oneList" :key="item.id">
          <view class="tit">
            <view>
              <image
                src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
                mode="scaleToFill"
                class="icon-s-1"
              />
              <text style="vertical-align: middle">{{ item?.name }}</text>
            </view>
            <view class="tag" @tap="delHandel(item)">-</view>
          </view>
          <!-- 电表 -->
          <view class="li" v-for="(sitem, idx) in item.device" :key="idx">
            <view class="le">
              <text v-if="sitem.type != 2" class="iconfont icon-dianbiao_shiti"></text>
              <text v-else class="iconfont icon-shuibiao"></text>
              <text
                :style="
                  sitem?.net_type == 1 && sitem?.signal_num <= 0
                    ? 'color: #98A6C3;'
                    : ''
                "
                >{{ sitem?.type == 2 ? "水表" : "电表" }}-{{
                  sitem?.sn
                }}</text
              >
            </view>
            <view
              class="ri"
              v-if="sitem?.signal_num > 0 && sitem?.net_type == 1"
            >
              在线
            </view>
            <view
              class="ri"
              style="color: #98a6c3"
              v-if="sitem?.net_type == 1 && sitem?.signal_num <= 0"
            >
              离线
            </view>
          </view>
          <!-- 门锁 -->
          <view
            class="li"
            v-for="(sitem, idx) in item.lock.filter(
              (o, i) => item.lock.findIndex((f) => o.lock_id == f.lock_id) == i
            )"
            :key="idx"
          >
            <view class="le">
              <text
                class="iconfont icon-a-jigouguanliduantubiao_huaban1fuben13"
                style="font-size: 35rpx; margin-left: -5rpx"
              ></text>
              <text>门锁-{{ sitem?.lock_id }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="add-box">
      <view class="house_name">以下是可选择加入的房源</view>

      <view class="input">
        <input
          type="text"
          placeholder="查询房源"
          v-model="search"
          @input="changeHandel"
        />
        <text class="iconfont icon-search"></text>
      </view>

      <view
        class="emty-box"
        v-if="unhouseList.length == 0"
        style="margin-top: 80rpx"
      >
        <image :src="emtyImg" class="emty-img" mode="aspectFill"></image>
        <view>暂无数据</view>
      </view>

      <!-- 列表 -->
      <view class="list" v-if="unhouseList.length > 0">
        <view class="item">
          <view class="_bottom" v-for="item in unhouseList" :key="item.id">
            <view class="tit">
              <at-checkbox
                :options="[{ label: item.name, value: item.id }]"
                :selectedList="checkedList"
                @change="handleChange(item.id)"
              />
              <image
                src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
                mode="scaleToFill"
                class="icon-s"
                @tap="handleChange(item.id)"
              />
              <view class="tag-2" @tap="addHandel(item)">+</view>
            </view>
            <!-- 电表 -->
            <view class="li" v-for="(sitem, idx) in item.device" :key="idx">
              <view class="le">
                <text class="iconfont icon-dianbiao_shiti"></text>
                <text
                  :style="
                    sitem?.net_type == 1 && sitem?.signal_num <= 0
                      ? 'color: #98A6C3;'
                      : ''
                  "
                  >{{ sitem?.net_type == 1 ? "电表" : "电表" }}-{{
                    sitem?.sn
                  }}</text
                >
              </view>
              <view
                class="ri"
                v-if="sitem?.signal_num > 0 && sitem?.net_type == 1"
              >
                在线
              </view>
              <view
                class="ri"
                style="color: #98a6c3"
                v-if="sitem?.net_type == 1 && sitem?.signal_num <= 0"
              >
                离线
              </view>
            </view>
            <!-- 门锁 -->
            <view
              class="li"
              v-for="(sitem, idx) in item.lock.filter(
                (o, i) =>
                  item.lock.findIndex((f) => o.lock_id == f.lock_id) == i
              )"
              :key="idx"
            >
              <view class="le">
                <text
                  class="iconfont icon-a-jigouguanliduantubiao_huaban1fuben13"
                  style="font-size: 35rpx; margin-left: -5rpx"
                ></text>
                <text>门锁-{{ sitem?.lock_id }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- utils -->
    <view class="footer-fixed" v-if="checkedList.length > 0">
      <button class="btn-add m33" @tap="saveHandel">保存</button>
    </view>

    <!-- 弹窗 -->
    <MyPopup :show="crePopShow" @close="crePopShow = false">
      <template #content>
        <view class="pop-content">
          <view class="pop-title">创建分组名称</view>
          <input placeholder="请输入分组名称" v-model="tagName" />
          <view class="utils">
            <view>取消</view>
            <button>确认创建</button>
          </view>
        </view>
      </template>
    </MyPopup>
  </view>
</template>

<style lang="scss">
@import "taro-ui-vue3/dist/style/components/checkbox.scss";
page {
  background: #f8f9ff;
}
.footer-fixed {
  animation: opacity 0.3s;
  @keyframes opacity {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}
.house-tag-edit,
.add-box {
  .house_name {
    padding: 15px 30px;
    font-size: 36rpx;
    font-weight: 700;
  }
  > .list {
    box-sizing: border-box;
    .icon-s {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      position: absolute;
      left: 62px;
      top: 20px;
    }
    .icon-s-1 {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 10px;
    }
    .item {
      width: 100%;
      min-height: 110px;
      box-sizing: border-box;
      background: #ffffff;
      padding: 20px;
      padding-top: 0;
      ._bottom {
        position: relative;
        padding-top: 15px;
        padding-bottom: 20px;
        animation: opacity 0.3s;
        border-bottom: 1px solid #d7dbe8;
        &:last-child {
          border: none;
        }
        @keyframes opacity {
          0% {
            opacity: 0;
          }
          100% {
            opacity: 1;
          }
        }
        .tit {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #204eca;
          margin-bottom: 37px;
          text {
            font-size: 32rpx;
            font-weight: 700;
          }
          font-size: 32px;
          font-weight: 700;
          .tag {
            // width: 63px;
            // height: 33px;
            // background: #f86811;
            // border-radius: 15px;
            // display: flex;
            // justify-content: center;
            // color: #fff;
            // align-items: center;
            // font-size: 22px;

            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 100px;
            height: 80%;
            background: red;
            border-radius: 15px;
            color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 55px;
            font-weight: 700;
            box-sizing: border-box;
          }
        }
        .li {
          display: flex;
          // justify-content: space-between;
          margin-bottom: 22px;
          font-size: 26px;
          .le {
            color: #090909;
            display: flex;
            align-items: center;
            margin-left: 10px;
            .iconfont {
              font-size: 24px;
              color: #6f7c9a;
              margin-right: 5px;
            }
          }
          .ri {
            width: 63px;
            height: 33px;
            color: #0072ff;
            margin-left: 20px;
          }
        }
      }
      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 15px;
        padding-top: 15px;
        .left {
          display: flex;
          align-items: center;
          width: 350px;
          font-size: 24px;
          .iconfont {
            font-size: 25px;
            margin-right: 7px;
            color: #000;
          }
          .title {
            margin-right: 10px;
            font-size: 34px;
          }
        }
        .right {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 148px;
          height: 38px;
          background: #8196d1;
          border-radius: 10px;
          transition: all 0.3s;
          .iconfont {
            margin-right: 10px;
            color: #ffffff;
          }
          view {
            font-size: 24rpx;
            color: #ffffff;
            line-height: 38px;
            text-align: center;
          }
        }
      }
    }
  }
  .emty-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    color: #999;
    transform: translateY(-10%);
    margin-top: 20px;
    .emty-img {
      width: 180px;
      height: 144px;
      margin: 0 auto;
    }
  }

  .pop-content {
    padding: 30px;
    .pop-title {
      font-size: 28px;
      color: #000;
      text-align: center;
      padding-bottom: 40px;
      margin-top: 20px;
    }
    input {
      width: 681px;
      height: 101px;
      background: rgba(87, 114, 150, 0);
      border-radius: 20px;
      border: 2px solid #e9e9e9;
      padding-left: 20px;
    }
    .utils {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      // border-top: 1px solid #E9E9E9;
      padding-top: 80px;
      view {
        width: 181px;
        height: 88px;
        border-radius: 20px;
        font-size: 28px;
        color: #000;
        text-align: center;
        line-height: 88px;
      }
      button {
        width: 460px;
        height: 88px;
        background: #1352fd;
        border-radius: 20px;
        font-size: 28px;
        color: #fff;
        text-align: center;
        line-height: 88px;
      }
    }
  }
}

.add-box {
  padding-bottom: 220px;

  .at-checkbox {
    &::after {
      content: none;
    }
    &::before {
      content: none;
    }
  }
  .at-checkbox__title {
    font-size: 28px;
    color: #204eca;
    margin-left: 40px;
    font-size: 32px;
    font-weight: 700;
  }
  .at-checkbox__option-wrap {
    padding: 0;
  }
  .at-checkbox__option {
    padding-left: 0;
  }
  .at-checkbox__option-cnt {
    background-color: #fff !important;
    align-items: center;

    &:active {
      background-color: #fff;
    }
  }

  .at-checkbox__icon-cnt {
    // width: 25px;
    // height: 25px;
    // min-width: 10px;
  }
  .input {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 90px;
    background: #ffffff;
    margin-bottom: 15px;
    padding: 0 20px;
    font-size: 28px;
    box-sizing: border-box;
    .iconfont {
      font-size: 30px;
    }
  }
  .tag-2 {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 100px;
    height: 80%;
    background: #1352fd;
    border-radius: 15px;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 37px;
    background-color: #2057f4 !important;
    font-size: 55px;
    font-weight: 700;
  }
}
</style>
