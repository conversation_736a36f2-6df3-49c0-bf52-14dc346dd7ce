<script setup>
const props = defineProps({
  title: String,
  confirmText: String,
  cancelText: String,
  showCancel: {
    type: Boolean,
    default: false,
  },
  show: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const emit = defineEmits(["confirm", "close"]);

const confirmHandel = () => {
  emit("confirm");
};
</script>

<template>
  <view class="y-modal-mask" v-if="props.show">
    <view class="y-modal-container">
      <view class="title"> {{ props.title }}</view>
      <view class="content">
        <slot name="content"></slot>
      </view>
      <view class="bottom-utils">
        <view class="confirm" @tap="confirmHandel"
          >{{ props.confirmText }}
        </view>
        <view class="cancel" v-if="props.showCancel"
          ><text @tap="emit('close')">{{
            props.cancelText || "取消"
          }}</text></view
        >
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.y-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999999999;
  .y-modal-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 550px;
    min-height: 320px;
    background-color: #fff;
    border-radius: 20px;
    color: #000;
    padding: 20px;
    z-index: 1000;
    box-sizing: border-box;
    animation: open 0.3s forwards;
    padding-bottom: 60px;
    @keyframes open {
      0% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }
    .title {
      text-align: center;
      font-size: 35px;
      color: #000;
    }
    .bottom-utils {
      .confirm {
        width: 50%;
        height: 80px;
        background-color: #2689fe;
        border-radius: 80px;
        text-align: center;
        line-height: 80px;
        color: #fff;
        font-size: 28px;
        margin: 0 auto;
        &:active {
          background-color: blue;
        }
      }
      .cancel {
        color: #999;
        font-size: 28px;
        text-align: center;
        margin-top: 15px;
      }
    }
  }
}
</style>
