<script setup>
import { ref } from "vue";
import Taro from "@tarojs/taro";
import request from "@/utils/request";
import { useGlobalStore } from "@/stores";

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
    default: false,
  },
  isInlineServe: {
    type: Boolean,
    default: true,
  },
  phone: {
    type: String,
    default: "",
  },
  title: {
    type: String,
    default: "客服热线",
  },
  content: {
    type: String,
    default: "",
  }
});

const detail = ref();

const globalStore = useGlobalStore();

const isAp = process.env.TARO_ENV === 'alipay'

const getDetail = () => {
  request
    .get({
      url: "article/detail?article_key=contact",
    })
    .then((res) => {
      detail.value = {
        title: res.data.title,
        content: res.data.content,
      };
    });
};
// getDetail();

const emit = defineEmits(["close"]);

const container = ref();

const maskClickHandel = (e) => {
  emit("close");
};

const openPhone = () => {
  Taro.makePhoneCall({
    phoneNumber: props.phone || globalStore?.userInfo?.agent?.mobile || "************", 
  });
};
</script>

<template>
  <view class="contant-mask" v-if="props.show" @tap.stop="maskClickHandel">
  </view>
  <view
    class="contant-container"
    v-if="props.show"
    ref="container"
    data-id="no-close"
  >
    <view class="title"> {{ props.title }} </view>
    <view class="phone">{{ props.phone || globalStore?.userInfo?.agent?.mobile }} </view>
    <view class="label"> {{ props.content }} </view>
    <view class="label">工作时间：周一至周日8:30-20:30</view>
    <view class="utils">
      <view class="left" @tap="openPhone"> 拨打电话 </view>
      <view class="right" v-if="!isAp && props.isInlineServe">
        <button open-type="contact" bindcontact="handleContact">
          在线客服
        </button>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.contant-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.contant-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600px;
  min-height: 350px;
  background-color: #fff;
  border-radius: 10px;
  color: #000;
  padding: 20px;
  padding-top: 40px;
  z-index: 1000;
  box-sizing: border-box;
  animation: open 0.3s forwards;
  padding-bottom: 50px;
  text-align: center;
  font-size: 25px;
  @keyframes open {
    0% {
      opacity: 0;
      transform: translate(-50%, -30%);
    }
    100% {
      opacity: 1;
      transform: translate(-50%, -50%);
    }
  }
  .title {
    font-weight: 700;
    font-size: 35px;
  }
  .phone {
    font-size: 50px;
    font-weight: 700;
    margin: 20px 0 25px 0;
  }
  .utils {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 30px;
    > view {
      width: 180px;
      height: 60px;
      line-height: 60px;
      text-align: center;
      border-radius: 15px;
      font-size: 25px;
      color: #fff;
    }
    .left {
      background: linear-gradient(to right, #1d8bfd, #2673f9);
      border: 1px solid #2673f9;
    }
    .right {
      border: 1px solid #2673f9;
      button {
        border: none;
        background-color: #fff;
        font-size: 25px;
        padding: 0;
        box-shadow: none;
        margin: 0;
        width: 180px;
        height: 60px;
        line-height: 60px;
        text-align: center;
        border-radius: 15px;
        color: #2673f9;
      }
    }
  }
}
</style>
