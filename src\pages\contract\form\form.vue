<!--
 * @Autor: lisong
 * @Date: 2023-08-12 20:58:39
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-13 09:17:30
-->
<template>
  <view class="list-box">
    <view class="list" @tap="handleUploadImage">
      <view class="list-label">合同拍照上传</view>
      <view class="list-right list-more">点击上传</view>
    </view>
  </view>
  <view
    class="flex text-center"
    style="background-color: #ffffff; padding: 20px"
    wx:if="{{type == 2}}"
  >
    <view
      class="img"
      style="width: 50%"
      v-for="(item, index) in form.contract_images"
      :key="index"
    >
      <image
        :src="item"
        mode="widthFix"
        style="width: 80%"
        @tap="onPreview(item)"
      ></image>
      <view class="btn-text mt10" @tap="onDel(index)">删除</view>
    </view>
  </view>
  <MyBottomBtn btnTxt="保存" @click="handleClick" />
</template>

<script setup>
//  constant
import { ref } from "vue";
import request from "@/utils/request";
import MyBottomBtn from "@/components/MyBottomBtn/MyBottomBtn";
import { addMonth } from "@/utils";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
import upload from "@/utils/upload";
definePageConfig({
  navigationBarTitleText: "合同设置",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

const form = ref({
  contract_images: [],
});

useLoad((options) => {
  form.value.id = options.contract_id;
  getContractDetail();
});

const getContractDetail = () => {
  request
    .get({
      url: "contract/" + form.value.id,
    })
    .then((res) => {
      form.value = {
        ...form.value,
        ...{
          water_meter: res.data.water_meter,
          electricity_meter: res.data.electricity_meter,
          gas_meter: res.data.gas_meter,
          contract_images: res.data.contract_images,
        },
      };
    });
};

const handleUploadImage = (e) => {
  upload.chooseImage(10).then(images => {
    images.map(img => {
      upload.uploadFile(img).then(url => {
        if (form.value.contract_images) {
          form.value.contract_images.push(url);
        } else {
          form.value.contract_images = [url];
        }
      })
    })
  })
};

const onPreview = (url) => {
  Taro.previewImage({
    current: "", // 当前显示图片的http链接
    urls: [url], // 需要预览的图片http链接列表
  });
};

const onDel = (index) => {
  form.value.contract_images.splice(index, 1);
};

const handleClick = () => {
  request
    .post({
      url: "contract/" + form.value.id + "/update",
      data: {
        ...form.value,
      },
    })
    .then((res) => {
      Taro.navigateBack();
    });
};
</script>

<style lang="scss">
page {
  background: #f7f9ff;
}
.list-box {
  margin-top: 32rpx;
}

.list {
  height: 120rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #dfdfdf;
  padding: 0 40rpx;
  .list-label {
    font-size: 32rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
  }
  .list-right {
    font-size: 30rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000;
    text-align: right;
  }
  .list-none {
    color: #b6bec5;
  }
  .list-more {
    padding-right: 30rpx;
    background: url("https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/list-more.png")
      right center no-repeat;
    background-size: 15rpx auto;
  }
}
</style>
