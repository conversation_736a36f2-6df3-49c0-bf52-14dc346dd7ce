<script setup>
import PageTop from "@/components/pageTop/index.vue";

import TabBarIndex from "@/components/customTabbar";

const iconHouse = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-house.png";
const iconContract = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-contract.png";
const iconBill = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-bill.png";
const iconBaoxiu = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-baoxiu.png";

import { ref } from "vue";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useShareAppMessage,
} from "@tarojs/taro";
import request from "@/utils/request";
import { useGlobalStore } from "@/stores";
import { toFixed } from "@/utils";

const iconChart = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-chart.png";
const iconHelp = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-help.png";
const iconArrow1 = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-arrow1.png";

import LoginDialog from "@/components/LoginDialog";

if (process.env.TARO_ENV === 'alipay') {
  my.setNavigationBar({
    frontColor: '#ffffff',
    backgroundColor: '#1452fd',
  })
}

// Taro.hideTabBar();

const globalStore = useGlobalStore();

globalStore.getUserInfo();

const showLogin = ref(false);

const onLoginClose = () => {
  showLogin.value = false;
};
const onLogin = () => {
  showLogin.value = false;
  getHome();
};

useDidShow(() => {
  if (!globalStore.isLogin) {
    showLogin.value = true;
  } else {
    showLogin.value = false;
  }
})

const home = ref({
  contract: {
    willExpire: 0,
    expired: 0,
  },
  lastMonth: {
    bill: 0,
    paid: 0,
  },
});

const getHome = () => {
  return new Promise((reslove, reject) => {
    if (globalStore.who === "business" && globalStore.isLogin) {
      request
        .get({
          url: "index/business",
        })
        .then((res) => {
          home.value = res.data;
          reslove();
        })
        .catch((e) => {
          reject(e);
        });
    }
  });
};

const onJump = (path) => {
  if (!globalStore.isLogin) {
    // Taro.navigateTo({
    //   url: '/pages/login/login'
    // })
    showLogin.value = true;
    return;
  }
  showLogin.value = false;
  Taro.navigateTo({
    url: path,
  });
};

useLoad(() => {
  getHome();
});

usePullDownRefresh(async () => {
  console.log("onPullDownRefresh");
  await getHome();
  Taro.stopPullDownRefresh();
});

const onJumpPublic = (path) => {
  Taro.navigateTo({
    url: path,
  });
};
</script>

<template>
  <view class="chuzu-container">
    <PageTop title="出租管理"> </PageTop>
    <view class="top-box">
      <view class="left">
        <view>近30日内合同到期户数</view>
        <view class="weight">{{ home.contract.willExpire }}</view>
        <view
          >逾期<text class="num">{{ home.contract.expired }}</text
          >户</view
        >
      </view>
      <view class="border-b"></view>
      <view class="right">
        <view>近30日待收金额</view>
        <view class="weight">{{ home?.bill?.amount }}</view>
        <view
          >其中逾期<text class="num">{{ home?.bill?.failAmount }}</text
          >元</view
        >
      </view>
    </view>
    <view class="main-card">
      <view class="card">
        <view>
          <view class="num">{{ home.house?.hired }}</view>
          <view>已租/户</view>
        </view>
        <view>
          <view class="num">{{ home.house?.vacant }}</view>
          <view>空置/户</view>
        </view>
        <view>
          <view class="num">{{ home.house?.percent }}</view>
          <view>出租率/%</view>
        </view>
        <view>
          <view class="num">{{ home.house?.total }}</view>
          <view>全部/户</view>
        </view>
      </view>
      <view class="box2 flex">
        <!-- <view
          class="enter-chart flex flex-v-center"
          @tap="onJump('/pages/money/stat/stat')"
        >
          <view
            ><image class="icon-chart" :src="iconChart" mode="aspectFit"></image
          ></view>
          <view>数据统计</view>
        </view> -->
        <view
            class="enter-help flex flex-v-center flex-space-between"
            @tap="onJumpPublic('/pages/webview/article?key=fangdongzhinan')"
          >
            <view
              ><image class="icon-help" :src="iconHelp" mode="aspectFit"></image
            ></view>
            <view style="padding-right: 20px; color: red;"
              >使用教程 房东必看</view
            >
            <view
              ><image
                class="icon-arrow1"
                :src="iconArrow1"
                mode="aspectFit"
              ></image
            ></view>
          </view>
      </view>

      <view class="content-box">
        <view @tap="onJump('/pages/house/house')">
          <view class="pic">
            <image class="menu-img" :src="iconHouse" mode="widthFix"></image>
          </view>
          <view class="text">房源管理</view>
        </view>
        <view @tap="onJump('/pages/contract/contract')">
          <view class="pic">
            <image class="menu-img" :src="iconContract" mode="widthFix"></image>
          </view>
          <view class="text">合同管理</view>
        </view>
        <view @tap="onJump('/pages/house/bill/bill')">
          <view class="pic">
            <image class="menu-img" :src="iconBill" mode="widthFix"></image>
          </view>
          <view class="text">费用管理</view>
        </view>
        <view @tap="onJump('/pages/repair/repair')">
          <view class="pic">
            <image class="menu-img" :src="iconBaoxiu" mode="widthFix"></image>
          </view>
          <view class="text">在线报修</view>
        </view>
      </view>
    </view>
    <!-- <TabBarIndex /> -->
    <LoginDialog :show="showLogin" @close="onLoginClose" @login="onLogin">
    </LoginDialog>
  </view>
</template>

<style lang="scss">
.chuzu-container {
  // padding-top: 80px;
  padding-bottom: 170px;
  .top-box {
    background: url("https://yimits.oss-cn-beijing.aliyuncs.com/images/bg-house.png") no-repeat bottom right;
    background-size: 476px 363px;
    background-color: #1452fd;
    height: 380px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 26px;
    margin-top: -20px;
    .border-b {
      width: 1px;
      height: 21%;
      opacity: 0.8;
      margin: 0 57px;
      background-color: #fff;
    }
    .left,
    .right {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .weight {
        font-size: 48px;
        margin: 10px 0;
      }
      .num {
        font-size: 30px;
      }
    }
  }
  .box2 {
    .enter-chart {
      background-color: #ffe9e7;
      border-radius: 52px;
      padding: 16px 17px;
      width: 228px;
      font-size: 26px;
    }
    .enter-help {
      background-color: #EBEFFF;
      border-radius: 52px;
      padding: 8px 23px 6px 10px;
      width: 100%;
      font-size: 34px;
      margin-left: 12px;
      font-weight: 700;
    }
    .icon-chart {
      width: 72px;
      height: 72px;
      margin-right: 11px;
    }
    .icon-help {
      width: 90px;
      height: 90px;
    }
    .icon-arrow1 {
      width: 44px;
      height: 44px;
    }
  }
  .main-card {
    background-color: #fff;
    border-top-left-radius: 35px;
    border-top-right-radius: 35px;
    margin-top: -75px;
    padding: 28px;
    .card {
      width: 100%;
      height: 170px;
      background: #1f5bfd;
      box-shadow: 0px 4px 32px 0px rgba(54, 69, 193, 0.24);
      border-radius: 30px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-bottom: 20px;
      > view {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: 24px;
        .num {
          font-size: 48px;
        }
      }
    }
    .content-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 20px;

      .pic {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 328px;
        height: 230px;
        background: #ebefff;
        border-radius: 15px;
        image {
          width: 101px;
          height: 101px;
        }
      }
      > view {
        &:nth-of-type(2) {
          .pic {
            background: #fff4eb;
          }
        }
        &:nth-of-type(3) {
          .pic {
            background: #f8efff;
          }
        }
        &:nth-of-type(4) {
          .pic {
            background: #ffe9e8;
          }
        }
      }
      .text {
        text-align: center;
        font-weight: 500;
        font-size: 32px;
        color: #000000;
        margin: 20px 0;
      }
    }
  }
}
</style>
