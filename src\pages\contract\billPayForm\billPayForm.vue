<!--
 * @Autor: lisong
 * @Date: 2023-08-13 16:01:13
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-13 19:41:51
-->
<template>
  <view class="list">
    <view class="list-label">收款金额</view>
    <view class="list-content">
      <view class="list-amount">{{ bill.amount }}</view>
    </view>
  </view>
  <view class="list">
    <view class="list-label">收款时间</view>
    <picker
      class="picker"
      :value="form.paid_at"
      mode="date"
      @change="handleDateChange"
    >
      <view class="list-content list-more">{{ form.paid_at }}</view>
    </picker>
  </view>
  <view class="list">
    <view class="list-label">支付方式</view>
    <picker
      class="picker"
      :range="paymentList"
      range-key="label"
      @change="handlePayChange"
    >
      <view class="list-content list-more">{{ paymentName }}</view>
    </picker>
  </view>
  <textarea
    class="textarea"
    placeholder="请填写备注"
    v-model="form.note"
  ></textarea>
  <view class="bottom">
    <view class="bottom-title">上传凭证</view>
    <view class="bottom-list">
      <view v-if="!form.image" class="image" @tap="handleUploadImage">
        <image
          class="icon-add"
          src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/icon-image_add.png"
        ></image>
      </view>
      <view class="image" v-if="form.image">
        <image class="pic" @tap="onPreview" :src="form.image"></image>
        <image
          class="icon-del"
          @tap="handleDel"
          src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/icon-image_del.png"
        ></image>
      </view>
    </view>
  </view>
  <myBottomBtn btnTxt="确认提交" @click="onSubmit" />
</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import myBottomBtn from "@/components/MyBottomBtn/MyBottomBtn";
import { formatDate } from "@/utils";
import { getApiRoot } from "@/config";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
definePageConfig({
  navigationBarTitleText: "确认收款",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

let bill_id = 0;
useLoad((options) => {
  bill_id = options.id;
  getBillDetail();
});

const bill = ref({});
const getBillDetail = () => {
  request
    .get({
      url: "contract/bill/" + bill_id,
    })
    .then((res) => {
      bill.value = res.data;
    });
};

const paymentList = [
  { label: "线下收款-微信", value: 1 },
  { label: "线下收款-支付宝", value: 2 },
  { label: "线下收款-银行卡", value: 3 },
  { label: "线上收款", value: 4 },
];

const paymentName = ref("线下收款-微信");
const form = ref({
  payment: 1,
  note: "",
  image: "",
  paid_at: formatDate(new Date()),
});

const handlePayChange = (e) => {
  const index = e.detail.value;
  const obj = paymentList[index];
  form.value.payment = obj.value;
  paymentName.value = obj.label;
};

const handleDateChange = (e) => {
  form.value.paid_at = e.detail.value;
};

const handleDel = () => {
  form.value.image = "";
};

const onPreview = () => {
  Taro.previewImage({
    current: "", // 当前显示图片的http链接
    urls: [form.value.image], // 需要预览的图片http链接列表
  });
};

const handleUploadImage = () => {
  Taro.chooseMedia({
    count: 1,
    mediaType: ["image"],
    sourceType: ["album", "camera"],
    success: (res) => {
      const tempFilePaths = res.tempFiles;
      console.log(tempFilePaths);
      tempFilePaths.map((file) => {
        Taro.uploadFile({
          url: getApiRoot("upload/image"),
          header: { Token: Taro.getStorageSync("token") },
          filePath: file.tempFilePath,
          name: "file",
          formData: {},
          success: (res) => {
            const url = JSON.parse(res.data).data.url;
            form.value.image = url;
          },
        });
      });
    },
  });
};

const onSubmit = () => {
  request
    .post({
      url: "contract/bill/" + bill_id + "/paid",
      data: form.value,
    })
    .then((res) => {
      Taro.navigateBack();
    });
};
</script>

<style lang="scss">
page {
  background: #f7f9ff;
}

.list {
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 36rpx;
  background: #fff;
  border-bottom: 1rpx solid #dfdfdf;
  .list-label {
    font-size: 32rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
  }
  .list-content {
    font-size: 30rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
    display: flex;
    justify-content: flex-end;
    flex: 1;
  }
  .list-more {
    padding-right: 30rpx;
    background: url("https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/list-more.png")
      right center no-repeat;
    background-size: 15rpx auto;
  }
  .picker {
    flex: 1;
    display: flex;
    justify-content: flex-end;
  }
  .list-amount {
    font-size: 48rpx;
    &::before {
      content: "￥";
      font-size: 32rpx;
    }
  }
}

.textarea {
  width: 750rpx;
  height: 380rpx;
  background: #ffffff;
  padding: 36rpx;
  box-sizing: border-box;
  margin: 24rpx 0;
  font-size: 32rpx;
}

.bottom {
  width: 750rpx;
  height: 380rpx;
  background: #ffffff;
  background: #ffffff;
  padding: 36rpx;
  box-sizing: border-box;
  .bottom-title {
    line-height: 1;
    font-size: 32rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
  }
  .bottom-list {
    margin-top: 36rpx;
    display: flex;
    flex-wrap: wrap;
    .icon-add {
      width: 40rpx;
      height: 40rpx;
    }
    .icon-del {
      width: 43rpx;
      height: 44rpx;
      position: absolute;
      right: 0;
      top: 0;
    }
    .image {
      width: 147rpx;
      height: 147rpx;
      background: #e2e7f6;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      .pic {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
