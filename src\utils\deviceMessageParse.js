function hexToSignedInt(hexString) {
  // 将十六进制字符串转换为无符号整数
  let unsignedInt = parseInt(hexString, 16);
  let bitLength = hexString.length * 4; // 每个十六进制字符代表4个二进制位
  let maxUnsignedValue = Math.pow(2, bitLength);

  // 如果超过了有符号整数的最大值的一半，视为负数
  if (unsignedInt >= maxUnsignedValue / 2) {
      unsignedInt -= maxUnsignedValue;
  }

  return unsignedInt;
}
export const deviceMessage = (str) => {
  let obj = {
    speed: -1,
    du: -1,
    status: -1,
    total: -1,
    power: -1,
    event: ''
  }
  str = str.toUpperCase()
  let cmd = str.substring(0,2)
  obj.event = cmd
  // 充值回复
  if (cmd === '0A') {
    obj.du = parseInt(str.substring(6,14), 16)
  }
  // 调表回复
  if (cmd === '02') {
    obj.speed = parseInt(str.substring(6,14), 16)
  }
  // 拉闸合闸状态回复
  if (cmd === '0D') {
    obj.status = parseInt(str.substring(4,6), 16)
  }
  // 抄表查询
  if (cmd === '0C') {
    obj.du = parseInt(str.substring(4,12), 16)
    obj.total = parseInt(str.substring(12,20), 16)
    obj.power = parseInt(str.substring(20,28), 16)
    obj.status = parseInt(str.substring(28, 30), 16)
    // 如果du是负数，则表示是0
    if (hexToSignedInt(str.substring(4,12)) < 0) {
      obj.du = hexToSignedInt(str.substring(4,12)) || 0
    }
  }
  if (obj.du < 0) {
    obj.du = (obj.du / 1000).toFixed(2)
  }
  if (obj.du > 0) {
    obj.du = (obj.du / 1000).toFixed(2)
  }
  if (obj.total > 0) {
    obj.total = (obj.total / 1000).toFixed(2)
  }
  if (obj.power > 0) {
    obj.power = obj.power / 1000
  }

  return obj
}
