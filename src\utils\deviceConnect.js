import Taro from '@tarojs/taro'
import {ab2hex, ab2hexArr, int2Byte, int2hex, int2hex2, hexToArrayBuffer, waitForTrue, checkIsConnectRssi} from '@/utils/index'
import { useGlobalStore } from '@/stores'
import request from '@/utils/request'
import {deviceMessage} from '@/utils/deviceMessageParse'
const globalStore = useGlobalStore()
let timConnection = null

let countdown = null;
const coutTimes = 30//常量
let timeRet = null
let DEVICEID = '';
let isSearchIng = false//是否正在搜索
let autoContShow = false //是否自动连接设备
let isConnectSucess = false //是否连接成功

// 校验权限 微信版
const checkScope = () => {
  return new Promise((resolve, reject) => {
    console.log('getSetting')
    Taro.getSetting({
      success: function (res) {
        console.log('getSetting success', res)
        if (!res.authSetting['scope.bluetooth']) {
          Taro.authorize({
            scope: 'scope.bluetooth',
            success: () => {
              if (!res.authSetting['scope.userLocation']) {
                  Taro.authorize({
                    scope: 'scope.userLocation',
                    success: () => {
                      resolve()
                    }
                  })
              } else {
                resolve()
              }
            },
            fail: (e) => {
              console.log('authorize failed', e)
            }
          })
        } else {
          if (!res.authSetting['scope.userLocation']) {
              Taro.authorize({
                scope: 'scope.userLocation',
                success: () => {
                  resolve()
                },
                fail: (e) => {
                  console.log('authorize failed', e)
                }
              })
          } else {
            resolve()
          }
        }
      },
      fail: (e) => {
        console.log(e, 'getSetting failed')
        reject(e)
      }
    })
  })
}

const checkScopeAlipay = () => {
  const systemInfo = my.getSystemInfoSync()
  console.log(systemInfo, 'systemInfo')
  if (!systemInfo.locationEnabled) {
    Taro.showToast({
      title: '手机定位未打开',
      icon: 'none',
      duration: 2000
    })
    return
  }
  if (typeof systemInfo.bluetoothEnabled !== 'undefined' && !systemInfo.bluetoothEnabled) {
    Taro.showToast({
      title: '手机蓝牙未打开',
      icon: 'none',
      duration: 2000
    })
    return
  }
  return _checkScopeAlipay(systemInfo.locationAuthorized)
}

const _checkScopeAlipay = (locationAuthorized) => {
  return new Promise((resolve, reject) => {
    console.log('alipay getSetting')
    Taro.getSetting({
      success: function (res) {
        console.log('alipay getSetting success', res)
        if (!res.authSetting['bluetooth']) {
          my.showAuthGuide({
            authType: 'BLUETOOTH',
            success: () => {
              if (!locationAuthorized) {
                my.showAuthGuide({
                  authType: 'LBS',
                  success: () => {
                    resolve()
                  },
                  fail: (e) => {
                    console.log('authorize failed', e)
                    reject()
                  }
                })
              } else {
                resolve()
              }
            },
            fail: (e) => {
              console.log('authorize failed', e)
              reject()
            }
          })
        } else {
          if (!locationAuthorized) {
            my.showAuthGuide({
              authType: 'LBS',
              success: () => {
                resolve()
              },
              fail: (e) => {
                console.log('authorize failed', e)
                reject()
              }
            })
          } else {
            resolve()
          }
        }
      },
      fail: (e) => {
        console.log(e, 'getSetting failed')
        reject(e)
      }
    })
  })
}

// 开始搜索
const startDiscovery = (autoContennt) => {
  return new Promise((resolve, reject) => {
    console.log('beaconDiscovery weapp')
    // 已经连接了，则不再搜索
    if(deviceConnect.isConnected()) return 
    // 正在搜索中，则不再搜索
    if (isSearchIng) {
      console.log('正在搜索中直接监听是否连接成功');
      // 获取数据的函数
      function getData() {
        return isConnectSucess;
      }
      waitForTrue(getData).then(() => {
        console.log('监听到连接成功');
        resolve()
      })
      return
    }
    Taro.startBluetoothDevicesDiscovery({
      //services: ['FEE7'],
      allowDuplicatesKey: true,
      powerLevel: 'high',
      success: (res) => {
        console.log('startBluetoothDevicesDiscovery', res)
        isSearchIng = true
        onBluetoothDeviceFound(autoContennt).then(resolve).catch(reject)
      },
      fail: (res) => {
        isSearchIng = false
        // Taro.showToast({
        //   title: '未获取蓝牙功能权限',
        //   icon: 'none'
        // })
      }
    })
  })
}

// 打开蓝牙
const openAdapter = () => {
  return new Promise((resolve, reject) => {
    Taro.openBluetoothAdapter({
      complete: (res) => {
        console.log('************打开蓝牙*******');
        console.log(res);
        console.log('************打开蓝牙*******');
        Taro.getBluetoothAdapterState({
          complete: (res) => {
            console.log('************获取本机蓝牙适配器状态*******');
            console.log(res, 'getBluetoothAdapterState')
            console.log('************获取本机蓝牙适配器状态*******');
            if (res.discovering || (res.adapterState && res.adapterState.discovering)) {
              // Taro.stopBluetoothDevicesDiscovery({
              //   complete: (res) => {
                  console.log(res, 'stopBluetoothDevicesDiscovery')
                  resolve(0)
              //   }
              // })
            }
            if (res.available || (res.adapterState && res.adapterState.available)) {
              console.log('debug')
              resolve(0)
            }
            reject()
          }
        })
      }
    })
  })
}

/**
 * 连接蓝牙设备的处理函数
 */
const connectDevice = (deviceId) => {
  return new Promise((resolve, reject) => {
    createBLEConnection(deviceId).then(() => {
      Taro.getBLEMTU({
        deviceId: deviceId,
        writeType: 'write',
        success: (mtu) => {
          console.log(mtu,"mtu")
        }
      })
      // 监听断开事件
      onBleConnectionStateChange()
      // 获取服务ID
      getBLEDeviceServices(deviceId).then(serviceId => {
        // 获取特征ID
        getBLEDeviceCharacteristics(deviceId, serviceId).then((charIds) => {
          globalStore.onConnectedDevice(deviceConnect.mac, deviceId, serviceId, charIds[0], charIds[1])
          // 监听回调事件
          notifyBLECharacteristicValueChange(deviceId, serviceId, charIds[1])
          clearInterval(countdown);
          globalStore.setDeviceTime(coutTimes)
          globalStore.setGlobDialog({ show: false, type: '' })
          clearTimeout(timConnection)
          resolve()
        })
      })
    }).catch(err => {
      Taro.hideLoading()
      if (err.errno == 1509007) {
        console.log('---已经链接----');
        resolve()
        isConnectSucess = true
        clearTimeout(timConnection)
        clearInterval(countdown);
        globalStore.setDeviceTime(coutTimes)
        globalStore.setGlobDialog({ show: false, type: '' })
      } else {
        console.log('-----连接失败-----');
        clearInterval(countdown);
        globalStore.setDeviceTime(coutTimes)
        globalStore.setGlobDialog({ show: false, type: 'BleConnectionTip' })
        setTimeout(() => {
          globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
        },300)
        console.log(err);
        reject()
      }
    })
  })
}


// 监听搜索到设备事件
const onBluetoothDeviceFound = (autoContennt) => {
  return new Promise((resolve, reject) => {
    console.log('监听搜索到设备事件');
    Taro.onBluetoothAdapterStateChange(function (res) {
      console.log('adapterState changed, now is', res)
      if (!res.available) {
        console.log('蓝牙被关闭');
        isSearchIng = false
        reject()
      }
    })
    let linArr = []
    Taro.onBluetoothDeviceFound(res => {
      if (res.devices.map(item => item.name).join(", ")) {
        linArr.push(res.devices.map(item => item.name).join(", "))
        // console.log(`-----监听设备到名称: ${[...new Set(linArr)]}--长度:${new Set(linArr).size}---`);
      }
      res.devices.forEach(d => {
        if (!d || (!d.name && !d.localName)) {
          return
        }
        let prefix = d.name.substring(0, 5)
        //如果是ios
        if (Taro.getSystemInfoSync().platform === 'ios') {
          prefix = d.localName.substring(0, 5)
          console.log('iOS设备', d.localName, 'prefix', prefix);
        }
        if (prefix === 'iEMC-' || prefix === 'iYMC-') {
          let suffix = int2hex(d.name.substring(d.name.length - 8)).toUpperCase()
            if (Taro.getSystemInfoSync().platform === 'ios') {
              suffix = int2hex(d.localName.substring(d.localName.length - 8)).toUpperCase()
              console.log('iOS设备', d.localName, 'suffix', suffix);
            }
          let macSuffix = deviceConnect.mac.substring(12-suffix.length)
          // console.log(suffix, macSuffix)
          if (suffix === macSuffix) {
            console.log(d,suffix,macSuffix, '匹配到设备')
            DEVICEID = d.deviceId
            globalStore.setDeviceRssi(d.RSSI)
            console.log(globalStore.deviceRssi,"deviceRssi");
            if (autoContennt || autoContShow) {
              autoContShow = false
              connectDevice(d.deviceId).then(resolve).catch(reject)
            }
            Taro.stopBluetoothDevicesDiscovery({
              success: () => {
                console.log('停止搜索')
              }
            })
          }
        }
      })
    })
  })
}

const queryConnectionStatus = (mac) => {
  return new Promise((resolve, reject) => {
    if (globalStore.device.isConnected && globalStore.device.mac === mac) {
      resolve()
    }
    if (globalStore.device.isConnected && globalStore.device.mac !== mac) {
      Taro.closeBLEConnection({
        deviceId: globalStore.device.deviceId,
        success (res) {
          reject()
        }
      })
    } else {
      reject()
    }
  })
}


// 主动连接设备
const getConnection = (mac) => {
  clearTimeout(timConnection)
  clearInterval(countdown);
  globalStore.setDeviceTime(coutTimes)
  return new Promise((resolve, reject) => {
    queryConnectionStatus(mac).then(() => {
      resolve()
    }).catch(() => {
      console.log(DEVICEID,"DEVICEIDDEVICEIDDEVICEID");
      if (!DEVICEID) {
        console.log('没有设备重新匹配');
        // 这里需要搜索到设备后自动连接
        autoContShow = true
        deviceConnect.discovery(true).then(resolve).catch(reject)
      }
      globalStore.setGlobDialog({show:true,type:'TimeLoading'})
      Taro.hideLoading()

      countdown = setInterval(function() {
        // if(globalStore.deviceRssi !== null) {
        //   checkIsConnectRssi().catch(() => {
        //     console.log('失败失败是啊比');
        //     clearInterval(countdown);
        //     Taro.hideLoading()
        //     globalStore.setDeviceTime(coutTimes)
        //     globalStore.setGlobDialog({ show: false, type: 'TimeLoading' })
        //   })
        // }
        globalStore.setDeviceTime(globalStore.deviceConTime-1)
        if (globalStore.deviceConTime <= 0) {
          clearInterval(countdown);
          globalStore.setDeviceTime(coutTimes)
          Taro.hideLoading()
          globalStore.setGlobDialog({show:false,type:'TimeLoading'})
          globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
        }
      }, 1000);
      globalStore.setLoadingTime(countdown)

      // 30秒没查到提示用户重连
      timConnection = setTimeout(() => {
        Taro.hideLoading()
        reject()
        clearTimeout(timConnection)
        clearInterval(countdown);
        globalStore.setDeviceTime(coutTimes)
        globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
        // globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
        Taro.stopBluetoothDevicesDiscovery({
          success: () => {
            console.log('停止搜索')
          }
        })
      }, 1000 * coutTimes)

      if (DEVICEID) {
        connectDevice(DEVICEID).then(resolve).catch(reject)
      }
    })

  })
}

// 创建连接
const createBLEConnection = (deviceId) => {
  return new Promise((resolve, reject) => {
    Taro.createBLEConnection({
      deviceId, // 搜索到设备的 deviceId
      success: () => {
        setTimeout(() => {
          resolve()
        }, 100)
      },
      fail: (err) => {
        console.log('createBLEConnection fail', err)
        reject(err)
      }
    })
  })
}

// 监听连接断开事件
const onBleConnectionStateChange = () => {
  Taro.onBLEConnectionStateChange(function(res) {
    // 该方法回调中可以用于处理连接意外断开等异常情况
    console.log(`device ${res.deviceId} state has changed, connected: ${res.connected}`)
    if (!res.connected) {
      globalStore.onCloseDevice()
    }
  })
}

// 获取serviceId
const getBLEDeviceServices = (deviceId) => {
  return new Promise((resolve, reject) => {
    // 连接成功，获取服务
    Taro.getBLEDeviceServices({
      deviceId,
      success: (res) => {
        console.log('设备连接成功', res)
        isConnectSucess = true;
        Taro.hideLoading()
        if (process.env.TARO_ENV === 'weapp') {
          resolve(res.services[0].uuid)
        } else {
          resolve(res.services[res.services.length - 1].uuid)
        }
      },
      fail: (err) => {
        console.log('getBLEDeviceServices fail', err)
        reject(err)
      }
    })
  })
}

// 获取特征ID
const getBLEDeviceCharacteristics = (deviceId, serviceId) => {
  return new Promise((resolve, reject) => {
    // 获取特征ID
      Taro.getBLEDeviceCharacteristics({
        // 这里的 deviceId 需要已经通过 Taro.createBLEConnection 与对应设备建立链接
        deviceId,
        // 这里的 serviceId 需要在 Taro.getBLEDeviceServices 接口中获取
        serviceId,
        success: (res) => {
          console.log('device getBLEDeviceCharacteristics:', res.characteristics)
          let writeId = 0
          let notifyId = 0
          /*if (process.env.TARO_ENV === 'weapp') {
            resolve([res.characteristics[0].uuid, res.characteristics[1].uuid])
          } else {
            resolve([res.characteristics[0].characteristicId, res.characteristics[1].characteristicId])
          }*/
          if (process.env.TARO_ENV === 'weapp') {
            res.characteristics.map(ch => {
              console.log(ch, ch.properties.notify, ch.properties.write, 'ch')
              if (ch.properties.notify) {
                notifyId = ch.uuid
              }
              if (ch.properties.write) {
                writeId = ch.uuid
              }
            })
          } else {
            res.characteristics.map(ch => {
              console.log(ch, ch.properties.notify, ch.properties.write, 'ch')
              if (ch.properties.notify) {
                notifyId = ch.characteristicId
              }
              if (ch.properties.write) {
                writeId = ch.characteristicId
              }
            })
          }
          if (!writeId || !notifyId) {
            Taro.showToast({
              title: '特征值获取失败',
              icon: 'none'
            })
            reject(new Error('特征值获取失败'))
          }
          resolve([writeId, notifyId])
        },
        fail: (err) => {
          console.log('getBLEDeviceCharacteristics fail', err)
          reject(err)
        }
      })
  })
}

// 发送指定
const sendCmd = (buffer) => {
  console.log(ab2hex(buffer), 'cmd')
  return new Promise((resolve, reject) => {
    Taro.writeBLECharacteristicValue({
      // 这里的 deviceId 需要在 getBluetoothDevices 或 onBluetoothDeviceFound 接口中获取
      deviceId: globalStore.device.deviceId,
      // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
      serviceId: globalStore.device.serviceId,
      // 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
      characteristicId: globalStore.device.charId,
      // 这里的value是ArrayBuffer类型
      value: buffer,
      success (res) {
        console.log('writeBLECharacteristicValue success', res.errMsg)
        resolve()
      },
      fail (res) {
        Taro.showToast({
          title: '发送指令失败',
          icon: 'error'
        })
        console.log('发送指令失败', res)
        reject(res)
      }
    })
  })
}

// 启用特征变化通知
const notifyBLECharacteristicValueChange = (deviceId, serviceId, notifyId) => {
  console.log(deviceId, serviceId, notifyId, 1)
  Taro.notifyBLECharacteristicValueChange({
    state: true, // 启用 notify 功能
    deviceId: deviceId,
    serviceId: serviceId,
    characteristicId: notifyId,
    success: (res) => {
      console.log('notifyBLECharacteristicValueChange success', res.errMsg)
      onBLECharacteristicValueChange()
    },
    fail: (err) => {
      console.log('notifyBLECharacteristicValueChange fail', err)
    }
  })
}

// 监听蓝牙低功耗设备的特征值变化事件
const onBLECharacteristicValueChange = () => {
  Taro.offBLECharacteristicValueChange()
  if (process.env.TARO_ENV === 'weapp') {
    console.log('onBLECharacteristicValueChange')
    Taro.onBLECharacteristicValueChange((res) => {
      console.log(`characteristic ${res.characteristicId} has changed, now is ${res.value}`)
      console.log(ab2hex(res.value), 'valueChange')
      let msg = deviceMessage(ab2hex(res.value))
      msg.mac = globalStore.device.mac
      
      globalStore.onDeviceMessage(msg)
      console.log("监听触发了",globalStore.device.message);
    })
  } else {
    console.log('onBLECharacteristicValueChange')
    Taro.onBLECharacteristicValueChange((res) => {
      console.log(`characteristic ${res.characteristicId} has changed, now is ${res.value}`)
      let msg = deviceMessage(res.value)
      msg.mac = globalStore.device.mac
      globalStore.onDeviceMessage(msg)
    })
  }
}

// 4G设备操作类
const netClient = {
  start(mac) {
    return new Promise((resolve, reject) => {
      request.post({
        url: 'device/start',
        data: {
          mac
        }
      }).then(res => {
        Taro.showLoading({
          title: '正在合闸中...',
          mask: true
        })
        resolve()
      })
    })
  },
  stop(mac) {
    return new Promise((resolve, reject) => {
      request.post({
        url: 'device/stop',
        data: {
          mac
        }
      }).then(res => {
        /*Taro.showToast({
          title: '拉闸成功',
          icon: 'none'
        })*/
        Taro.showLoading({
          title: '正在拉闸中...',
          mask: true
        })
        resolve()
      })
    })
  },
  queryStatus(mac) {
    Taro.showToast({
      title: '4G电表数据实时更新，无需读表',
      icon: 'none'
    })
  },
  querySpeed(mac) {

    return new Promise((resolve, reject) => {
      request.post({
        url: 'device/querySpeed',
        data: {
          mac
        }
      }).then(res => {
        /*Taro.showToast({
          title: '拉闸成功',
          icon: 'none'
        })*/
        Taro.showLoading({
          title: '正在读取中...',
          mask: true
        })
        resolve()
      })
    })
  },
  speed(mac, sp,isSetting = false) {

    return new Promise((resolve, reject) => {
      request.post({
        url: 'device/speed',
        data: {
          mac,
          speed: sp * 1000
        }
      }).then(res => {
        if (isSetting) {
          Taro.showLoading({
            title: '正在设置中...',
            mask: true
          })
        } else {
          Taro.showLoading({
            title: '正在调表中...',
            mask: true
          })
        }
        resolve()
      })
    })
  },
  clear(mac) {

    return new Promise((resolve, reject) => {
      request.post({
        url: 'device/clear',
        data: {
          mac
        }
      }).then(res => {
        Taro.showLoading({
          title: '正在清零中...',
          mask: true
        })
        resolve()
      })
    })
  },
  recharge(mac, du, amount, isPaid, addBill = 1) {
    return new Promise((resolve, reject) => {
      request.post({
        url: 'device/recharge',
        data: {
          mac,
          du,
          amount,
          isPaid,
          addBill
        },
        showLoading: false
      }).then(res => {
        resolve()
      })
    })
  },
  // 只做充值不做其他记录，适用房客
  recharge2(mac, du, orderNo,isEdit=false) {
    console.log('77******',isEdit);
    return new Promise((resolve, reject) => {
      request.post({
        url: 'order/recharge',
        data: {
          orderNo: orderNo,
          type: isEdit ? 'edit' : 'add'
        },
        showLoading: false
      }).then(res => {
        resolve()
      }).catch(() => {
        reject()
      })
    })
  }
}

// 蓝牙设备操作类
const bleClient = {
  start(mac) {
    console.log('start', mac)
    return new Promise((resolve, reject) => {
      getConnection(mac).then(_=> {
        let buffer = new ArrayBuffer(2)
        let dataView = new DataView(buffer)
        dataView.setUint8(0, 0x0D)
        dataView.setUint8(1, 0x01)
        sendCmd(buffer).then(_=> {
          resolve()
        })
      }).catch(_=> {
        reject()
      })
    })
  },
  stop(mac) {
    return new Promise((resolve, reject) => {
      getConnection(mac).then(_=> {
        let buffer = new ArrayBuffer(2)
        let dataView = new DataView(buffer)
        dataView.setUint8(0, 0x0D)
        dataView.setUint8(1, 0x02)
        sendCmd(buffer).then(_=> {
          resolve()
        })
      }).catch(_=> {
        reject()
      })
    })
  },
  queryStatus(mac) {

    return new Promise((resolve, reject) => {
      getConnection(mac).then(_=> {
        let buffer = new ArrayBuffer(2)
        let dataView = new DataView(buffer)
        dataView.setUint8(0, 0x0C)
        sendCmd(buffer).then(_=> {
          resolve()
        })
      }).catch(_=> {
        reject()
      })
    })
  },
  querySpeed(mac) {

    return new Promise((resolve, reject) => {
      getConnection(mac).then(_=> {

        let hex = '02' + int2hex2(131, 2) + int2hex2(0, 8)

        sendCmd(hexToArrayBuffer(hex)).then(_=> {
          // Taro.showLoading({
          //   title: '正在读表中...',
          //   mask: true
          // })
          resolve()
        })
      }).catch(_=> {
        reject()
      })
    })
  },
  speed(mac, sp) {

    return new Promise((resolve, reject) => {
        getConnection(mac).then(_=> {

          Taro.showLoading({
            title: '正在设置中...',
            mask: true
          })
          let hex = '0203' + int2hex2(sp * 1000, 8)

          sendCmd(hexToArrayBuffer(hex)).then(_=> {
            hex = '02FE' + int2hex2(sp * 1000, 8)
            sendCmd(hexToArrayBuffer(hex)).then(_=> {
              resolve()
            })
          })
        }).catch(_=> {
          reject()
        })
    })
  },
   setTotalDu(mac, du) {
    return new Promise((resolve, reject) => {
        getConnection(mac).then(_=> {
          let hex = '0208' + int2hex2(du * 1000, 8)
          sendCmd(hexToArrayBuffer(hex)).then(_=> {
            hex = '02FE' + int2hex2(du * 1000, 8)
            sendCmd(hexToArrayBuffer(hex)).then(_=> {
              resolve()
            })
          })
        }).catch(_=> {
          reject()
        })
    })
  },
  clear(mac) {
    return new Promise((resolve, reject) => {
      getConnection(mac).then(_=> {
            let buffer = new ArrayBuffer(6)
            let dataView = new DataView(buffer)
            dataView.setUint8(0, 0x0A)
            dataView.setUint8(1, 0x03)

            let bytes = int2Byte(0, 4)
            for (let i = 2; i < 6; i ++) {
              dataView.setUint8(i, bytes[i - 2])
            }
            sendCmd(buffer).then(_=> {
              resolve()
            })
          }).catch(_=> {
            reject()
          })
    })
  },
  recharge(mac, du, amount, isPaid, addBill = 1) {
    return new Promise((resolve, reject) => {
      getConnection(mac).then(_=> {
        let hex = '0A01' + int2hex2(du * 1000, 8)
        sendCmd(hexToArrayBuffer(hex)).then(_=> {
          request.post({
            url: 'device/recharge',
            data: {
              mac,
              du,
              amount,
              isRecharged: 1,
              isPaid,
              addBill
            },
            showLoading: false
          }).then(res => {
            resolve()
          })
        }).catch(_=> {
          reject()
        })
      })
    })
  },
  // 只做充值不做其他记录，适用房客,isEdit:直接写入电量不是累加,默认false
  recharge2 (mac, du, orderNo,isEdit=false) {
    return new Promise((resolve, reject) => {
      if(globalStore.rechargeTime && (Date.now() - globalStore.rechargeTime) < 1000 * globalStore.rechargeTimeSecens) {
        console.log('***请勿频繁充值***');
        return reject()
      }
      getConnection(mac).then(_=> {
        let hex = '0A01' + int2hex2(du * 1000, 8)
        if (isEdit) {
          console.log('***直接写入电量不是累加***', du);
          hex = '0A03' + int2hex2(du * 1000, 8)
        } 
        sendCmd(hexToArrayBuffer(hex)).then(_=> {
          request.post({
            url: 'order/bleRecharge',
            data: {
              orderNo,
              description:'蓝牙表充值成功',
            },
            showLoading: false
          }).then(res => {
            // 存时间戳
            globalStore.setRechargeTime(Date.now())
            resolve()
          })
        })
      }).catch(_=> {
        reject()
      })
    })
  }
}

const deviceConnect = {
  client: null,
  mac: '',
  netType: 0,
  init(mac, netType) {
    console.log('device connect init', mac, netType)
    this.mac = mac
    this.netType = netType
    DEVICEID = ''
    isSearchIng = false
    autoContShow = false
    isConnectSucess = false
    if (this.netType !== 1) {
      Taro.stopBluetoothDevicesDiscovery()
      this.discovery()
      this.client = bleClient
    } else {
      this.client = netClient
    }
  },
  getConnection() {
    return getConnection(this.mac)
  },
  isConnected() {
    return globalStore.device.isConnected && globalStore.device.mac === this.mac
  },
  discovery(autoContennt) {
    return new Promise((resolve, reject) => {
      if (process.env.TARO_ENV === 'weapp') {
        checkScope().then(_=> {
          console.log('checkScope')
          openAdapter().then(status => {
            if (status === 1) {
              onBluetoothDeviceFound()
            } else {
              startDiscovery(autoContennt).then(resolve).catch(reject)
            }
          }).catch(_=> {
            globalStore.setGlobDialog({show:true,type:'PermissionDenied'})
          })
        })
      } else {
        checkScopeAlipay().then(_=> {
          console.log('checkScope')
          openAdapter().then(status => {
            if (status === 1) {
              onBluetoothDeviceFound()
            } else {
              startDiscovery(autoContennt).then(resolve).catch(reject)
            }
          }).catch(_=> {
            globalStore.setGlobDialog({show:true,type:'PermissionDenied'})
          })
        })
  
      }
    })

  },
  start() {
    return this.client.start(this.mac)
  },
  stop() {
    return this.client.stop(this.mac)
  },
  speed(speed,isSetting) {
    return this.client.speed(this.mac, speed,isSetting)
  },
  setTotalDu(du) {
    return this.client.setTotalDu(this.mac, du)
  },
  clear() {
    return this.client.clear(this.mac)
  },
  // 房东充值 addBill参数，1=记录账单， 0=不记录账单
  recharge(du, amount, isPaid, addBill) {
    return this.client.recharge(this.mac, du, amount, isPaid, addBill)
  },
  // 房客充值 
  recharge2(du, orderNo,isEdit=false) {
    return new Promise((resolve, reject) => {
      console.log('999***');
      this.client.recharge2(this.mac, du, orderNo, isEdit)
        .then(() => {
          resolve()
        }).catch(() => {
          reject()
          console.log('error***');
      })
    })
  },
  queryStatus() {
    return new Promise((resolve, reject) => {
      this.client.queryStatus(this.mac)
        .then(() => {
          resolve()
        }).catch(() => {
          reject()
      })
    })
  },
  querySpeed() {
    return this.client.querySpeed(this.mac)
  },
  closeConnection() {
    if (globalStore.device.isConnected) {
      Taro.closeBLEConnection({
        deviceId: globalStore.device.deviceId,
        success (res) {
          console.log('device closed', res)
        }
      })
    }
  }
}

export default deviceConnect
