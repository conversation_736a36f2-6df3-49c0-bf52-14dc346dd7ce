<!--
 * @Autor: lisong
 * @Date: 2023-08-13 15:09:06
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-13 20:27:24
-->
<template>
  <MySearch placeholder="关健词搜索" @search="onSearch"></MySearch>
  <view class="list">
    <view
      class="list-item"
      v-for="item in items"
      :key="item.id"
      @tap="onDetail(item.id)"
    >
      <view class="list-item_title">
        <text>{{ item.sn }}</text>
        <view class="list-item_status"></view>
      </view>
      <view class="list-item_content">
        <view class="list-item_name">
          <text>{{ item.house.estate_name }}</text>
          <view class="list-item_price" v-if="item.contract_rent"
            >{{ item.contract_rent.amount }}元 / 月</view
          >
        </view>
        <view class="list-item_address">{{ item.house.name }}</view>
        <view class="list-item_labels">
          <view class="list-item_label">{{
            item.house.is_whole ? "整租" : "合租"
          }}</view>
          <view class="list-item_label2" v-if="item.type == 1">纸质合同</view>
        </view>
        <view class="list-item_time" v-if="item.expired_at">
          {{ item.start_at }} ~ {{ item.expired_at }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from "vue";
import MySearch from "@/components/MySearch";
import request from "@/utils/request";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
definePageConfig({
  navigationBarTitleText: "费用管理",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

useDidShow(() => {
  fetch();
});

useReachBottom(() => {
  if (total.value > items.value.length) {
    params.value.page += 1;
    getList();
  }
});

const items = ref([]);
const total = ref(0);
const params = ref({
  page: 1,
  status: 20,
  sort_by: "start_at",
  sort_order: "desc",
  keyword: "",
});

const fetch = () => {
  items.value = [];
  params.value.page = 1;
  getList();
};
const getList = () => {
  request
    .get({
      url: "contract",
      data: {
        ...params.value,
      },
    })
    .then((res) => {
      items.value = [...items.value, ...res.data.items];
      total.value = res.data.total;
    });
};

const onSearch = (keyword) => {
  params.value.keyword = keyword;
  fetch();
};

const onDetail = (id) => {
  Taro.navigateTo({
    url: "/pages/contract/bill/bill?contract_id=" + id,
  });
};
</script>

<style lang="scss">
page {
  background: #f1f3f7;
}
.list {
  padding-bottom: 170rpx;
}

.list-item {
  width: 700rpx;
  background: #ffffff;
  border-radius: 14rpx;
  margin: 20rpx auto;
  padding-bottom: 38rpx;
  .list-item_title {
    height: 70rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16rpx 0 27rpx;
    border-bottom: 1rpx dashed #dee5f4;
    font-size: 24rpx;
    font-weight: 500;
    color: #a8b1ca;
  }
  .list-item_status {
    font-size: 26rpx;
    display: flex;
    align-items: center;
    color: #000000;
    &::after {
      content: "";
      width: 10rpx;
      height: 17rpx;
      background-color: #a8b1ca;
      clip-path: polygon(100% 50%, 0 0, 0 100%);
      margin-left: 10rpx;
    }
  }

  .house-status-signing {
    color: #ff4200;
  }

  .house-status-loading {
    color: #1352fd;
  }

  .list-item_content {
    padding: 27rpx 20rpx 0 30rpx;
    .list-item_name {
      font-size: 36rpx;
      font-weight: 500;
      color: #000000;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .list-item_price {
        font-size: 26rpx;
        font-family: OPPOSans;
        font-weight: 500;
        color: #a8b1ca;
      }
    }
    .list-item_address {
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .list-item_labels {
      display: flex;
      margin-top: 15rpx;
      align-items: center;
      .list-item_label {
        width: 110rpx;
        height: 34rpx;
        background: #e4ebff;
        border-radius: 6rpx;
        font-size: 22rpx;
        font-family: OPPOSans;
        font-weight: 400;
        color: #1352fd;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .list-item_label2 {
        width: 110rpx;
        height: 34rpx;
        background: #f9e8d6;
        border-radius: 6rpx;
        font-size: 22rpx;
        font-family: OPPOSans;
        font-weight: 400;
        color: #f3560a;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 15rpx;
      }

      .list-item_yq {
        margin-left: 15rpx;
        color: #ff4200;
        font-size: 22rpx;
        font-family: OPPOSans;
        font-weight: 400;
      }
    }
    .list-item_time {
      font-size: 26rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #a8b1ca;
      margin-top: 20rpx;
    }
  }
}
</style>
