<template>
  <view class="toast-contaoner" v-show="show">
    <view
      class="content"
      :style="{
        width: `${width}rpx`,
        minHeight: `${height}rpx`,
        padding: `${padding}`,
        margin: margin,
      }"
    >
      <!-- <image v-if="type === 'succ'" src="@/static/image/sucess.png" mode="aspectFill"></image>
			<image v-else-if="type === 'loading'" class="loading" src="@/static/image/loading.png" mode="aspectFill">
			</image> -->
      <view v-if="props.sencondText" style="font-size: 65rpx;margin-bottom: 20rpx">{{ props.sencondText }}</view>
      <text v-if="type === 'succ'">{{ props.text }}</text>
      <text v-if="type === 'loading'">{{ props.text }}</text>
      <text v-if="type === 'text'">{{ props.text }}</text>
    </view>
  </view>
</template>

<script setup>
import { onMounted, ref, onUnmounted, watch } from "vue";
const props = defineProps({
  text: {
    type: String,
    default: "�ύ��",
  },
  type: {
    type: String,
    default: "loading",
  },
  show: {
    type: Boolean,
    default: false,
  },
  width: {
    type: String,
    default: "200",
  },
  height: {
    type: String,
    default: "200",
  },
  padding: {
    type: String,
    default: "0",
  },
  margin: {
    type: String,
    default: "0",
  },
  sencondText: {
    type: Number,
    default: 0,
  },
  closeSencond: {
    type: Number,
    default: 5,
  }
});

const emit = defineEmits(["close"]);

const time = ref(null)

watch(() => props.show, (v) => {
  if (v) {
     time.value = setTimeout(() => {
      emit("close");
    }, 1000 * (props.closeSencond || 5));
  } else {
    emit("close");
  }
})


onUnmounted(() => {
  clearTimeout(time.value);
});
</script>

<style lang="less">
.toast-contaoner {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999999999;
  animation: opt 0.3s forwards;
  background-color: rgba(0, 0, 0, 0.35);

  @keyframes opt {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  .content {
    position: relative;
    z-index: 99999999999;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 200rpx;
    min-height: 200rpx;
    background: rgba(0, 0, 0, 0.75);
    border-radius: 20rpx;
    color: #fff;
    text-align: center;
    font-size: 34px;
    image {
      width: 55rpx;
      height: 55rpx;
      margin-bottom: 15rpx;
    }

    .loading {
      animation: routeA 1s infinite;

      @keyframes routeA {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }
    }
  }
}
</style>
