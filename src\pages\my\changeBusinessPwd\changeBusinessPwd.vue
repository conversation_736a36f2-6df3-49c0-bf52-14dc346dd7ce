<template>
  <View class="container">
    <view>
      <MyCell prefix="手机号">
        <template #content
          ><input
            v-model="formState.mobile"
            type="number"
            :disabled="true"
            placeholder="请输入手机号"
        /></template>
      </MyCell>
      <MyCell prefix="验证码">
        <template #content>
          <view class="code-ipt">
            <input
              class="my-input-m"
              v-model="formState.sms_code"
              type="number"
              placeholder="请输入"
            />
            <view class="code">
              <text class="btn-text-small" v-if="!isSend" @tap="getSmsCode"
                >获取验证码</text
              >
              <text class="color-low" v-if="isSend">{{ timeout }}秒后重发</text>
            </view>
          </view>
        </template>
      </MyCell>
      <MyCell prefix="新密码">
        <template #content
          ><input
            v-model="formState.password"
            type="password"
            :password="true"
            placeholder="请输入新密码"
        /></template>
      </MyCell>
    </view>
    <view class="p20">
      <button class="btn-primary" @tap="handleSubmit">确认修改</button>
    </view>
  </View>
</template>

<script setup>
import { ref } from "vue";
import Taro, { useDidShow, useDidHide, useReady, useLoad } from "@tarojs/taro";
import request from "@/utils/request";

import { useGlobalStore } from "@/stores";

import MyCell from "@/components/MyCell";

import MyInput from "@/components/MyInput";

const globalStore = useGlobalStore();

const formState = ref({
  mobile: "",
  password: "",
  sms_code: "",
});

definePageConfig({
  navigationBarTitleText: "修改密码",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

useLoad(async () => {
  const userInfo = await globalStore.getUserInfo();
  if (userInfo.mobile) {
    formState.value.mobile = userInfo.mobile;
  }
});

const isSend = ref(false);
const timeout = ref(60);

const timeoutCountdown = () => {
  setTimeout(() => {
    timeout.value = timeout.value - 1;

    if (timeout.value > 0) {
      timeoutCountdown();
    } else {
      isSend.value = false;
      timeout.value = 60;
    }
  }, 1000);
};

const getSmsCode = () => {
  if (!formState.value.mobile) {
    Taro.showToast({
      title: "请输入手机号码",
      icon: "none",
    });
    return;
  }
  request
    .post({
      url: "sendSmsCode",
      data: {
        mobile: formState.value.mobile,
      },
    })
    .then((res) => {
      isSend.value = true;
      timeoutCountdown();
      Taro.showToast({
        title: "验证码已发送",
        icon: "success",
      });
    });
};

const handleSubmit = () => {
  console.log(formState.value.sms_code);
  if (
    !formState.value.mobile ||
    !String(formState.value.sms_code).trim() ||
    !String(formState.value.password).trim()
  ) {
    Taro.showToast({
      title: "表单填写不全",
      icon: "error",
    });
    return;
  }
  if (String(formState.value.password).trim().length < 6) {
    Taro.showToast({
      title: "密码不能少于6位数",
      icon: "none",
    });
    return;
  }
  request
    .post({
      url: "user/changePassword",
      data: {
        mobile: formState.value.mobile,
        sms_code: String(formState.value.sms_code).trim(),
        password: String(formState.value.password).trim(),
      },
    })
    .then((res) => {
      Taro.showModal({
        title: "修改成功",
        content: "请使用新密码重新登录",
        showCancel: false,
        success: function (res) {
          if (res.confirm) {
            globalStore.logout();
            Taro.redirectTo({
              url: "/pages/login/login",
            });
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    });
};
</script>

<style lang="scss">
.code-ipt {
  display: flex;
  justify-content: space-between;
}
</style>
