<template>
  <web-view v-if="url" :src="url"></web-view>
</template>
<script setup>
import { ref } from "vue";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
import request from "@/utils/request";
definePageConfig({
  navigationBarTitleText: '常见问题',
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

const detail = ref({
  title: '',
  content: ''
})
const id = ref('')
const url = ref('')

const getDetail = () => {
  request.get({
    url: 'qa/' + id.value,
  }).then(res => {
    detail.value = {
      title: res.data.title,
      content: res.data.content
    }
    Taro.setNavigationBarTitle({
      title: res.data.title,
    })
  })
}

useLoad((options) => {
  id.value = options.id
  url.value = 'https://f.ts.yimitongxun.com/h5/qa?id=' + id.value
  getDetail()
})

</script>
