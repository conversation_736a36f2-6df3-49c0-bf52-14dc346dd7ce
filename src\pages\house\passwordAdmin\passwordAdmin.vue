<script setup>
import MyIcon from "@/components/MyIcon";
import request from "@/utils/request";
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import { onMounted, ref, unref, watch } from "vue";
import {
  createPwdHandel,
  getLockList,
  getLockPwdList,
  modifyPwdHandel,
  getLockDetail,
  getCloudPassword,
  getKeyHandel,
} from "@/utils/lock";
import { useGlobalStore } from "@/stores";
import MyPopup from "@/components/MyPopup";

const globalStore = useGlobalStore();

const pwdPopShow = ref(false);

const keyInfo = ref(null); //锁信息

const zukePwd = ref(); //租客密码（自定义永久密码）

const fangdongPwd = ref(false); //房东密码（管理员密码）

const datePwd = ref(false); //限时密码

const query = ref();
const keyboardPwd = ref("");
const keyboardPwdCofirm = ref("");

const pwdDetail = ref();
const pwdDateDetail = ref();

const eysclose =
  "https://yimits.oss-cn-beijing.aliyuncs.com/picss/eys-close.png";
const eysopen = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/eys-open.png";
const tip = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/tip.png";
const tip2 = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/tip-b.png";
import { changeAdminPasscode } from "@/utils/lock";
import { delPwdHandel } from "@/utils/lock";

const lockId = ref(); //暂时模拟

const fastMobile = ref();

const house_id = ref();

const pwdList = ref();

const typeAddPwd = ref(""); // 1:房东密码 2:租客密码 3:限时密码

const lockDetail = ref();

const selector = ref(["4小时", "8小时"]); // 下拉选项数组
const selectedValue = ref(0); // 默认选择第一个选项
const curentLock = ref();
const isDongJie = ref(false); //是否冻结

useLoad(async (q) => {
  console.log("load", q);
  lockId.value = q.lockId; //锁id
  fastMobile.value = q.fastMobile;
  house_id.value = q.house_id;

  // query.value = JSON.parse(q.item);
  // console.log(query.value);
  getDianLockList();
  getKeyHandel({ lockId: q.lockId }).then((res) => {
    // keyId.value = res?.list[0]?.keyId;
    console.log(res);
  });

  init().then(() => {
    // 获取锁详情信息
    getLockDetail({ keyInfo: keyInfo.value }).then((res) => {
      console.log(res);
      lockDetail.value = res;
      fangdongPwd.value = res.noKeyPwd;
    });
  });
});

const init = () => {
  console.log("获取钥匙列表");
  pwdPopShow.value = false;
  keyboardPwdCofirm.value = "";
  keyboardPwd.value = "";
  return new Promise((resolve, reject) => {
    getLockList().then(async (res) => {
      console.log(res);
      // 查找id
      const index = res.list.findIndex((item) => item.lockId == lockId.value);
      keyInfo.value = res.list[index];
      console.log(keyInfo.value, "keyInfo.value ");
      const list = await getLockPwdList({ keyInfo: keyInfo.value });
      resolve();
      console.log(list);
      if (list.length) {
        initPwd(list);
      }
    });
  });
};

// 初始化密码列表
const initPwd = (list) => {
  const idx = list.findIndex((item) => item.keyboardPwdType == "2");
  const idx2 = list.findIndex(
    (item) => item.keyboardPwdType == "3" && item.endDate > Date.now()
  );
  console.log(list[idx]);
  console.log(idx2);
  if (idx != -1) {
    zukePwd.value = list[idx].keyboardPwd;
    pwdDetail.value = list[idx];
  }

  if (idx2 != -1) {
    datePwd.value = list[idx2].keyboardPwd;
    pwdDateDetail.value = list[idx2];
  }
  // 获取锁详情信息
  getLockDetail({ keyInfo: keyInfo.value }).then((res) => {
    console.log(res);
    fangdongPwd.value = res.noKeyPwd;
  });
};

useDidShow(async () => {
  console.log("show");
  // query.value = JSON.parse(q.item);
  // console.log(query.value);
});

function getCurrentDate() {
  const date = new Date();
  const year = date.getFullYear();
  const month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份从 0 开始，需要加 1，并补零
  const day = ("0" + date.getDate()).slice(-2); // 日期补零

  return `${year}-${month}-${day}`;
}

const isOpenFangDong = ref(false);
const isOpenZuke = ref(false);
const isOpenDate = ref(false);

const isOpen = ref(false);
const isOpen2 = ref(false);

const startDate = ref(getCurrentDate());
const endDate = ref(getCurrentDate());

const clickEysHandel = () => {
  isOpen.value = !isOpen.value;
};

const clickEysHandel2 = () => {
  isOpen2.value = !isOpen2.value;
};

// 获取几个h后的时间戳
function getFutureTimestamp(h) {
  // 获取当前的时间戳（以毫秒为单位）
  const now = Date.now();
  // 将小时转换为毫秒：1小时 = 60分钟 = 3600秒 = 3600 * 1000毫秒
  const hoursInMilliseconds = h * 3600 * 1000;
  // 返回当前时间加上 h 小时后的时间戳
  return now + hoursInMilliseconds;
}

const addDatePwdHandel = (h = 1) => {
  // pwdPopShow.value = true;
  // typeAddPwd.value = 3;
  getCloudPassword({
    lockId: lockId.value,
    endDate: getFutureTimestamp(h),
    keyboardPwdName: `限时密码-${h}h`,
    keyboardPwdType: 3,
  }).then((resPwd) => {
    console.log(resPwd);
    init().then(() => {
      setTimeout(() => {
        Taro.showModal({
          title: "限时密码",
          content: `已经成功生成限时密码,密码为:${resPwd.keyboardPwd},请在有效期内使用,点击确定复制密码`,
          showCancel: false,
          confirmText: "确定",
          success: function (res) {
            if (res.confirm) {
              const str = `您好，
您的房间 ${lockDetail.value.lockAlias} 的开门密码是 ${resPwd.keyboardPwd}
这是限时密码，只能在指定时间使用
注：
密码必须在 ${formatTimestamp(
                pwdDateDetail.value?.startDate
              )} - ${formatTimestamp(
                pwdDateDetail.value?.endDate
              )} 内使用，否则将自动失效。
开锁时，先激活锁键盘，再输入密码，以#号结束，#号键在键盘右下角，有可能是其它图标。
`;
              Taro.setClipboardData({
                data: str,
                success: function (res) {
                  Taro.showToast({
                    title: "密码已复制",
                    icon: "none",
                  });
                },
              });
            }
          },
        });
      }, 300);
    });
  });
};

// 获取单次密码
const getSignlePwd = () => {
  getCloudPassword({
    lockId: lockId.value,
    keyboardPwdName: `单次密码`,
    keyboardPwdType: 1,
  }).then((resPwd) => {
    Taro.showModal({
      title: "单次密码",
      content: `已经成功生成单次密码,请在6小时内使用,密码为:${resPwd.keyboardPwd},点击确定复制密码`,
      showCancel: false,
      confirmText: "确定",
      success: function (res) {
        if (res.confirm) {
          const str = `您好，
您的房间 ${lockDetail.value.lockAlias} 的开门密码是 ${resPwd.keyboardPwd}
这是单次密码，只能使用一次
注：
密码必须在 ${formatTimestamp(getFutureTimestamp(6))} 前使用，否则将自动失效。
开锁时，先激活锁键盘，再输入密码，以#号结束，#号键在键盘右下角，有可能是其它图标。
`;
          Taro.setClipboardData({
            data: str,
            success: function (res) {
              Taro.showToast({
                title: "密码已复制",
                icon: "none",
              });
            },
          });
        }
      },
    });
  });
};

const addZukePwdHandel = () => {
  if (isDongJie.value) {
    return Taro.showToast({
      title: "该密码已被冻结，请先解冻",
      icon: "none",
    });
  }
  pwdPopShow.value = true;
  typeAddPwd.value = 2;
};

const getDianLockList = () => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "ttlock/keyList",
        data: {
          lock_id: lockId.value,
          type: 2,
        },
      })
      .then((res) => {
        console.log(res);
        const filterArr = res.data.filter(
          (item) => item.mobile == fastMobile.value
        );
        console.log(filterArr);
        curentLock.value = filterArr[0];
        console.log(curentLock.value);
        if (curentLock.value?.status == 0) {
          isDongJie.value = true;
          zukePwd.value = curentLock.value?.password;
        } else {
          isDongJie.value = false;
        }
        resolve();
      })
      .finally(() => {
        Taro.hideLoading();
      });
  });
};

const freezeHandel = () => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "ttlock/freeze",
        data: {
          mobile: fastMobile.value,
          status: !isDongJie.value ? 0 : 1,
          lock_id: lockId.value,
        },
      })
      .then(() => {
        request
          .post({
            url: "ttlock/updatePassword",
            data: {
              lock_id: lockId.value,
              mobile: fastMobile.value,
              password: zukePwd.value,
            },
          })
          .then(async () => {
            await getDianLockList();
            resolve();
          });
      });
  });
};

// 冻结密码
const freeze = async () => {
  if (isDongJie.value) {
    // 解冻 添加密码
    Taro.showLoading({
      title: "正在解冻",
    });
    console.log(curentLock.value);
    await createPwdHandel({
      keyboardPwd: curentLock.value?.password,
      keyboardPwdName: `${globalStore.userInfo.nickname}-${globalStore.userInfo.mobile}`,
      keyInfo: keyInfo.value,
      keyboardPwdType: 2,
    });
    await init();
    await freezeHandel();
    Taro.showToast({
      title: "解冻成功",
      icon: "none",
    });
  } else {
    // 冻结 删除密码
    Taro.showLoading({
      title: "正在冻结",
    });
    await delPwdHandel({
      keyboardPwdId: unref(pwdDetail).keyboardPwdId,
      lockId: unref(pwdDetail).lockId,
      keyboardPwd: zukePwd.value,
      lockData: keyInfo.value?.lockData,
    });
    await freezeHandel();
    Taro.showToast({
      title: "冻结成功",
      icon: "none",
    });
  }
};

const dongjieHandel = () => {
  // dongJieHandel()
  Taro.showModal({
    title: `${isDongJie.value ? "解冻" : "冻结"}锁`,
    content: `是否${
      isDongJie.value ? "解冻" : "冻结"
    }租客密码？（请在锁附近操作！）`,
    success: function (res) {
      if (res.confirm) {
        freeze();
      }
    },
  });
};

const addFongdPwdHandel = () => {
  pwdPopShow.value = true;
  typeAddPwd.value = 1;
};

const onDateChange = (v) => {
  startDate.value = v.detail.value;
};

const onDateEndChange = (v) => {
  endDate.value = v.detail.value;
};

const redenrTitle = () => {
  let title = "";
  if (typeAddPwd.value == 1) {
    if (fangdongPwd.value) {
      title = "修改房东密码";
    } else {
      title = "添加房东密码";
    }
  } else if (typeAddPwd.value == 2) {
    if (zukePwd.value) {
      title = "修改租客密码";
    } else {
      title = "添加租客密码";
    }
  } else if (typeAddPwd.value == 3) {
    if (datePwd.value) {
      title = "修改有效期密码";
    } else {
      title = "添加有效期密码";
    }
  }
  return title;
};

function formatTimestamp(timestamp) {
  const date = new Date(timestamp);

  // 获取年、月、日、小时和分钟
  const year = date.getFullYear();
  const month = date.getMonth() + 1; // 月份从0开始，因此要加1
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();

  // 格式化为 "YYYY.M.D HH:MM" 格式
  const formattedDate = `${year}.${month}.${day} ${hours}:${
    minutes < 10 ? "0" + minutes : minutes
  }`;

  return formattedDate;
}

watch(() => keyboardPwd.value, (val) => {
  if (typeAddPwd.value == 2) {
    keyboardPwdCofirm.value = val;
  }
})

// 保存
const saveHandel = async () => {
  if (
    unref(keyboardPwd).trim() == "" ||
    unref(keyboardPwdCofirm).trim() == ""
  ) {
    Taro.showToast({
      title: "请输入密码",
      icon: "none",
    });
    return;
  }
  if (
    unref(keyboardPwd).trim().length != 6 ||
    unref(keyboardPwdCofirm).trim().length != 6
  ) {
    Taro.showToast({
      title: "请输入6位密码",
      icon: "none",
    });
    return;
  }
  if (unref(keyboardPwd).trim() != unref(keyboardPwdCofirm).trim()) {
    Taro.showToast({
      title: "两次输入的密码不一致",
      icon: "none",
    });
    return;
  }

  if (typeAddPwd.value == 1) {
    // 房东密码
    if (fangdongPwd.value) {
      // 修改密码
      Taro.showLoading({
        title: "修改中",
      });
      changeAdminPasscode({
        keyInfo: keyInfo.value,
        newPasscode: unref(keyboardPwd).trim(),
      }).then(() => {
        Taro.showToast({
          title: "修改成功",
        });
        // 获取锁详情信息
        getLockDetail({ keyInfo: keyInfo.value }).then((res) => {
          console.log(res);
          lockDetail.value = res;
          fangdongPwd.value = res.noKeyPwd;
        });
        init().then(() => {
          Taro.hideLoading();
          setTimeout(() => {
            Taro.showToast({
              title: "修改成功",
              icon: "none",
            });
          }, 1000);
        });
      });
    } else {
      // 添加密码
    }
  } else if (typeAddPwd.value == 2) {
    // 租客密码
    if (zukePwd.value) {
      modifyPwdHandel({
        oldKeyboardPwd: zukePwd.value,
        keyboardPwd: unref(keyboardPwd).trim(),
        keyInfo: keyInfo.value,
        keyboardPwdId: unref(pwdDetail).keyboardPwdId,
        lockId: unref(pwdDetail).lockId,
        keyboardPwdType: 2,
      }).then((res) => {
        console.log(res);
        init().then(() => {
          setTimeout(() => {
            Taro.showToast({
              title: "修改成功",
              icon: "none",
            });
          }, 900);
        });
      });
    } else {
      await createPwdHandel({
        keyboardPwd: unref(keyboardPwdCofirm).trim(),
        keyboardPwdName: `${globalStore.userInfo.nickname}-${globalStore.userInfo.mobile}`,
        keyInfo: keyInfo.value,
        keyboardPwdType: 2,
      });

      init().then(() => {
        setTimeout(() => {
          Taro.showToast({
            title: "添加成功",
            icon: "none",
          });
        }, 900);
      });
    }
  } else if (typeAddPwd.value == 3) {
    if (new Date(startDate.value) >= new Date(endDate.value)) {
      Taro.showToast({
        title: "结束日期需要大于开始日期",
        icon: "none",
      });
      return;
    }
    // 日期密码
    console.log(zukePwd.value);
    console.log(unref(keyboardPwd).trim());
    // console.log(unref(pwdDateDetail).keyboardPwdId);
    if (datePwd.value) {
      console.log("修改密码");
      modifyPwdHandel({
        oldKeyboardPwd: datePwd.value,
        keyboardPwd: unref(keyboardPwd).trim(),
        keyInfo: keyInfo.value,
        keyboardPwdId: unref(pwdDateDetail).keyboardPwdId,
        lockId: unref(pwdDateDetail).lockId,
        keyboardPwdType: 3,
        startDate: new Date(startDate.value).getTime(),
        endDate: new Date(endDate.value).getTime(),
      }).then((res) => {
        console.log(res);
        init().then(() => {
          setTimeout(() => {
            Taro.showToast({
              title: "修改成功",
              icon: "none",
            });
          }, 900);
        });
      });
    } else {
      console.log("添加密码");
      await createPwdHandel({
        keyboardPwd: unref(keyboardPwdCofirm).trim(),
        keyboardPwdName: `${globalStore.userInfo.nickname}-${globalStore.userInfo.mobile}`,
        keyInfo: keyInfo.value,
        startDate: new Date(startDate.value).getTime(),
        endDate: new Date(endDate.value).getTime(),
        keyboardPwdType: 3,
      });
      init().then(() => {
        setTimeout(() => {
          Taro.showToast({
            title: "添加成功",
            icon: "none",
          });
        }, 900);
      });
    }
  }
};

// 复制到简洁版
const copyHandel = (str) => {
  Taro.setClipboardData({
    data: str,
    success: function () {
      Taro.showToast({
        title: "复制成功",
        icon: "none",
      });
    },
  });
};

const clickEditHandel = () => {
  if (
    unref(keyboardPwd).trim() == "" ||
    unref(keyboardPwdCofirm).trim() == ""
  ) {
    Taro.showToast({
      title: "请输入密码",
      icon: "none",
    });
    return;
  }
  if (
    unref(keyboardPwd).trim().length != 6 ||
    unref(keyboardPwdCofirm).trim().length != 6
  ) {
    Taro.showToast({
      title: "请输入6位密码",
      icon: "none",
    });
    return;
  }
  if (unref(keyboardPwd).trim() != unref(keyboardPwdCofirm).trim()) {
    Taro.showToast({
      title: "两次输入的密码不一致",
      icon: "none",
    });
    return;
  }
  modifyPwdHandel({
    oldKeyboardPwd: query.value.keyboardPwd,
    keyboardPwd: unref(keyboardPwd),
    keyInfo: globalStore.KEYINFO,
    keyboardPwdId: query.value.keyboardPwdId,
    lockId: query.value.lockId,
  }).then((res) => {
    console.log(res);
    Taro.showToast({
      title: "修改成功",
      icon: "none",
    });
  });
};

const handleChange = (event) => {
  // 更新选中的值
  selectedValue.value = event.detail.value;
  addDatePwdHandel(selector.value[event.detail.value].split("小时")[0]);
};
</script>

<template>
  <view class="pwd-edit-lock">
    <view class="cell">
      <view class="tit">
        <view class="tx-left">
          <text>房东密码</text>
          <image
            v-if="fangdongPwd"
            :src="isOpenFangDong ? eysclose : eysopen"
            mode="aspectFit"
            @tap="isOpenFangDong = !isOpenFangDong"
          ></image>
        </view>
        <view
          v-if="fangdongPwd"
          class="pwd-box"
          :style="isOpenFangDong ? '' : 'font-size:25rpx;font-weight: 600'"
        >
          <text>{{ isOpenFangDong ? fangdongPwd : "······" }}</text>
          <text class="copy" v-if="fangdongPwd" @tap="copyHandel(fangdongPwd)"
            >复制</text
          >
        </view>
      </view>
      <view class="ipt">
        <view class="text" @tap="addFongdPwdHandel" v-if="!fangdongPwd"
          >+添加房东密码</view
        >
        <view class="text" @tap="addFongdPwdHandel" v-else>修改房东密码</view>
      </view>
    </view>

    <view class="cell">
      <view class="tit">
        <view class="tx-left">
          <text style="flex-shrink: 0">租客密码</text>
          <image
            v-if="zukePwd"
            :src="isOpenZuke ? eysclose : eysopen"
            mode="aspectFit"
            @tap="isOpenZuke = !isOpenZuke"
          ></image>
          <text
            :style="{ color: isDongJie ? '#FF3B30' : '', flexShrink: 0 }"
            :class="{ red: isDongJie }"
            >{{ !isDongJie ? "" : "已冻结" }}</text
          >
        </view>
        <view
          v-if="zukePwd"
          class="pwd-box"
          :style="isOpenZuke ? '' : 'font-size:25rpx;font-weight: 600'"
        >
          <text> {{ isOpenZuke ? zukePwd : "······" }}</text>
          <text class="copy" v-if="zukePwd" @tap="copyHandel(zukePwd)"
            >复制</text
          >
        </view>
      </view>
      <view class="ipt">
        <view class="text" @tap="addZukePwdHandel" v-if="!zukePwd"
          >+添加租客密码</view
        >
        <view class="text" @tap="addZukePwdHandel" v-else>修改租客密码</view>
        <view class="text" @tap="dongjieHandel" v-if="zukePwd">{{
          !isDongJie ? "冻结" : "解冻"
        }}</view>
      </view>
    </view>

    <view class="cell">
      <view class="tit">
        <view class="tx-left">
          <text>有效期密码</text>
          <image
            v-if="datePwd"
            class="pwd-box"
            :src="isOpenDate ? eysclose : eysopen"
            mode="aspectFit"
            @tap="isOpenDate = !isOpenDate"
          ></image>
        </view>
        <view
          v-if="datePwd"
          :style="isOpenDate ? '' : 'font-size:25rpx;font-weight: 600'"
        >
          <text> {{ isOpenDate ? datePwd : "······" }}</text>
          <text class="copy" v-if="datePwd" @tap="copyHandel(datePwd)"
            >复制</text
          >
        </view>
      </view>
      <view class="ipt">
        <!-- <view class="text" @tap="addDatePwdHandel" 
          >+获取限时密码</view
        > -->
        <view class="text">
          <!-- Picker 组件 -->
          <picker
            mode="selector"
            :range="selector"
            @change="handleChange"
            v-if="
              !pwdDateDetail?.endDate ||
              pwdDateDetail?.endDate <= new Date().getTime()
            "
          >
            <view>获取限时密码</view>
          </picker>
          <view
            style="color: #333"
            v-if="
              pwdDateDetail?.endDate &&
              pwdDateDetail?.endDate > new Date().getTime()
            "
            >{{ formatTimestamp(pwdDateDetail?.startDate) }} -
            {{ formatTimestamp(pwdDateDetail?.endDate) }}</view
          >
        </view>
      </view>
    </view>

    <view class="cell">
      <view class="tit">
        <view class="tx-left">
          <text>单次密码</text>
          <!-- <image
            :src="isOpenZuke ? eysclose : eysopen"
            mode="aspectFit"
            @tap="isOpenZuke = !isOpenZuke"
          ></image> -->
        </view>
      </view>
      <view class="ipt">
        <view class="text" @tap="getSignlePwd">获取单次密码</view>
      </view>
    </view>
  </view>

  <MyPopup :show="pwdPopShow" @close="pwdPopShow = false">
    <template #content>
      <view class="pwd-admin-pop">
        <view class="title">
          {{ redenrTitle() }}
        </view>
        <view class="content">
          <view class="input">
            <input
              maxlength="6"
              placeholder="请输入密码"
              :type="isOpen ? 'text' : 'password'"
              v-model="keyboardPwd"
              :key="isOpen"
            />
            <image
              :src="isOpen ? eysclose : eysopen"
              mode="aspectFit"
              @tap="clickEysHandel"
            ></image>
          </view>
          <view class="input"  v-if="typeAddPwd != 2">
            <input
              maxlength="6"
              placeholder="请再次请输入密码"
              :type="isOpen2 ? 'text' : 'password'"
              v-model="keyboardPwdCofirm"
              :key="isOpen2"
            />
            <image
              :src="isOpen2 ? eysclose : eysopen"
              mode="aspectFit"
              @tap="clickEysHandel2"
            ></image>
          </view>
          <view v-if="typeAddPwd == 3">
            <view class="picker-box">
              <picker mode="date" @change="onDateChange">
                <view class="picker"> 选择开始时间{{ startDate }} </view>
              </picker>
              <picker mode="date" @change="onDateEndChange">
                <view class="picker"> 选择结束时间 {{ endDate }} </view>
              </picker>
            </view>
          </view>
          <view class="tip-box">
            <view class="one">
              <image :src="tip" mode="aspectFit"></image>
              <text>密码限制为6为数字</text>
            </view>
            <view class="two">
              <image :src="tip2" mode="aspectFit"></image>
              <text>请确认您已靠近门锁</text>
            </view>
          </view>
        </view>
        <view class="utils">
          <view class="cancel" @tap="pwdPopShow = false">取消</view>
          <view class="save" @tap="saveHandel">保存</view>
        </view>
      </view>
    </template>
  </MyPopup>
</template>

<style lang="less">
page {
  background-color: #f1f3f7;
}
.pwd-admin-pop {
  .title {
    text-align: center;
    padding-top: 52px;
  }
  .content {
    padding: 52px 59px;
    padding-top: 0;

    .input {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 40px;
      height: 86px;
      padding: 0 20px;
      background: #fbfcfd;
      border-radius: 10px;
      border: 1px solid #d7d7d7;
      input {
        font-size: 30px;
        flex: 1;
      }
      image {
        width: 41px;
        height: 28px;
      }
    }
    .picker-box {
      // display: flex;
      margin-top: 15px;
      justify-content: space-between;
      align-items: center;
      .picker {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
        height: 74px;
        color: #fff;
        background-color: #2e4dcf;
        border-radius: 15px;
        margin-top: 15px;
        // width: 330px;
      }
    }
    .tip-box {
      margin-top: 42px;
      font-size: 24px;
      > view {
        image {
          width: 20px;
          height: 20px;
        }
      }
      .one {
        color: #939393;
        margin-bottom: 30px;
        text {
          color: #939393;
          margin-left: 10px;
        }
      }
      .two {
        color: #1352fd;
        text {
          color: #1352fd;
          margin-left: 10px;
        }
      }
    }
  }
  .utils {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid #dfdfdf;
    padding-top: 30px;
    margin-top: 30px;
    padding: 52px 59px;
    padding-bottom: 20px;
    > view {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .cancel {
      width: 300px;
      height: 89px;
      background: #fbfcfd;
      border-radius: 20px;
      border: 2px solid #2e4dcf;
      color: #1352fd;
    }
    .save {
      width: 300px;
      height: 89px;
      background: #1352fd;
      border-radius: 20px;
      border: 2px solid #2e4dcf;
      color: #fff;
    }
  }
}
.pwd-edit-lock {
  .cell {
    height: 200px;
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    margin-bottom: 5px;
    margin-top: 30px;
    .pwd-box {
      display: flex;
      align-items: center;
    }
    .tit {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 30px;
      color: #000;
      height: 90px;
      padding: 0 40px;
      border-bottom: 2px solid #f1f2f3;
      .tx-left {
        width: 240px;
        display: flex;
        align-items: center;
        .red {
          margin-left: 10px;
          font-size: 24px;
        }
      }
      image {
        width: 41px;
        height: 28px;
        margin-left: 10px;
        vertical-align: middle;
        margin-top: -5px;
        flex-shrink: 0;
      }
      .copy {
        font-size: 24px;
        color: #1352fd;
        font-weight: normal;
        display: inline-block;
        margin-left: 10px;
      }
    }
    .ipt {
      display: flex;
      align-items: center;
      .text {
        color: #1352fd;
        font-size: 26px;
      }
      > view {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30px 40px;
        box-sizing: border-box;

        input {
          font-size: 30px;
        }
      }
    }
    image {
      width: 41px;
      height: 28px;
    }
  }
  .tip-box {
    margin-top: 42px;
    font-size: 24px;
    margin-left: 40px;
    > view {
      image {
        width: 20px;
        height: 20px;
      }
    }
    .one {
      color: #939393;
      margin-bottom: 30px;
      text {
        color: #939393;
        margin-left: 10px;
      }
    }
    .two {
      color: #1352fd;
      text {
        color: #1352fd;
        margin-left: 10px;
      }
    }
  }
  .btn {
    width: 100%;
    min-height: 150px;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    // 设置ios刘海屏底部横线安全区域
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    button {
      width: 700px;
      height: 88px;
      background: #1352fd;
      border-radius: 20px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0px -4px 32px 0px rgba(54, 69, 193, 0.24);
    }
  }
}
</style>
