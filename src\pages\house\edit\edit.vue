<template>
  <view>
    <view class="house-form">
      <!-- <MyCell prefix="所在地" required arrow suffix="请选择"></MyCell> -->
      <MyCell
        prefix="小区名"
        required
        arrow
        :suffix="formState.estate_name ? '' : '请选择'"
        @tap="onChooseLocation"
        align="right"
      >
        <template #content>{{ formState.estate_name }}</template>
      </MyCell>
      <MyCell prefix="房源名" required align="right">
        <template #content>
          <input
            name="name"
            v-model="formState.name"
            placeholder="几栋几单元几室"
          />
        </template>
      </MyCell>
      <MyCell prefix="户型" align="right">
        <template #content>
          <view class="flex flex-v-center flex-right">
            <input
              type="number"
              name="fang"
              class="mini-input"
              v-model="formState.fang"
              placeholder="1"
            />
            <view class="mini-txt">房 /</view>
            <input
              type="number"
              name="ting"
              class="mini-input"
              placeholder="1"
              v-model="formState.ting"
            />
            <view class="mini-txt">厅 /</view>
            <input
              type="number"
              name="wei"
              class="mini-input"
              placeholder="1"
              v-model="formState.wei"
            /><view class="mini-txt">卫 /</view>
            <input
              type="number"
              name="wei"
              class="mini-input"
              placeholder="0"
              v-model="formState.chu"
            /><view class="mini-txt">厨</view>
          </view>
        </template>
      </MyCell>
      <MyCell prefix="参考租金" align="right" suffix="元/月">
        <template #content>
          <input
            type="digit"
            name="rent"
            v-model="formState.rent"
            placeholder="5000.00"
          />
        </template>
      </MyCell>
      <MyCell prefix="总面积" align="right" suffix="㎡">
        <template #content>
          <input
            type="digit"
            name="rent"
            v-model="formState.size"
            placeholder="100"
          />
        </template>
      </MyCell>
    </view>
    <view class="footer-fixed">
      <button class="btn-add m33" @tap="handleSubmit" :loading="loading">
        保存房源
      </button>
    </view>
  </view>
</template>
<script setup>
import { ref } from "vue";
import "./edit.scss";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
import request from "@/utils/request";
import { debounce } from "lodash";

import MyInput from "@/components/MyInput";
import MyCell from "@/components/MyCell";
import {validation} from "@/utils/validation"

import { useGlobalStore } from "@/stores";
const globalStore = useGlobalStore();

const formState = ref({
  estate_name: "",
  name: "",
  province: "",
  city: "",
  district: "",
  fang: 1,
  ting: 1,
  wei: 1,
  chu: 1,
  size: 0,
  rent: 0,
});

const loading = ref(false);

const handleSubmit = debounce(function () {
  validation(formState.value, {
    estate_name: {
      type: 'required',
      message: '请输入小区名'
    },
    name: {
      type: 'required',
      message: '请输入房源名'
    }
  }).then(() => {
    loading.value = true;
  request
    .post({
      url: "house/update/" + formState.value.id,
      data: {
        house: formState.value,
      },
    })
    .then((data) => {
      Taro.showToast({
        title: "保存成功！",
        icon: "success",
      });
      loading.value = false;
    })
    .catch((_) => {
      loading.value = false;
    });
  }).catch((err) => {
    Taro.showToast({
      title: err,
      icon: "error",
    });
  })

}, 500);

useLoad((options) => {
  formState.value.id = options.id;
  getDetail();
});

const getDetail = () => {
  request
    .get({
      url: "house/" + formState.value.id,
    })
    .then((res) => {
      formState.value = {
        id: res.data.house.id,
        estate_name: res.data.house.estate_name,
        name: res.data.house.name,
        province: res.data.house.province,
        city: res.data.house.city,
        district: res.data.house.district,
        fang: res.data.house.fang,
        ting: res.data.house.ting,
        wei: res.data.house.wei,
        chu: res.data.house.chu,
        size: res.data.house.size,
        rent: res.data.house.rent,
      };
    });
};

  // 防止精度丢失
  function addDecimals(a, b) {
		let multiplier = Math.pow(10, 10); // 选择一个适当的倍数
		let intA = Math.round(a * multiplier);
		let intB = Math.round(b * multiplier);
		let result = (intA + intB) / multiplier;
		return result;
	}

const onChooseLocation = () => {
  Taro.chooseLocation({
        success: (res) => {
          console.log(res)
          formState.value.estate_name = res.name

          // let reg = /.+?(省|市|自治区|自治州|县|区)/g
          let reg = /.+?(省|市|自治区|自治州|县|区|镇|乡|街道)/g;
          const area = res.address.match(reg)
          formState.value.province = area[0]
          formState.value.city = area[1]
          formState.value.district = area[2]
          formState.value.country = area[3] || ''
          formState.value.lat = res.latitude
          formState.value.lng = res.longitude
        }
        })
        return
  Taro.showLoading({
    title: "正在获取当前位置",
  });
  Taro.getLocation({
    isHighAccuracy: true,
    success: (local) => {
      console.log(local);
      Taro.hideLoading();
      if (!formState.value.estate_name) {
        // getAddress(local.latitude, local.longitude);
          // 微信有偏差需要处理
          if (process.env.TARO_ENV === 'weapp') {
            getAddress(addDecimals(local.latitude, 0.000312), addDecimals(local.longitude, 0.006611))
          } else {
            getAddress(local.latitude, local.longitude)
          }
        return;
      }
      Taro.chooseLocation({
        // latitude: local.latitude,
        // longitude: local.longitude,
        success: (res) => {
          console.log(res);
          formState.value.estate_name = res.name;

          let reg = /.+?(省|市|自治区|自治州|县|区)/g;
          const area = res.address.match(reg);
          formState.value.province = area[0];
          formState.value.city = area[1];
          formState.value.district = area[2];
        },
      });
    },
    fail: () => {
      Taro.hideLoading();
    },
  });
};

const getAddress = (latitude, longitude) => {
  request
    .get({
      url: "getAddress",
      data: {
        location: latitude + "," + longitude,
      },
    })
    .then((res) => {
      console.log(res.data);
      if (res.data.status === 0) {
        formState.value.estate_name =
          res.data.result.formatted_addresses.recommend;
        formState.value.province = res.data.result.address_component.province;
        formState.value.city = res.data.result.address_component.city;
        formState.value.district = res.data.result.address_component.district;
        console.log(formState);
      }
    });
};
</script>
