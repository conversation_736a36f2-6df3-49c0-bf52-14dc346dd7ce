<script setup>
import { computed, ref } from "vue";
import "./addWaterDevice.scss";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
import request from "@/utils/request";
import { debounce } from "lodash";
import { validation } from "@/utils/validation";
import shuiImg from "@/assets/water/device.png";

import MyInput from "@/components/MyInput";
import MyCell from "@/components/MyCell";
import MyIcon from "@/components/MyIcon";
import contantModal from "../../../pages/my/components/contantModal/index.vue";
import ClassPopUp from "@/components/ClassPopUp";

import { useGlobalStore } from "@/stores";
const globalStore = useGlobalStore();

const selectClassNames = ref([])

const classShow = ref(false)

const checkedList = ref([]);

const sn = ref("");

const posCur = ref(-1)

const onConfirm = (val,val2) => {
  selectClassNames.value = val;
  checkedList.value = val2;
}

const bindReplaceInput = (e) => {
  let pos = e.detail.cursor
  posCur.value = pos
}

const isContact2 = ref(false);

const utilsClass = ref("footer-fixed");

const device = ref({
  sn: "",
  net_type: "",
  price: "",
});

const deviceAll = ref()

const ServicePrice = ref('')

// 综合电价
const totalPrice = computed(() => {
//  return  deviceAll.value?.agent?.type == 2 ? (+device.value.price + Number(ServicePrice.value) + Number(deviceAll.value?.agent?.service)).toFixed(2)  : (+device.value.price + +ServicePrice.value).toFixed(2)
  // 统一 服务费+基础电价
  return  (+device.value.price + +ServicePrice.value).toFixed(2)
})


const house_name = ref("");

const house_id = ref("");

const formState = ref({
  estate_name: "",
  name: "",
  province: "",
  city: "",
  district: "",
  country: "",
  lat: "",
  lng: "",
  fang: 1,
  ting: 1,
  wei: 1,
  chu: 1,
  size: 0,
  rent: 0,
});

useLoad((options) => {
  console.log(options);
  
  if (!options.sn) {
    // Taro.showToast({
    //   title: '设备不存在',
    //   icon: 'error'
    // })
    return;
  }
  sn.value = options.sn;
  house_name.value = options.house_name;
  formState.value.name = options.house_name;
  formState.value.house_id = options.house_id;
  getDeviceDetail(sn.value);
  // // 如果有缓存的地址
  if (globalStore.cacheLocation && !house_name.value) {
    formState.value.province = globalStore.cacheLocation.province;
    formState.value.city = globalStore.cacheLocation.city;
    formState.value.district = globalStore.cacheLocation.district;
    formState.value.estate_name = globalStore.cacheLocation.estate_name;
    formState.value.country = globalStore.cacheLocation?.country;
    formState.value.lat = globalStore.cacheLocation?.lat;
    formState.value.lng = globalStore.cacheLocation?.lng;
  }
});

const handleSubmit = debounce(function () {
  if (deviceAll.value.type != 2) {
    return Taro.showToast({
      title: "当前表不是水表，请添加水表！",
      icon: "none",
    });
  }
  console.log(formState.value);
  console.log(device.value);

  let obj = {
    name: {
      type: "required",
      message: "请输入房源名",
    }
  }
  if (!house_name.value) { 
    obj.estate_name = {
      type: "required",
      message: "请输入小区名",
    }
  }

  validation(formState.value, obj)
    .then(() => {
      validation(device.value, {
        price: {
          type: "required",
          message: "请输入水费定价",
        },
      })
        .then(() => {
          if (device.value.price <= 0) {
            Taro.showToast({
              title: "水费定价必须大于0",
              icon: "none",
            });
            return;
          }
          if (isNaN(totalPrice.value)) {
            Taro.showToast({
              title: '请输入正确的价格!',
              icon: 'none'
            })
            return
          }
          if (globalStore.isAdmin) {
            if (selectClassNames.value.length == 0) {
              return Taro.showToast({
                title: "请选择分类",
                icon: "error",
              })
            }
          }
          globalStore.setcacheWaterDevicePrice(device.value.price);
          // Taro.showToast({
          //     title: "功能正在完善，敬请期待！",
          //     icon: "none",
          //   });
          // return
          //如果没有房源则是创建
          if (!house_name.value) {
            request.post({
              url: 'house/create',
              data: {
                house: formState.value,
                device: {
                  ...device.value,
                  basic_price: device.value.price,
                  service_price: ServicePrice.value,
                  price: totalPrice.value
                },
                classId: selectClassNames.value.length ? JSON.stringify(checkedList.value) : '',
              },
              showToast:false   
            }).then(data => {
              Taro.showModal({
                title: '提示',
                showCancel: false,
                content: '设备添加成功！',
                success: function (res) {
                  Taro.navigateBack()
                }
              })
            }).catch((err) => {
              console.log(err,"err");
              if (err.data.message.includes('专属客服')) {
                isContact2.value = true
              } else {
                Taro.showToast({
                  title: err.data.message,
                  icon: 'none'
                })
              }
            })
            return
          }
          request
            .post({
              url: "device/create",
              data: {
                  ...formState.value,
                  ...device.value,
                  basic_price: device.value.price,
                  service_price: ServicePrice.value,
                  price: totalPrice.value
              },
              showToast: false,
            })
            .then((data) => {
              Taro.showModal({
                title: "提示",
                showCancel: false,
                content: "设备添加成功！",
                success: function (res) {
                  Taro.navigateBack();
                },
              });
            })
            .catch((err) => {
              console.log(err, "err");
              if (err.data.message.includes("专属客服")) {
                isContact2.value = true;
              } else {
                Taro.showToast({
                  title: err.data.message,
                  icon: "none",
                });
              }
            });
        })
        .catch((err) => {
          Taro.showToast({
            title: err,
            icon: "error",
          });
        });
    })
    .catch((err) => {
      Taro.showToast({
        title: err,
        icon: "error",
      });
    });
}, 500);

// 防止精度丢失
function addDecimals(a, b) {
  let multiplier = Math.pow(10, 10); // 选择一个适当的倍数
  let intA = Math.round(a * multiplier);
  let intB = Math.round(b * multiplier);
  let result = (intA + intB) / multiplier;
  return result;
}

const onChooseLocation = () => {
  Taro.chooseLocation({
    success: (res) => {
      console.log(res);
      formState.value.estate_name = res.name;

      // let reg = /.+?(省|市|自治区|自治州|县|区)/g
      let reg = /.+?(省|市|自治区|自治州|县|区|镇|乡|街道)/g;
      const area = res.address.match(reg);
      console.log(area);
      formState.value.province = area[0];
      formState.value.city = area[1];
      formState.value.district = area[2];
      formState.value.country = area[3] || "";
      formState.value.lat = res.latitude;
      formState.value.lng = res.longitude;
      Promise.resolve().then(() => {
        globalStore.setcacheLocation(formState.value);
      });
    },
  });
  return;
};

const getDeviceDetail = (sn) => {
  request
    .get({
      url: "device/search",
      data: {
        sn: sn,
      },
    })
    .then((res) => {
      deviceAll.value = res.data
      device.value = {
        sn: res.data.sn,
        net_type: res.data.net_type,
        price: res.data.price,
      };
      if (!res.data.price) {
        // 如果有缓存的电价
        if (globalStore.waterDevicePrice) {
          device.value.price = globalStore.waterDevicePrice;
        }
      }
    })
    .catch(() => {
      // 如果有缓存的电价
      if (globalStore.waterDevicePrice) {
        device.value.price = globalStore.waterDevicePrice;
      }
    });
};

const getAddress = (latitude, longitude) => {
  request
    .get({
      url: "getAddress",
      data: {
        location: latitude + "," + longitude,
      },
    })
    .then((res) => {
      console.log(res.data);
      if (res.data.status === 0) {
        formState.value.estate_name =
          res.data.result.formatted_addresses.recommend;
        formState.value.province = res.data.result.address_component.province;
        formState.value.city = res.data.result.address_component.city;
        formState.value.district = res.data.result.address_component.district;
        console.log(formState);
      }
    });
};
</script>

<template>
  <view>
    <view class="house-form">
      <view class="device flex">
        <view class="device-img">
          <image :src="shuiImg" mode="aspectFill"></image>
        </view>
        <view class="device-info2">
          <view class="device-type">水表 </view>
          <view class="device-sn">设备编号：{{ sn }}</view>
        </view>
      </view>

      <MyCell
        v-if="!house_name"
        prefix="小区名"
        required
        arrow
        :suffix="formState.estate_name ? '' : '请选择'"
        @tap="onChooseLocation"
        align="right"
      >
        <template #content>{{ formState.estate_name }}</template>
      </MyCell>
      <MyCell
        prefix="分组"
        required
        v-if="globalStore.isAdmin"
        suffixWidth="200"
        arrow
        :suffix="selectClassNames.join(',') ? '' : '请选择分组'"
        @tap="classShow = true"
        align="right"
      >
        <template #content>{{ selectClassNames.join(',') }}</template>
      </MyCell>
      <MyCell prefix="房源名" required align="right">
        <template #content>
          <input
            name="name"
            :disabled="house_name ? true : false"
            @focus="utilsClass = ''"
            @blur="utilsClass = 'footer-fixed'"
            v-model="formState.name"
            cursor-spacing="120"
            placeholder="几栋几单元几室"
            :cursor="posCur"
            @input="bindReplaceInput"
          />
        </template>
      </MyCell>
      <MyCell prefix="基础水价" required align="right" suffix="元/m³">
        <template #content>
          <input
            type="digit"
            @focus="utilsClass = ''"
            @blur="utilsClass = 'footer-fixed'"
            name="price"
            cursor-spacing="120"
            v-model="device.price"
            placeholder=""
            :cursor="posCur"
            @input="bindReplaceInput"
          />
        </template>
      </MyCell>
      <MyCell prefix="服务费"  align="right" suffix="元/度">
        <template #content>
          <input type="digit" :cursor="posCur" @input="bindReplaceInput"  name="price" cursor-spacing="120"  v-model="ServicePrice" placeholder="输入服务费" />
        </template>
      </MyCell>
      <MyCell prefix="水表名称" align="right">
        <template #content>
          <input
            type="text"
            name="alias"
            v-model="formState.alias"
            placeholder="请输入"
            :cursor="posCur"
            @input="bindReplaceInput"
          />
        </template>
      </MyCell>
    </view>
    <contantModal
      :show="isContact2"
      @close="isContact2 = false"
      :isInlineServe="false"
      :phone="globalStore.userInfo.service_te"
      title="绑定异常，请联系您的专属客服"
    />

    <view :class="utilsClass">
      <button class="btn-add m33" @tap="handleSubmit">添加设备</button>
    </view>
    <ClassPopUp 
      :classShow="classShow" 
      @close="classShow = false"
      @confirm="onConfirm"
    />
  </view>
</template>
