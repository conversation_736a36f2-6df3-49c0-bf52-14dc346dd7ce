<template>
  <View class="container">
    <view class="login-top">
      <view class="logo2"><image :src="Logo2" class="logo2-img"></image></view>
      <view class="t1">闪租婆</view>
      <view class="t2">让租赁更智能化</view>
    </view>
    <view class="login-form">
      <MyInput placeholder="请输入">
        <template #prefix><text>手机号</text></template>
        <template #content><input class="my-input-m" v-model="formState.mobile" type="number" placeholder="请输入" /></template>
      </MyInput>
      <MyInput placeholder="请输入">
        <template #prefix><text>密码</text></template>
        <template #content><input class="my-input-m" v-model="formState.password" :type="passwordType" :password="passwordType === 'password'" placeholder="请输入" /></template>
        <template #suffix>
            <image @tap="onChangePasswordType" v-if="passwordType === 'password'" :src="iconEye" class="icon-eye"></image>
            <image @tap="onChangePasswordType" v-if="passwordType === 'text'" :src="iconEyeClose" class="icon-eye"></image>
        </template>
      </MyInput>
      <view class="privacy flex flex-v-center">
        <view class="privacy-left">
          <image @tap="onChangePrivacy(1)" v-if="!formState.privacy" :src="iconRadio" class="icon-radio"></image>
            <image @tap="onChangePrivacy(0)" v-if="formState.privacy" :src="iconRadioChecked" class="icon-radio"></image>
          <text @tap="onChangePrivacy(1)">阅读并同意</text> <text class="link" @tap="onJump('/pages/article/article?key=yonghuxieyi')">用户手册</text> 和
          <text class="link" @tap="onJump('/pages/article/article?key=yinsizhengce')">隐私政策</text></view>
        <view class="privacy-right"><text @tap="onForgetPwd">忘记密码?</text></view>
      </view>
      <view>
        <button class="btn-primary" @tap="handleLogin">登录</button>
      </view>
    </view>
    <view class="reg" @tap="onReg">
        注册账号 <image :src="iconRegArrow" class="icon-reg-arrow"></image>
    </view>
  </View>
</template>

<script setup>
  import { ref } from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad } from '@tarojs/taro'
  import request from '@/utils/request'

  import MyInput from '@/components/MyInput'

  import './login.scss'
  const Logo2 = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/logo2.png'
  const iconEye = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-eye.png'
  const iconEyeClose = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-eye-close.png'
  const iconRadio = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-radio.png'
  const iconRadioChecked = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-radio-checked.png'
  const iconRegArrow = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-reg-arrow.png'

  import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

  const formState = ref({
    mobile: '',
    password: ''
  })

  const passwordType = ref('password')

  const onChangePasswordType = () => {
    if (passwordType.value === 'password') {
        passwordType.value = 'text'
    } else {
        passwordType.value = 'password'
    }
  }

  const onChangePrivacy = (val) => {
    formState.value.privacy = val
  }

  const handleLogin = () => {
    console.log(formState.value, 'formState')

    if (!formState.value.mobile || (!formState.value.password)) {
      Taro.showToast({
        title: '表单填写不全',
        icon: 'none'
      })
      return
    }
    if (!formState.value.privacy) {
      Taro.showToast({
        title: '请阅读并同意用户手册及隐私协议',
        icon: 'none'
      })
      return
    }
    //发起网络请求
    request.post({
      url: 'user/login',
      data: formState.value,
    }).then((res) => {
      Taro.setStorageSync('token', res.data.token)
      globalStore.setUserInfo(res.data)
      globalStore.homeHasChange = true
      updateOpenId()
    })
  }

  const updateOpenId = () => {
    if (process.env.TARO_ENV === 'weapp') {
      wx.login({
        success(res) {
          if (res.code) {
            request.post({
              url: 'user/updateOpenId',
              data: {
                code: res.code
              }
            }).then(_ => {
              Taro.switchTab({
                url: '/pages/index/index'
              })
            })
          }
        },
        fail() {
          Taro.showToast({
            title: '获取openid出错',
            icon: 'error'
          })
        }
      })
    } else {
      my.getAuthCode({
        scopes: 'auth_base',
        success: res => {
          // 在服务端获取用户信息
          if (res.authCode) {
            request.post({
              url: 'user/updateAlipayId',
              data: {
                code: res.authCode
              }
            }).then(_ => {
              Taro.switchTab({
                url: '/pages/index/index'
              })
            })
          }
        },
        fail: err => {
          console.log('my.getAuthCode 调用失败', err)
        }
      });
    }
  }

  const onReg = () => {
    Taro.redirectTo({
      url: '/pages/reg/reg'
    })
  }

  const onForgetPwd = () => {
    Taro.navigateTo({
      url: '/pages/login/forget/forget'
    })
  }

  const onJump = (path) => {
    Taro.navigateTo({
      url: path
    })
  }

</script>
