.mini-input {
  width: 60px;
  text-align: center;
}
.mini-txt {
  width: 60px;
  text-align: right;
}
.mini-txt2 {
  width: 60px;
  text-align: right;
}
.result {
  padding: 161px 125px;
  text-align: center;

  .result-t {
    font-size: 48px;
    font-weight: 500;
    color: #000000;
    padding-top: 31px;
  }
  .result-st {
    font-size: 30px;
    font-weight: 500;
    color: #A5ADBE;
    padding-top: 36px;
  }
  .result-btn {
    margin-top: 99px;

    view {
      margin-bottom: 36px;
    }
  }
}

.my-cell-content {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 500px;
  display: block;
}

