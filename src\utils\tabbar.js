
import Taro from "@tarojs/taro"

import { ref } from 'vue';

export const landlord = [
  {
    pagePath: 'pages/index/index',
    text: '首页',
    iconPath: 'assets/images/tabbar-home.png',
    selectedIconPath: 'assets/images/tabbar-home-active.png'
  },
  {
    pagePath: 'pages/chuzu/chuzu',
    text: '出租',
    iconPath: 'assets/images/chuzu.png',
    selectedIconPath: 'assets/images/chuzu-a.png'
  },
  {
    pagePath: 'pages/qianbao/qianbao',
    text: '钱包',
    iconPath: 'assets/images/qianbao.png',
    selectedIconPath: 'assets/images/qianbao-a.png'
  },
  // {
  //   pagePath: 'pages/message/message',
  //   text: '消息',
  //   iconPath: 'assets/images/tabbar-msg.png',
  //   selectedIconPath: 'assets/images/tabbar-msg-active.png'
  // },
  {
    pagePath: 'pages/my/my',
    text: '我的',
    iconPath: 'assets/images/tabbar-my.png',
    selectedIconPath: 'assets/images/tabbar-my-active.png'
  }
]

export const tenant = [
  {
    pagePath: 'pages/index/index',
    text: '首页',
    iconPath: 'assets/images/tabbar-home.png',
    selectedIconPath: 'assets/images/tabbar-home-active.png'
  },
  {
    pagePath: 'scan',
    iconPath: 'assets/images/scantab.png',
    selectedIconPath: 'assets/images/scantab.png',
    text: '扫码充值'
  },
  // {
  //   pagePath: 'pages/chuzu/chuzu',
  //   text: '出租',
  //   iconPath: 'assets/images/chuzu.png',
  //   selectedIconPath: 'assets/images/chuzu-a.png'
  // },
  // {
  //   pagePath: 'pages/qianbao/qianbao',
  //   text: '钱包',
  //   iconPath: 'assets/images/qianbao.png',
  //   selectedIconPath: 'assets/images/qianbao-a.png'
  // },
  {
    pagePath: 'pages/message/message',
    text: '消息',
    iconPath: 'assets/images/tabbar-msg.png',
    selectedIconPath: 'assets/images/tabbar-msg-active.png'
  },
  {
    pagePath: 'pages/my/my',
    text: '我的',
    iconPath: 'assets/images/tabbar-my.png',
    selectedIconPath: 'assets/images/tabbar-my-active.png'
  }
]

export const curent = ref(Taro.getStorageSync("tablist") && JSON.parse(Taro.getStorageSync("tablist")) || [
  {
    pagePath: 'pages/index/index',
    text: '首页',
    iconPath: 'assets/images/tabbar-home.png',
    selectedIconPath: 'assets/images/tabbar-home-active.png'
  },
  {
    pagePath: 'pages/chuzu/chuzu',
    text: '出租',
    iconPath: 'assets/images/chuzu.png',
    selectedIconPath: 'assets/images/chuzu-a.png'
  },
  {
    pagePath: 'pages/qianbao/qianbao',
    text: '钱包',
    iconPath: 'assets/images/qianbao.png',
    selectedIconPath: 'assets/images/qianbao-a.png'
  },
  {
    pagePath: 'pages/my/my',
    text: '我的',
    iconPath: 'assets/images/tabbar-my.png',
    selectedIconPath: 'assets/images/tabbar-my-active.png'
  }
])

export const landlordAdmin = [
  {
    pagePath: 'pages/index/index',
    text: '首页',
    iconPath: 'assets/images/tabbar-home.png',
    selectedIconPath: 'assets/images/tabbar-home-active.png'
  },
  {
    pagePath: 'pages/chuzu/chuzu',
    text: '出租',
    iconPath: 'assets/images/chuzu.png',
    selectedIconPath: 'assets/images/chuzu-a.png'
  },
  {
    pagePath: 'pages/my/my',
    text: '我的',
    iconPath: 'assets/images/tabbar-my.png',
    selectedIconPath: 'assets/images/tabbar-my-active.png'
  }
]