<template>
  <view>
    <view class="container">
      <view class="order-item" v-for="item in items" :key="item.id">
        <view class="goods-title">
          <view>{{item.goods_title}}</view>
          <view class="active" v-if="item.status == 5">使用中</view>
        </view>
        <view class="flex flex-space-between order-m flex-v-center">
          <view class="flex-l order-title">{{item.goods_desc}}</view>
          <view class="flex-r order-amount" style="text-align: right;"><text class="cny">￥</text>{{item.pay_amt}}</view>
          <view class="t-p" v-if="Number(item.return_fee) > 0">+{{ device_id ? (Number(item.pay_amt) - Number(item.return_fee) - Number(item.fee)).toFixed(2) : item.return_fee }}</view>
        </view>
        <view class="order-m order-info">
          <view>支付方式 <text v-if="item.pay_channel === 'wx_lite'">微信支付</text><text v-else>支付宝</text></view>
          <view class="mt10">支付日期 {{item.paid_at}}</view>
          <view class="mt10" v-if="item.status == 4">已退款</view>
          <view class="mt10" v-if="item.status == 4">使用电量：{{ item.diff }}度</view>
          <view class="del-order" v-if="item.status == 5" @tap="delOrder(item)">结束订单</view>
        </view>
      </view>
    </view>
    <GlobDialog @confirm="confirmGlobHandel"/>

  </view>
</template>

<script setup>
import {computed, ref} from 'vue'
  import Taro, { useDidShow, useDidHide, useReady,useUnload, useLoad, usePullDownRefresh, useReachBottom } from '@tarojs/taro'
  import request from '@/utils/request'
  import {formatTime} from '@/utils'
  import deviceConnect from '@/utils/deviceConnect'
  import GlobDialog from '@/components/globDialog/index.vue'

import MyIcon from '@/components/MyIcon'

  import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

  definePageConfig({
    navigationBarTitleText: "用电记录",
    navigationBarBackgroundColor: "#1352FD",
    navigationBarTextStyle: "white",
    enablePullDownRefresh: true,
  });

  /** ----------------------接口数据-begin----------------------------------- */
  const items = ref([])
  const total = ref(0)
  const device = ref()
  const pageIsAlive = ref(true)
  const timerRed = ref(null)
  const curItem = ref(null)
  const readSpeedStateZeroBleTime = ref(null)

  useDidHide(() => {
    pageIsAlive.value = false
    clearInterval(timerRed.value)
  })

  useUnload(() => {
    pageIsAlive.value = false
    clearInterval(timerRed.value)
    globalStore.setGlobDialog({show:false,type:''})
    globalStore.setDeviceTime(15)
  })
  const params = ref({
    page: 1,
    pageSize:5
  })

  const fetch = () => {
    items.value = []
    params.value.page = 1
    getList()
  }

  const getList = () => {
    request.get({
      url: device_id.value ? 'order/businessTempList' : 'order/tempList',
      data: {
        ...params.value,
        device_id:device_id.value || undefined
      }
    }).then(res => {
      if (items.value.length > 0) {
        items.value = [...items.value, ... res.data.data]
      } else {
        items.value = res.data.data
      }
      total.value = res.data.total
      if (res.data.current_page >= res.data.last_page) {
        isLastPage.value = true
      }
    })
  }

    // 操表
    const listenDeviceReadBle = (toast=true) => {
    let s = 0
    s=0
    return new Promise((resolve, reject) => {
      timerRed.value = setInterval(() => {
        s ++

        if (globalStore.device.message.length > 0) {
          let msg = globalStore.device.message.shift()
          console.log(msg, device.value.mac)
          if (msg.mac === device.value.mac) {
            if (msg.event === '0C') {
              device.value.status = msg.status
              device.value.du = msg.du
              device.value.read_at = formatTime(new Date())
              let postData = {
                du: msg.du,
                status: msg.status,
                info: globalStore.who,
                log: 1
              }
              device.value.total = msg.total
              postData.total = msg.total
              postData.power = msg.power
              request.post({
                url: 'device/' + device.value.id + '/read',
                showLoading: false,
                data: postData
              }).then(_=> {
                s=0
                Taro.hideLoading()
                clearInterval(timerRed.value)
                globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
                if (toast) {
                  globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
                  Taro.showToast({
                    title: '抄表成功',
                    duration: 3000
                  })
                }
                resolve()
              })
            }

            if (s >= 25) {
              s=0
              Taro.hideLoading()
              clearInterval(timerRed.value)
              Taro.showToast({
                title: '抄表超时',
                icon: 'error',
                duration: 2000
              })
              reject()
            }
          }
        }
      }, 1000)
    })
  }

  // 查询清0
  const listenDeviceClearBle = () => {
  let s = 0
  return new Promise((resolve, reject) => {
    readSpeedStateZeroBleTime.value = setInterval(() => {
      s ++

      if (globalStore.device.message.length > 0) {
        let msg = globalStore.device.message.shift()
        console.log(msg, device.value.mac)
        if (msg.mac === device.value.mac) {
          if (msg.event === '0A') {
            device.value.du = msg.du
            device.value.read_at = formatTime(new Date())
            let postData = {
              du: msg.du,
              info: globalStore.who,
              log: 1
            }
            request.post({
              url: 'device/' + device.value.id + '/read',
              showLoading: false,
              data: postData
            }).then(_=> {
              Taro.hideLoading()
              clearInterval(readSpeedStateZeroBleTime.value)
              globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
              Taro.showToast({
                title: '操作成功',
                duration: 3000
              })
              resolve()
            })
          }

          if (s >= 15) {
            Taro.hideLoading()
            clearInterval(readSpeedStateZeroBleTime.value)
            Taro.showToast({
              title: '操作超时',
              icon: "error",
              duration: 2000
            })
            reject()
          }
        }
      }

    }, 1000)
  })
  }

  const confirmGlobHandel = async () => {
    // 抄表
    clearInterval(readSpeedStateZeroBleTime.value)
      deviceConnect.getConnection()
      .then(() => {
        deviceConnect.queryStatus()
      })
      .catch(() => {
        if (pageIsAlive.value) {
          globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
          globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
          }
      })
      Taro.showLoading({
        title: '正在读表中...',
        icon: 'none',
        mask:true
      })
     await listenDeviceReadBle(false)
    unOrderHandel({device_total:curItem.value.device_total,order_no:curItem.value.order_no,isBle:true})
  }

  const unOrderHandel = ({device_total,order_no,isBle}) => {
    console.log((device.value.total - device_total).toFixed(2),"diff度数");
    
    Taro.showModal({
        title: '提示',
        content: '确定结束订单吗？',
        success: function (res) {
          if (res.confirm) {
            request.get({
              url: 'order/tempEnd',
              data: {
                orderNo:order_no,
                type: isBle ? 'ble' :'4G',//ble=蓝牙 4g=联网
                diff: isBle ? (device.value.total - device_total).toFixed(2) : 0//蓝牙表需要 最新的总用电量减去下单时的用电量 就是用的度数
              }
            }).then(res => {
              if (isBle) {
                // 把电量清零
                deviceConnect.getConnection()
                .then(() => {
                  deviceConnect.clear()
                listenDeviceClearBle()
                }).catch(() => {
                  if (pageIsAlive.value) {
                    globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
                    globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
                    }
                })
              }
              Taro.showToast({
                title: '操作成功',
                icon: 'none'
              });
              setTimeout(() => {
                fetch()
                Taro.navigateBack()
              },2000)
            })
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
  }

  const delOrder = async (item) => {
    // 根据设备id获取设备详情区分蓝牙还是4g
    curItem.value = item
    console.log(item.device_id);
    const detail = await request.get({
        url:'device/'+item.device_id
    })
    console.log(detail);
    let isBle = false
    device.value = detail.data
      if (detail.data.net_type == 1) {
      // 4G
        if (detail.data.signal <= 0) {
          Taro.showToast({
            title: '设备离线',
            icon: 'error',
            duration: 2000
          })
          return
        }
      isBle = false
      unOrderHandel({device_total:item.device_total,order_no:item.order_no,isBle})
      } else {
      // 蓝牙
      isBle = true
      // 蓝牙表需要先抄表
      globalStore.setGlobDialog({show:true,type:'BleConnectionTip'})
    }
  }

  /** ----------------------接口数据-end----------------------------------- */
  const device_id = ref()
  useLoad((query) => {
    console.log(query,"device_id");
    device_id.value = query?.device_id
    fetch()
  })

  useDidShow(() => {

  })

  usePullDownRefresh(() => {
    Taro.stopPullDownRefresh()
    fetch()
  })

  const isLastPage = ref(false)

  useReachBottom(() => {
    if (!isLastPage.value) {
      params.value.page += 1
      getList()
    }
  })


</script>
<style lang="scss">
page, body {
  background-color: #f1f2f3;
}
.container {
  padding-top: 20px;
}
.order-item {
  margin-bottom: 26px;
  background: #FFFFFF;
  text-align: left;
  border-radius: 14px;
  padding-bottom: 10px;
}

.goods-title {
  font-size: 30px;
  font-weight: 500;
  color: #000000;
  padding: 20px 16px 20px 42px;
  border-bottom: 1px solid #DFDFDF;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 20px;
  .active {
    color: red;
  }
}
.order-m {
  position: relative;
  margin: 31px 17px 36px 44px;
  .t-p {
    position: absolute;
    bottom: -50px;
    color: red;
    right: 28px;
    font-size: 36px;
    font-weight: 500;
  }
}

.order-title {
  font-size: 30px;
  font-weight: 500;
  color: #000000;
}

.order-amount {
  font-size: 48px;
  font-weight: normal;
  color: #000000;
}
.cny {
  font-size: 30px;
  font-weight: 500;
  color: #000000;
}

.order-info {
  position: relative;
  font-size: 26px;
  font-weight: 500;
  color: #A8B1CA;
  .del-order {
    position: absolute;
    bottom: -10px;
    right: 20px;
    color: #1352fd;
    font-size: 30px;
  }
}


</style>
