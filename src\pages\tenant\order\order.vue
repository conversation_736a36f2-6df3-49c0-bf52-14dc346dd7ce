<template>
  <view>
    <view class="container">
      <view class="order-item" v-for="item in items" :key="item.id">
        <view class="goods-title">
          <view>{{item.goods_title}}</view>
        </view>
        <!-- <view class="flex flex-space-between order-m flex-v-center">
          <view class="flex-l order-title">{{item.goods_desc}}</view>
          <view class="flex-r order-amount" style="text-align: right;"><text class="cny">￥</text>{{item.pay_amt}}</view>
        </view> -->
        <view class="order-m order-info">
          <view class="mt10">充值ID： <text>{{ item.pay_channel === 'wx_lite' ? '微信用户' : '支付宝用户' }} ({{ item?.user?.account_id }})</text></view>
          <view class="mt10">收款方：<text>{{ maskRest(item?.business?.name) || '暂无昵称' }}</text></view>
          <view class="mt10">订单号：<text>{{ item?.order_no }}</text></view>
          <view class="mt10">电表名称：<text>{{ item?.house?.name }}</text></view>
          <view class="mt10">电表号：<text>{{ item?.device?.sn || item?.goods_desc.split('：')[1] }}</text></view>
          <view class="mt10" v-if="item?.business?.agent_id && item?.business?.agent && item?.business?.agent?.type == 2">充值金额：<text>{{ item?.pay_amt }}元</text></view>
          <view class="mt10" v-else>{{ item?.device?.type == 2 ? '到账水量：' : '到账电量：' }}<text>{{ item?.du }} {{ device?.type == 2 ? 'm³' : '度'}}</text></view>
          <view class="mt10" v-if="!item?.business?.agent_id || !item?.business?.agent || (item?.business?.agent_id && item?.business?.agent && item?.business?.agent?.type != 2)">基础费用：<text>{{ item?.device?.basic_price || item?.device_price }}元</text></view>
          <view class="mt10" v-if="!item?.business?.agent_id || !item?.business?.agent || (item?.business?.agent_id && item?.business?.agent && item?.business?.agent?.type != 2)">管理维修费：<text>{{ item?.service_fee }}元</text></view>
          <view class="mt10" v-if="!item?.business?.agent_id || !item?.business?.agent || (item?.business?.agent_id && item?.business?.agent && item?.business?.agent?.type != 2)">平台服务费：<text>0元</text></view>
          <view class="mt10">支付方式： <text v-if="item.pay_channel === 'wx_lite'">微信支付</text><text v-else>支付宝</text></view>
          <view class="mt10">支付日期： <text>{{item.paid_at}}</text></view>
          <view class="mt10" v-if="item.status == 4">已退款</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import {computed, ref} from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad, usePullDownRefresh, useReachBottom } from '@tarojs/taro'
  import request from '@/utils/request'
  import MyIcon from '@/components/MyIcon'

  import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

  definePageConfig({
    navigationBarTitleText: "充值记录",
    navigationBarBackgroundColor: "#1352FD",
    navigationBarTextStyle: "white",
  });

  function maskRest(str) {
    if (!str) {
      return str; // 如果字符串为空，则直接返回
    }
    if (str.length === 1) {
      return str; // 如果字符串只有一个字符，则直接返回
    }
    return str[0] + '*'.repeat(str.length - 1);
  }

  /** ----------------------接口数据-begin----------------------------------- */
  const items = ref([])
  const total = ref(0)

  const params = ref({
    page: 1
  })

  const fetch = () => {
    items.value = []
    params.value.page = 1
    getList()
  }

  const getList = () => {
    request.get({
      url: 'tenant/orders',
      data: {
        ... params.value
      }
    }).then(res => {
      if (items.value.length > 0) {
        items.value = [...items.value, ... res.data.items]
      } else {
        items.value = res.data.items
      }
      total.value = res.data.total
      if (res.data.currentPage >= res.data.lastPage) {
        isLastPage.value = true
      }
    })
  }

  /** ----------------------接口数据-end----------------------------------- */

  useLoad(() => {
    fetch()
  })

  useDidShow(() => {

  })

  usePullDownRefresh(() => {
    Taro.stopPullDownRefresh()
    fetch()
  })

  const isLastPage = ref(false)

  useReachBottom(() => {
    if (!isLastPage.value) {
      params.value.page += 1
      getList()
    }
  })


</script>
<style lang="scss">
page, body {
  background-color: #f1f2f3;
}
.container {
  padding-top: 20px;
}
.order-item {
  margin-bottom: 26px;
  background: #FFFFFF;
  text-align: left;
  border-radius: 14px;
  padding-bottom: 10px;
}

.goods-title {
  font-size: 30px;
  font-weight: 500;
  color: #000000;
  padding: 20px 16px 20px 42px;
  border-bottom: 1px solid #DFDFDF;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 20px;
  .active {
    color: red;
  }
}
.order-m {
  position: relative;
  margin: 31px 17px 36px 44px;
  .t-p {
    position: absolute;
    bottom: -50px;
    color: red;
    right: 28px;
    font-size: 36px;
    font-weight: 500;
  }
}

.order-title {
  font-size: 30px;
  font-weight: 500;
  color: #000000;
  text-align: center;
}

.order-amount {
  font-size: 48px;
  font-weight: normal;
  color: #000000;
}
.cny {
  font-size: 30px;
  font-weight: 500;
  color: #000000;
}

.order-info {
  position: relative;
  font-size: 26px;
  font-weight: 500;
  color: #A8B1CA;
  .del-order {
    position: absolute;
    bottom: -10px;
    right: 20px;
    color: #1352fd;
    font-size: 30px;
  }
  >view {
    margin-bottom: 15px;
    text {
      color: #000000;
      margin-left: 15px;
    }
  }
}


</style>
