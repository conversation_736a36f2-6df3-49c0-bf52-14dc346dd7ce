<!--
 * @Autor: lisong
 * @Date: 2023-08-13 19:44:19
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-13 20:54:50
-->
<template>
  <view class="top">{{ bill.title }}</view>
  <view class="amount">
    <view class="add">+</view>
    <view>{{ bill.amount }}</view>
  </view>
  <view class="sub">成功</view>
  <view class="list">
    <view class="list-label">支付方式</view>
    <view class="list-content">{{ paymentName }}</view>
  </view>
  <view class="list">
    <view class="list-label">创建时间</view>
    <view class="list-content">{{ bill.bill_pay?.created_at }}</view>
  </view>
  <view class="list">
    <view class="list-label">支付时间</view>
    <view class="list-content">{{ bill.bill_pay?.paid_at }}</view>
  </view>
  <view class="list">
    <view class="list-label">订单号</view>
    <view class="list-content">{{ bill.bill_pay?.sn }}</view>
  </view>
  <view class="list">
    <view class="list-label">合同号</view>
    <view class="list-content">
      <view>{{ bill.contract?.sn }}</view>
      <view @tap="onContract" class="look">查看</view>
    </view>
  </view>
  <view class="list">
    <view class="list-label">房源信息</view>
    <view class="list-content"
      >{{ bill.house?.estate_name }}{{ bill.house?.name }}
    </view>
  </view>
  <view class="list">
    <view class="list-label">租客信息</view>
    <view class="list-content">{{ bill.contract?.master_tenant?.name }}</view>
  </view>
  <view class="list" @tap="handleCall">
    <view class="list-label">手机号</view>
    <view class="list-content">
      <view>{{ bill.contract?.master_tenant?.mobile }}</view>
      <image
        class="icon-phone"
        src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/icon-phone.png"
      ></image>
    </view>
  </view>
  <view class="list" v-if="bill.bill_pay?.payment < 4">
    <view class="list-label">操作人</view>
    <view class="list-content">
      <text>{{ bill.business?.name }}({{bill.business?.user?.mobile}})</text>
    </view>
  </view>
  <view class="list">
    <view class="list-label">描述</view>
    <view class="list-content">
      <text>{{ bill.start_date }}~{{bill.end_date}} </text><text v-if="bill.type === 1">房租</text>
      <text v-if="bill.type === 2">押金</text><text v-if="bill.type === 3">电费</text><text v-if="bill.type === 4">水费</text>
    </view>
  </view>
</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import myBottomBtn from "@/components/MyBottomBtn/MyBottomBtn";
import { getApiRoot } from "@/config";
import { addMonth } from "@/utils";
import { useGlobalStore } from "@/stores";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
const globalStore = useGlobalStore();
definePageConfig({
  navigationBarTitleText: "账单详情",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

let bill_id = 0;
useLoad((options) => {
  bill_id = options.id;
  getBillDetail();
});

const bill = ref({});
const getBillDetail = () => {
  request
    .get({
      url: "contract/bill/" + bill_id,
    })
    .then((res) => {
      paymentList.map((item) => {
        if (item.value === res.data.bill_pay.payment) {
          paymentName.value = item.label;
        }
      });
      bill.value = res.data;
    });
};

const paymentList = [
  { label: "线下收款-微信", value: 1 },
  { label: "线下收款-支付宝", value: 2 },
  { label: "线下收款-银行卡", value: 3 },
  { label: "线上收款", value: 4 },
];

const paymentName = ref("线下收款-微信");

const onContract = () => {
  Taro.navigateTo({
    url: "/pages/contract/detail/detail?id=" + bill.value.contract_id,
  });
};

const handleCall = () => {
  Taro.makePhoneCall({
    phoneNumber: bill.value.contract?.master_tenant?.mobile, //仅为示例，并非真实的电话号码
  });
};
</script>

<style lang="scss">
.top {
  height: 147rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-family: OPPOSans;
  font-weight: 500;
  color: #000000;
}

.amount {
  font-size: 101rpx;
  font-family: Bahnschrift;
  font-weight: normal;
  color: #000000;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add {
  font-size: 73rpx;
  font-family: OPPOSans;
  font-weight: 500;
  color: #000000;
  margin-right: 10rpx;
}

.sub {
  padding: 40rpx 0;
  font-size: 32rpx;
  font-family: OPPOSans;
  font-weight: 500;
  color: #b6bec5;
  text-align: center;
}

.list {
  margin-bottom: 26rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 32px;
  .list-label {
    font-size: 30rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #b6bec5;
  }
  .list-content {
    font-size: 30rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
    display: flex;
    align-items: center;
    .look {
      padding-left: 27rpx;
      padding-right: 30rpx;
      background: url("https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/icon-main_select.png")
        right center no-repeat;
      background-size: 15rpx auto;
      color: #1352fd;
    }
    .icon-phone {
      width: 30rpx;
      height: 30rpx;
      margin-left: 16rpx;
    }
  }
}
</style>
