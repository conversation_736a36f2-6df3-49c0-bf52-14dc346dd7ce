<script setup>
import { ref } from "vue";
import { AtCheckbox } from "taro-ui-vue3";
import request from "@/utils/request";
import Taro, { useDidShow, useLoad, usePullDownRefresh,useUnload } from "@tarojs/taro";
import emtyImg from "@/assets/pic/emty.png";
import { unref,onUnmounted } from "vue";
import { useGlobalStore } from "@/stores";
import GlobDialog from "@/components/globDialog/index.vue";
import YModal from "@/components/YModal";
const totalD = 'https://yimits.oss-cn-beijing.aliyuncs.com/miniProgram/223531730875525_.pic.jpg'
const fenD = 'https://yimits.oss-cn-beijing.aliyuncs.com/miniProgram/223541730875525_.pic.jpg'
import { AtSwitch } from "taro-ui-vue3/lib";


const globalStore = useGlobalStore();

const isMore = ref(false);

const checkedList = ref([]);

const classList = ref([]);

const originalClassList = ref([]); // 存储原始数据

const searchKeyword = ref(''); // 搜索关键词

const mainDevices = ref([]);

const curentIdx = ref(0);

const curentDevice = ref();

const curIndex = ref(0);

const showTipModal = ref(false)

const curType = ref()

const tipImg = ref(totalD)

onUnmounted(() => {
  globalStore.setGlobDialog({
        show: false,
        type: "",
      });
})

useUnload(() => {
  globalStore.setGlobDialog({
      show: false,
      type: "",
    });
})

useDidShow((options) => {
  console.log(options);
  getList();
  searchKeyword.value = ''
  mainDevices.value.splice(0);
  checkedList.value.splice(0);
});

const itemClickHandel = (index) => {
  isMore.value = !isMore.value;
  curentIdx.value = index;
};

// 获取分类列表
const getList = () => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "houseClass/list",
        data: {
          type: "edit",
        },
      })
      .then((res) => {
        if (res.code != 200) return;
        console.log(res);
        const processedData = res.data.map((item) => {
          return {
            ...item,
            isMainShare: item.houses.some((dev) =>
              dev.device.some((dev) => dev.bus_device)
            ),
          };
        });

        // 保存原始数据
        originalClassList.value = processedData;
        classList.value = processedData;
        console.log(classList.value, "classList");

        // 是否有公摊表 isMainShare
        resolve();
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const handleChange = (val, sitem, index) => {
  console.log(val);
  console.log(sitem, "sitem");
  console.log(index, "index");
  curIndex.value = index;
  curentDevice.value = sitem;
  checkedList.value.splice(0);
  const idx = checkedList.value.findIndex((item) => item == val);
  if (idx != -1) {
    checkedList.value.splice(idx, 1);
    console.log(checkedList.value, "arrset");
    return;
  }

  checkedList.value.push(val);

  const arr = new Set(checkedList.value);
  checkedList.value = Array.from(arr);

  console.log(checkedList.value, "arrset");
};

const toSonDeviceSettingHandel = (item, idx, label, tag_id) => {
  console.log(idx, label, tag_id, "item");
  if (!item?.bus_device) {
    Taro.showToast({
      title: "请先加入公摊",
      icon: "none",
    });
    return;
  }
  let arr = [];
  if (item?.bus_device?.type == 1) return;

  classList.value[idx].houses.forEach((house) => {
    house.device.forEach((dev) => {
      if (
        dev.bus_device &&
        dev.bus_device?.type == 1 &&
        !arr.some((existingDev) => existingDev.id === dev.id) // 检查是否已经存在
      ) {
        console.log(dev, "maindev");
        arr.push(dev);
      }
    });
  });

  console.log(arr, "Arr");

  const obj = {
    sn: item?.sn,
    device_id: item?.id,
    public_sn: arr[0]?.sn,
    public_device_id: arr[0]?.id,
    // bus_id: item?.bus_device?.id,
    status: item?.bus_device?.status,
    tag_name: label,
    rate: item?.bus_device?.rate,
    recharge: item?.bus_device?.recharge,
    remark: item?.bus_device?.remark,
    clear: item?.bus_device?.clear,
    tag_id: tag_id,
  };
  console.log(obj);
  Taro.navigateTo({
    url:
      "/pages/other/publicShareSonSetting/publicShareSonSetting?item=" +
      JSON.stringify(obj),
  });
};

const createTagHandel = () => {
  Taro.navigateTo({
    url: "/pages/other/houseTagAdmin/houseTagAdmin",
  });
};

const toDeviceSettingHandel = (idx) => {
  // 查询到公摊设备
  mainDevices.value.splice(0);
  classList.value[idx].houses.forEach((house) => {
    house.device.forEach((dev) => {
      if (
        dev.bus_device &&
        dev.bus_device?.type == 1 &&
        !mainDevices.value.some((existingDev) => existingDev.id === dev.id) // 检查是否已经存在
      ) {
        console.log(dev, "maindev");
        mainDevices.value.push(dev);
      }
    });
  });

  console.log(mainDevices.value, "mainDevices");
  console.log(classList.value[idx]);

  const obj = {
    label: classList.value[idx].label,
    device_id: mainDevices.value[0]?.id,
    device_mac: mainDevices.value[0]?.mac,
    tag_id: classList.value[idx].id,
  };

  Taro.navigateTo({
    url:
      "/pages/other/publicShareSetting/publicShareSetting?obj=" +
      JSON.stringify(obj),
  });
};

const toLogHandel = (item, idx) => {
  console.log(item);
  mainDevices.value.splice(0);
  // 查询到公摊设备
  classList.value[idx].houses.forEach((house) => {
    house.device.forEach((dev) => {
      if (
        dev.bus_device &&
        dev.bus_device?.type == 1 &&
        !mainDevices.value.some((existingDev) => existingDev.id === dev.id) // 检查是否已经存在
      ) {
        console.log(dev, "maindev");
        mainDevices.value.push(dev);
      }
    });
  });

  console.log(mainDevices.value, "mainDevices");
  const obj = {
    id: item.id,
    sn: mainDevices.value[0]?.sn,
    device_id: mainDevices.value[0]?.id,
  };
  Taro.navigateTo({
    url: "/pages/other/publicLog/publicLog?item=" + JSON.stringify(obj),
  });
};

/**
 * 设置为后付费模式
 */
const setMode = (mac) => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "device/setMode",
        data: {
          mac,
          type: "2", //模式1-预付费模式 2-后付费模式
        },
        showLoading: false,
      })
      .then((res) => {
        if (res.code != 200) return reject();
        if (res.data?.pay_type == 1) {
          // 重试一次
          request
            .get({
              url: "device/setMode",
              data: {
                mac,
                type: "2", //模式1-预付费模式 2-后付费模式
              },
              showLoading: false,
            })
            .then((res) => {
              if (res.code != 200) return;
              if (res.data?.pay_type == 1) {
                Taro.showToast({
                  title: "设置模式失败请重试！",
                  icon: "none",
                  duration: 2000,
                });
                return reject();
              } else {
                resolve(res.data);
              }
            });
          return;
        }
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const settingDeviceHandel = () => {
  if (checkedList.value.length == 0) {
    return Taro.showToast({
      title: "请选择设备",
      icon: "none",
      duration: 2000,
    });
  }
  console.log(curentDevice.value, "curentDevice");
  if (curentDevice.value?.net_type != 1) {
    return Taro.showToast({
      title: "当前设备不是4G设备，请选择4G设备",
      icon: "none",
      duration: 2000,
    });
  }
  if (
    curentDevice.value?.net_type == 1 &&
    curentDevice.value?.signal_num <= 0
  ) {
    return Taro.showToast({
      title: "当前设备离线，请先上线设备",
      icon: "none",
      duration: 2000,
    });
  }
  Taro.showActionSheet({
    itemList: ["子表模式", "总表模式"],
    success: async function (res) {
      let type = res.tapIndex == 0 ? 1 : 2;
      if (process.env.TARO_ENV === "alipay") {
       type = res.index == 0 ? 1 : 2;
      }
      showTipModal.value = true
      if (type == 1) {
        tipImg.value = fenD
      } else {
        tipImg.value = totalD
      }
      curType.value = type
    },
  });
};

const tipConfirmHandel = () => {
  showTipModal.value = false
  Taro.showModal({
    title: "提示",
    content: "确定要设置公摊设备吗？",
    success: async function (res) {
      if (res.confirm) {
        console.log("用户点击确定");
        globalStore.setGlobDialog({
          show: true,
          type: "setModeTimeLoading",
        });
        // Taro.showLoading({
        //   title: "设置中...",
        //   mask: true,
        // });
        await setMode(curentDevice.value.mac);
        request
          .get({
            url: "device/setBus",
            data: {
              device_id: checkedList.value[0],
              count_type: curType.value,
            },
            showLoading: false,
            showToast: false
          })
          .then((res) => {
            if (res.code != 200) return;
            getList().then(() => {
              Taro.showToast({
                title: "设置成功",
                icon: "success",
                duration: 2000,
              });
              globalStore.setGlobDialog({
                show: false,
                type: "",
              });
              setTimeout(() => {
                toDeviceSettingHandel(unref(curentIdx));
              }, 400);
            });
          })
          .catch((err) => {
            console.log(err);
            setTimeout(() => {
              Taro.showToast({
                title: err?.data?.message || '设置失败！',
                icon: "none",
                duration: 2000,
              });
            }, 500);
          })
          .finally(() => {
            Taro.hideLoading();
            globalStore.setGlobDialog({ show: false, type: "" });
          });
      } else {
        console.log("用户点击取消");
      }
    },
  });
}

const previewImage = (url) => {
  Taro.previewImage({
    urls: [url],
  });
};

const changeAutoSwitch = (e,id) => {
  console.log(e);
  request.get({
    url: "device/editBusAuto",
    data: {
      is_autoload: e ? 1 : 0,
      bus_id:id
    },
    showLoading: true,
  }).then(() => {
    Taro.showToast({
      title: e ? '开启自动均摊成功！' : '关闭自动均摊成功！',
      icon: 'none',
      duration: 3500,
    })
  })
}

// 搜索功能
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    // 搜索关键词为空时，显示全部原始数据
    classList.value = originalClassList.value;
    return;
  }

  // 按照label字段进行模糊搜索
  classList.value = originalClassList.value.filter(item =>
    item.label && item.label.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
}

// 监听搜索框输入变化
const onSearchInput = (e) => {
  searchKeyword.value = e.detail.value;
  if (!searchKeyword.value.trim()) {
    // 搜索关键词为空时，立即显示全部原始数据
    classList.value = originalClassList.value;
  }
}

// 回车搜索
const onSearchConfirm = () => {
  handleSearch();
}
</script>

<template>
  <view class="pubilc-share-container">
    <!-- 搜索框 -->
    <view class="search-container">
      <input
        class="search-input"
        type="text"
        placeholder="请输入分类名称搜索"
        :value="searchKeyword"
        @input="onSearchInput"
        @confirm="onSearchConfirm"
        confirm-type="search"
      />
      <view class="search-btn" @tap="handleSearch">
        <text class="iconfont icon-sousuo"></text>
      </view>
    </view>

    <!-- emty -->
    <view class="emty-box" v-if="classList.length == 0">
      <image :src="emtyImg" class="emty-img" mode="aspectFill"></image>
      <view>{{ searchKeyword ? '暂无搜索结果' : '暂无公摊' }}</view>
    </view>
    <!-- 列表 -->
    <view class="list" v-for="(item, index) in classList" :key="index">
      <view class="item">
        <view class="top" @tap="itemClickHandel(index)">
          <view class="left">
            <view class="_name">
              <view
                class="icon-xiajiantou iconfont"
                v-if="item?.houses?.length > 0"
                :style="{
                  transform: `rotate(${
                    isMore && curentIdx == index ? '180' : '0'
                  }deg) translateY(3rpx)`,
                  transition: 'all .3s',
                }"
              ></view>
              <view class="title">{{ item?.label }}</view>
            </view>
          </view>
          <view class="right"> ({{ item?.houses?.length }}) </view>
        </view>
        <view
          class="detail"
          v-if="isMore && curentIdx == index && item?.houses?.length > 0"
        >
          <view class="_bottom" v-for="oitem in item.houses" :key="item.id">
            <view class="tit">
              <image
                src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
                mode="scaleToFill"
                class="icon-s"
              />
              <view>
                <text>{{ oitem?.name }}</text>
                <text v-if="oitem.device.some(
                      (eitem) =>
                        eitem?.bus_device != null && eitem?.bus_device?.pid == 0
                    )" style="font-size: 24rpx;display: inline-block;transform: translateY(-3rpx);">(下方开关为是否开启自动平摊)</text>
                <text
                  class="lebel"
                  v-if="
                    oitem.device.some(
                      (eitem) =>
                        eitem?.bus_device != null && eitem?.bus_device?.pid != 0
                    )
                  "
                  >分摊权重</text
                >
              </view>
            </view>
            <!-- 电表 -->
            <view class="li" v-for="(sitem, idx) in oitem.device" :key="idx">
              <view
                class="le"
                v-if="sitem?.type != 2"
                @tap.stop="
                  toSonDeviceSettingHandel(sitem, index, item?.label, item?.id)
                "
              >
                <text class="iconfont icon-dianbiao_shiti"></text>
                <text
                  class="sn"
                  :style="
                    sitem?.net_type == 1 && sitem?.signal_num <= 0
                      ? 'color: #98A6C3;'
                      : ''
                  "
                  >{{ sitem?.net_type == 1 ? "电表" : "电表" }}-{{
                    sitem?.sn
                  }}</text
                >
                <text
                  class="online"
                  style="color: #0072ff"
                  v-if="sitem?.signal_num > 0 && sitem?.net_type == 1"
                >
                  在线
                </text>
                <text
                  class="online"
                  style="color: #98a6c3"
                  v-if="sitem?.net_type == 1 && sitem?.signal_num <= 0"
                >
                  离线
                </text>
                <text
                  class="ed"
                  v-if="sitem?.bus_device && sitem?.bus_device.pid == 0"
                  >已设主表</text
                >
                <text
                  class="to"
                  v-if="
                    sitem?.bus_device &&
                    sitem?.bus_device.pid &&
                    sitem?.bus_device.status == 1
                  "
                  >分摊中</text
                >
                <text
                  class="to"
                  style="color: red; border-color: red"
                  v-if="
                    sitem?.bus_device &&
                    sitem?.bus_device.pid &&
                    !sitem?.bus_device.status
                  "
                  >分摊关闭</text
                >
                <!-- <text
                  class="to"
                  style="color: red; border-color: red"
                  v-if="
                    !sitem?.bus_device 
                  "
                  >未加入</text
                > -->
              </view>
              <view
                class="ri"
                v-if="sitem?.type != 2"
                :style="
                  sitem?.bus_device && sitem?.bus_device.pid == 0
                    ? 'margin-left: 0rpx;padding-left: 55rpx;'
                    : ''
                "
              >
              <AtSwitch
                title=""
                v-if="sitem?.bus_device && sitem?.bus_device.pid == 0"
                :checked="sitem?.bus_device?.is_autoload == 1 ? true : false"
                @change="changeAutoSwitch($event,sitem?.bus_device?.id)"
                size="small"
                style="position: absolute;
                padding: 0;
                margin: 0;
                width: 0rpx;
                transform: scale(0.6);
                background-color: transparent;
                "
              />
                <view
                  class="log"
                  v-if="sitem?.bus_device && sitem?.bus_device.pid == 0"
                  @tap.stop="toLogHandel(item, index)"
                  >公摊记录</view
                >
                <view
                  class="setting"
                  @tap.stop="toDeviceSettingHandel(index)"
                  v-if="sitem?.bus_device && sitem?.bus_device.pid == 0"
                >
                  <text class="iconfont icon-setting1"></text>
                </view>
                <view
                  v-if="sitem?.bus_device && sitem?.bus_device?.type != 1"
                  style="color: #000"
                >
                  {{ sitem?.bus_device?.rate || 0 }}%</view
                >
                <!--  -->
                <view
                  class="check"
                  v-if="!sitem?.bus_device && !item.isMainShare"
                >
                  <at-checkbox
                    :options="[{ label: '', value: sitem.id }]"
                    :selectedList="checkedList"
                    @change="handleChange(sitem.id, sitem, index)"
                  />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- utils -->
    <view class="footer-fixed utils" :style="{justifyContent: globalStore?.isAdmin ? 'center' : 'space-between',paddingLeft: globalStore?.isAdmin ? '0' : '72rpx'}">
      <text @tap="createTagHandel" v-if="!globalStore?.isAdmin">创建分组</text>
      <button class="btn-add m33" @tap="settingDeviceHandel">
        设置为公摊主表
      </button>
    </view>
  </view>
  <GlobDialog @confirm="confirmGlobHandel" />

  <YModal
      title="提示"
      confirmText="继续"
      :show="showTipModal"
      @close="showTipModal = false"
      @confirm="tipConfirmHandel"
      showCancel
    >
      <template #content>
        <view class="content-modal-pubilc-share">
          <view>
            <image
              :src="tipImg"
              @tap="previewImage(tipImg)"
              mode="aspectFill"
            />
          </view>
        </view>
      </template>
    </YModal>
</template>

<style lang="scss">
@import "taro-ui-vue3/dist/style/components/checkbox.scss";

page {
  background-color: #f8f9ff;
}
.content-modal-pubilc-share {
  padding: 20px;
  overflow: hidden;
  image {
    width: 100%;
    height: 460px;
    object-fit: contain;
    transform: translateY(65px);
  }
  >view{
    margin-bottom: 20px;
    text-align: center;
    // font-weight: 700;
    font-size: 35px;
  }
}
.pubilc-share-container {
  padding-bottom: 150px;

  .search-container {
    display: flex;
    align-items: center;
    padding: 20px 32px;
    background-color: #fff;
    margin-bottom: 20px;

    .search-input {
      flex: 1;
      height: 80px;
      padding: 0 20px;
      background-color: #f8f9ff;
      border-radius: 40px;
      font-size: 28px;
      color: #333;
      border: none;
      outline: none;

      &::placeholder {
        color: #999;
      }
    }

    .search-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;
      background-color: #1352fd;
      border-radius: 40px;
      margin-left: 20px;

      .iconfont {
        font-size: 32px;
        color: #fff;
      }
    }
  }
  .emty-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    font-size: 28rpx;
    color: #999;
    transform: translateY(-10%);
    .emty-img {
      width: 360px;
      height: 284px;
      margin: 0 auto;
    }
  }
  .list {
    .item {
      box-sizing: border-box;
      min-height: 120px;
      margin-bottom: 20px;
      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding: 20px 32px;

        .icon-xiajiantou {
          font-size: 24px;
          color: #6f7c9a;
          margin-right: 5px;
        }
        .icon-setting1 {
          font-size: 26px;
          color: #fff;
        }
        .left {
          display: flex;
          align-items: center;
          ._name {
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .setting {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 6px 12px;
            background-color: #dc7731;
            box-sizing: border-box;
            margin-left: 20px;
            border-radius: 16px;
          }
          .log {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 5px 10px;

            background: #5b7dd8;
            border-radius: 16px;
            color: #fff;
            // font-size: 26px;
            // margin-left: 20px;
            font-size: 22px;
            margin-left: 37px;
          }
          .title {
            margin-left: 20px;
            font-size: 38px;
            font-weight: 500;
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 320px;
            white-space: nowrap;
          }
        }
      }
      .detail {
        background-color: #fff;
        ._bottom {
          padding-top: 15px;
          padding-bottom: 20px;
          animation: opacity 0.3s;
          margin: 0px 32px;
          padding-bottom: 30px;
          padding-top: 25px;
          border-bottom: 2px solid #f8f9fe;

          @keyframes opacity {
            0% {
              opacity: 0;
            }
            100% {
              opacity: 1;
            }
          }
        }
        .tit {
          position: relative;
          display: flex;
          align-items: center;
          font-size: 32px;
          color: #204eca;
          font-weight: 700;
          .lebel {
            position: absolute;
            right: 90px;
            font-size: 24px;
            color: #333;
            display: inline-block;
            transform: translateY(-2px);
            font-weight: normal;
          }
          .icon-s {
            width: 32px;
            height: 32px;
            vertical-align: middle;
            margin-right: 10px;
          }
        }
        .li {
          display: flex;
          // justify-content: space-between;
          margin-top: 10px;
          font-size: 26px;
          .setting {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 6px 12px;
            background-color: #dc7731;
            box-sizing: border-box;
            margin-left: 15px;
            border-radius: 16px;
            .icon-setting1 {
              font-size: 24px;
              color: #fff;
            }
          }
          .log {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 5px 10px;

            background: #5b7dd8;
            border-radius: 16px;
            color: #fff;
            // font-size: 26px;
            // margin-left: 20px;
            font-size: 22px;
            margin-left: 40px;
          }
          .ri {
            padding-left: 70px;
            display: flex;
            align-items: center;
            .check {
              position: absolute;
              right: 0;
            }
            .at-checkbox {
              &::after {
                content: none;
              }
              &::before {
                content: none;
              }
            }
            .at-checkbox__title {
              font-size: 28px;
              color: #204eca;
              margin-left: 40px;
              font-size: 32px;
              font-weight: 700;
            }
            .at-checkbox__option-wrap {
              padding: 0;
            }
            .at-checkbox__option {
              padding-left: 0;
            }
            .at-checkbox__option-cnt {
              background-color: #fff !important;
              align-items: center;

              &:active {
                background-color: #fff;
              }
            }

            .at-checkbox__option--selected {
              .at-checkbox__icon-cnt {
                background-color: #5bc650;
              }
            }

            .at-checkbox__icon-cnt {
              // width: 25px;
              // height: 25px;
              // min-width: 10px;
            }
          }
          .le {
            color: #090909;
            display: flex;
            align-items: center;
            width: 61%;
            .iconfont {
              font-size: 24px;
              color: #6f7c9a;
              margin-right: 5px;
            }
            .sn {
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: 240px;
              white-space: nowrap;
            }
            .online {
              margin-left: 5px;
            }
            .ed {
              display: flex;
              justify-content: center;
              align-items: center;
              background-color: #3c5ec7;
              padding: 1px 8px;
              border-radius: 20px;
              margin-left: 10px;
              color: #fff;
              font-size: 22px;
            }
            .to {
              display: flex;
              justify-content: center;
              align-items: center;
              background-color: #fff;
              padding: 1px 8px;
              border-radius: 20px;
              margin-left: 10px;
              color: #0072ff;
              font-size: 22px;
              border: 1px solid #0072ff;
            }
          }
          .ri {
            color: #0072ff;
            // margin-left: 20px;
          }
        }
      }
    }
  }
  .utils {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding-left: 72px;
    button {
      width: 454px;
    }
  }
}
</style>
