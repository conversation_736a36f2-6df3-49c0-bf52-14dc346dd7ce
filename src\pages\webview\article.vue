<template>
  <web-view v-if="url" :src="url"></web-view>
</template>
<script setup>
import { ref } from "vue";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
import request from "@/utils/request";
definePageConfig({
  navigationBarTitleText: '',
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

const detail = ref({
  title: '',
  content: ''
})
const key = ref('')
const url = ref('')

const getDetail = () => {
  request.get({
    url: 'article/detail?article_key=' + key.value,
  }).then(res => {

    detail.value = {
      title: res.data.title,
      content: res.data.content
    }
    Taro.setNavigationBarTitle({
      title: res.data.title,
    })
  })
}

useLoad((options) => {
  key.value = options.key
  url.value = 'https://f.ts.yimitongxun.com/h5/article?article_key=' + key.value
  getDetail()
})

</script>
