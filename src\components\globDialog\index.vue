<script setup>
import { computed, ref, watch, } from 'vue';
import { useGlobalStore } from "@/stores";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
  useUnload
} from '@tarojs/taro'


const props = defineProps({
  // maskClose: {
  //   type: Boolean,
  //   default: true,
  // },
  timeS: {
    type: Number,
    default: 13
  },
  setModeMsg: {
    type: String,
    default: '正在设置中...请稍等!'
  },
  device: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['confirm','reloadMainHandel']);

const maskClose = ref(true);

const locaImg = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/location.png'
const bleImg = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/ble.png'
const bleTipImg = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/ble-tip.png'
const erroImg = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/con-erro.png'
const conErroImg = 'https://yimits.oss-cn-beijing.aliyuncs.com/miniProgram/pic/connet-fail-2x.png'
const reashN = 'https://yimits.oss-cn-beijing.aliyuncs.com/miniProgram/pic/reash-n-2x.png'
const reashW = 'https://yimits.oss-cn-beijing.aliyuncs.com/miniProgram/pic/reash-w-2x.png'
const loadingImg = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/time-loading.png'


const globalStore = useGlobalStore();

const timeDefaSeMode = ref(props.timeS)//初始化设置mode倒计时时间

const countTImer = ref(null)

//全局弹窗的类型 PermissionDenied(获取权限失败)  | ConnectionFailed（连接失败） |  TimeLoading（连接倒计时） | BleConnectionTip(蓝牙表链接前提示) | setModeTimeLoading（4g设置模式倒计时） | setModeFailed  (设置mode失败)
const type = ref('')

const isShow = ref(false)
const isNoShow  = ref(false)//充值失败后不在改变弹窗

// const type = computed(() => globalStore.globDialogType);

watch(() => globalStore.globDialogType, (newVal) => {
  if (newVal == 'TimeLoading' || newVal == 'BleConnectionTip' || newVal == 'RefFailed' || newVal == 'TimeReFLoading') {
    maskClose.value = false
  } else {
    maskClose.value = true
  }

  if (newVal == 'setModeTimeLoading') { 
    countTImer.value = setInterval(() => {
      if (timeDefaSeMode.value > 0) {
        timeDefaSeMode.value--
      } else {
        globalStore.setGlobDialog({show:false,type:''})
        globalStore.setGlobDialog({show:true,type:'setModeFailed'})
        timeDefaSeMode.value = props.timeS
        clearInterval(countTImer.value)
      }
    },1000)
  }
  if (newVal === 'RefFailed' || isNoShow.value) {
    isNoShow.value = true
    setTimeout(() => {
      type.value = 'RefFailed'
    }, 310)
    return
  } 
  type.value = newVal
},{immediate:true})

watch(() => globalStore.isGlobVisable, (newVal) => {
  if (!newVal) { 
    clearInterval(countTImer.value)
    timeDefaSeMode.value = props.timeS
  }
  if (type.value === 'RefFailed' || isNoShow.value) {
    isNoShow.value = true
    return
  } 
  isShow.value = newVal
})


const maskHandel = (e) => {
  if (e.mpEvent.target.id != 'mask') return
  if (type.value == 'PermissionDenied') {
    // globalStore.setGlobDialog(false, "")
  }
}

const clickUrlHandel = () => {
  Taro.redirectTo({
    url: '/pages/qa/index'
  })
}

const clickYesHandel = () => {
  globalStore.setIndexRecharegeId(null)
  globalStore.setIndexPageScrollTop(0)
  if (type.value == 'PermissionDenied') {
    globalStore.setGlobDialog(false, "")
    return 
  }
  if (type.value == 'BleConnectionTip') {
    globalStore.setGlobDialog(false, "")
    return emit('confirm')
  }
  if (type.value == 'setModeFailed') {
    globalStore.setGlobDialog(false, "")
    return emit('confirm')
  }
  globalStore.setGlobDialog(false, "")
  isNoShow.value = false
  // emit('confirm')
  Taro.reLaunch({
    url: '/pages/index/index'
  })
}

</script>

<template>
  <view v-if="isShow" class="global-dialog-component-mask" @tap.stop="maskHandel" id="mask">
    <view class="global-dialog-component"
      :style="{ height: type == 'BleConnectionTip' ? '600rpx' : type == 'ConnectionFailed' ? '622rpx' : type == 'RefFailed' ? '670rpx' : type == 'PermissionDenied' ? '650rpx' : '558rpx' }">
      <!-- PermissionDenied -->
      <view v-if="type == 'PermissionDenied'" class="PermissionDenied">
        <view class="img-box">
          <image :src="locaImg"></image>
          <image :src="bleImg"></image>
        </view>
        <view class="title">
          蓝牙和定位<text>未打开</text>
        </view>
        <view class="desc">
          请关闭小程序并打开手机蓝牙和定位功能后,
          重启小程序重新操作
        </view>
        <view class="btn-box">
          <view @tap="clickYesHandel" style="color: #000;">我知道了</view>
        </view>
      </view>
      <!-- ConnectionFailed -->
      <view v-if="type == 'ConnectionFailed'">
        <view class="ConnectionFailed">
          <!-- 网络刷新 -->
          <view class="reload-btn" @tap="emit('reloadMainHandel')" v-if="props?.device?.client_id && props?.device?.is_master == 0 && props?.device?.signal_num == 0">
            <!-- <text class="iconfont icon-shuaxin"></text>  -->
             <view class="img">
               <image
                :src="reashN"
                mode="scaleToFill"
                class="reashN"
                />
              <image
                :src="reashW"
                mode="scaleToFill"
                class="reashW"
              />
             </view>
            <text class="text-v-center">刷新网络</text>
          </view>
          <image :src="conErroImg" mode="aspectFill"></image>
          <view class="title">
            设备连接失败
          </view>
          <view class="desc">
            <view> 1、请确保与设备保持了3米内距离</view>
            <view> 2、关闭小程序重启后进行操作</view>
          </view>
          <!-- 按钮 -->
          <view class="btn-box">
            <view class="btn1" @tap="clickUrlHandel">查看教程</view>
            <view @tap="clickYesHandel">我知道了</view>
          </view>
        </view>
      </view>
      <!-- waterConnectionFailed -->
      <view v-if="type == 'waterConnectionFailed'">
        <view class="waterConnectionFailed">
          <!-- 网络刷新 -->
          <view class="reload-btn" @tap="emit('reloadMainHandel')" v-if="props?.device?.client_id && props?.device?.is_master == 0 && props?.device?.signal_num == 0">
            <!-- <text class="iconfont icon-shuaxin"></text>  -->
              <view class="img">
              <image
                :src="reashN"
                mode="scaleToFill"
                class="reashN"
                />
              <image
                :src="reashW"
                mode="scaleToFill"
                class="reashW"
              />
            </view>
            <text class="text-v-center">刷新网络</text>
          </view>
          <image :src="conErroImg" mode="aspectFill"></image>
          <view class="title">
            水表连接失败
          </view>
          <view class="desc">
            <view> 1、请确保与水表保持了3米内距离</view>
            <view> 2、关闭小程序重启后进行操作</view>
          </view>
          <!-- 按钮 -->
          <view class="btn-box">
            <view class="btn1" @tap="clickUrlHandel">查看教程</view>
            <view @tap="clickYesHandel">我知道了</view>
          </view>
        </view>
      </view>
      <!-- RefFailed -->
      <view v-if="type == 'RefFailed'">
        <view class="RefFailed">
          <image :src="erroImg" mode="aspectFill"></image>
          <view class="title">
            设备充值失败
          </view>
          <view class="desc">
            <view> 1、电量充值失败，请退出小程序重新操作</view>
            <view> 2、补充电量不再扣费，联系客服: {{ globalStore?.userInfo?.service_te || globalStore?.userInfo?.agent?.mobile || '************'}}</view>
          </view>
          <!-- 按钮 -->
          <view class="btn-box">
            <!-- <view class="btn1" @tap="clickUrlHandel">查看教程</view> -->
            <view @tap="clickYesHandel">我知道了</view>
          </view>
        </view>
      </view>
      <!-- setModeFailed -->
      <view v-if="type == 'setModeFailed'">
        <view class="setModeFailed">
          <image :src="erroImg" mode="aspectFill"></image>
          <view class="title">
            设备设置模式失败
          </view>
          <view class="desc">
            <view> 1、确认设备是否在线重新操作</view>
          </view>
          <!-- 按钮 -->
          <view class="btn-box">
            <!-- <view class="btn1" @tap="clickUrlHandel">查看教程</view> -->
            <view @tap="clickYesHandel">我知道了</view>
          </view>
        </view>
      </view>
      <!-- TimeLoading -->
      <view v-if="type == 'TimeLoading'">
        <view class="TimeLoading">
          <view class="title">
            倒计时
          </view>
          <image :src="loadingImg"></image>
          <view class="times">
            <text class="s">{{ globalStore.deviceConTime }}</text>
            <text class="m">秒</text>
          </view>
          <view class="desc">
            正在连接中...请稍等!
          </view>
        </view>
      </view>
      <!-- TimeReFLoading -->
      <view v-if="type == 'TimeReFLoading'">
        <view class="TimeReFLoading">
          <view class="title">
            倒计时
          </view>
          <image :src="loadingImg"></image>
          <view class="times">
            <text class="s">{{ globalStore.deviceRefTime }}</text>
            <text class="m">秒</text>
          </view>
          <view class="desc">
            正在充值中...请稍等!
          </view>
        </view>
      </view>
      <!-- setModeTimeLoading -->
      <view v-if="type == 'setModeTimeLoading'">
        <view class="setModeTimeLoading">
          <view class="title">
            倒计时
          </view>
          <image :src="loadingImg"></image>
          <view class="times">
            <text class="s">{{ timeDefaSeMode }}</text>
            <text class="m">秒</text>
          </view>
          <view class="desc">
            {{ setModeMsg }}
          </view>
        </view>
      </view>
      <!-- BleConnectionTip -->
      <view v-if="type == 'BleConnectionTip'">
        <view class="BleConnectionTip">
          <image :src="bleTipImg" mode="aspectFill"></image>
          <view>请与设备保持3米内距离进行操作</view>
          <view class="btn-box">
            <view @tap="clickYesHandel" style="color: #000;">我知道了</view>
          </view>
        </view>
      </view>
      <!-- 未知 -->
      <view v-if="type == ''">
        未知
      </view>
    </view>
  </view>
</template>


<style lang="scss">
.global-dialog-component-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  overflow: hidden;

  .global-dialog-component {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 679px;
    height: 558px;
    background: #FFFFFF;
    border-radius: 30px;
    color: #000;
    z-index: 99999;
    box-sizing: border-box;
    animation: open 0.3s forwards;

    @keyframes open {
      0% {
        opacity: 0;
        transform: translate(-50%, -30%);
      }

      100% {
        opacity: 1;
        transform: translate(-50%, -50%);
      }
    }

    .TimeLoading {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;

      .times {
        position: absolute;
        left: 50%;
        top: 235px;
        transform: translateX(-50%);

        .s {
          font-weight: bold;
          font-size: 48px;
          color: #000000;
        }

        .b {
          font-weight: 500;
          font-size: 30px;
          color: #000000;
          vertical-align: middle;
        }
      }

      .title {
        font-weight: bold;
        font-size: 40px;
        color: #000000;
        margin-top: 35px;
        margin-bottom: 60px;
      }

      @keyframes loading_ {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }


      image {
        width: 234px;
        height: 236px;
        animation: loading_ 1s linear infinite;
      }

      .desc {
        font-weight: 400;
        font-size: 40px;
        color: #000000;
        margin-top: 69px;
      }

    }

    .TimeReFLoading ,.setModeTimeLoading{
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;

      .times {
        position: absolute;
        left: 50%;
        top: 235px;
        transform: translateX(-50%);

        .s {
          font-weight: bold;
          font-size: 48px;
          color: #000000;
        }

        .b {
          font-weight: 500;
          font-size: 30px;
          color: #000000;
          vertical-align: middle;
        }
      }

      .title {
        font-weight: bold;
        font-size: 40px;
        color: #000000;
        margin-top: 35px;
        margin-bottom: 60px;
      }

      @keyframes loading_ {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }


      image {
        width: 234px;
        height: 236px;
        animation: loading_ 1s linear infinite;
      }

      .desc {
        font-weight: 400;
        font-size: 40px;
        color: #000000;
        margin-top: 69px;
      }
    }

    .PermissionDenied {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;

      .img-box {
        display: flex;
        align-items: center;
        margin-top: 130px;

        image {
          width: 125px;
          height: 125px;

          &:nth-child(1) {
            margin-right: 98px;
          }
        }
      }

      .title {
        font-weight: bold;
        margin-top: 93px;
        margin-bottom: 45px;
        font-size: 38px;
        color: #000000;

        text {
          color: #e31d1d;
        }
      }

      .desc {
        font-weight: 400;
        font-size: 30px;
        color: #000000;
        width: 85%;
        text-align: center;
      }
      .btn-box {
        position: absolute;
        left: 0;
        bottom: 0;
        display: flex;
        justify-content: center;
        border-top: 1px solid #dcdcdc;
        width: 100%;
        height: 100px;
        line-height: 100px;

        view {
          color: #0173FF;
          text-align: center;
          width: 50%;

          &:active {
            opacity: .85;
          }
        }

        .btn1 {
          border-right: 1px solid #dcdcdc;
          color: #000;
        }
      }
    }

    .BleConnectionTip {
      padding-top: 83px;
      text-align: center;
      font-size: 40px;

      image {
        width: 558px;
        height: 223px;
        margin-bottom: 89px;
      }

      .btn-box {
        position: absolute;
        left: 0;
        bottom: 0;
        display: flex;
        justify-content: center;
        border-top: 1px solid #dcdcdc;
        width: 100%;
        height: 100px;
        line-height: 100px;

        view {
          color: #0173FF;
          text-align: center;
          width: 50%;

          &:active {
            opacity: .85;
          }
        }

        .btn1 {
          border-right: 1px solid #dcdcdc;
          color: #000;
        }
      }
    }

    .ConnectionFailed ,.waterConnectionFailed{
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 75px;
       .reload-btn {
        position: absolute;
        right: 30px;
        top: 10%;
        transform: translateY(-50%);
        display: flex;
        justify-content: center;
        align-items: center;
        color: #000;
        flex-direction: column;
        .iconfont {
          font-size: 40px;
          color: #000;
          margin-bottom: 0;
        }
        text {
          margin-bottom: 2px;
          display: block;
          font-weight: bold;
          font-size: 20px;
        }
        .img {
          position: relative;
           width: 62px;
          height: 62px;
        }
        .reashW {
          width: 62px;
          height: 62px;
          margin-bottom: 0;
        }
        .reashN{
          position: absolute;
          left: 52%;
          top: 50%;
          transform: translate(-50%,-50%);
          width: 28px;
          height: 20px;
          margin-bottom: 0;
        }
      }

      image {
        width: 151px;
        height: 123px;
        margin-bottom: 44px;
      }

      .title {
        font-weight: bold;
        font-size: 40px;
        color: #000000;
        margin-bottom: 42px;
      }

      .desc {
        font-weight: 400;
        font-size: 40px;
        color: #000000;
      }

      .btn-box {
        position: absolute;
        left: 0;
        bottom: 0;
        display: flex;
        justify-content: space-between;
        border-top: 1px solid #dcdcdc;
        width: 100%;
        height: 100px;
        line-height: 100px;

        view {
          color: #0173FF;
          text-align: center;
          width: 50%;

          &:active {
            opacity: .85;
          }
        }

        .btn1 {
          border-right: 1px solid #dcdcdc;
          color: #000;
        }
      }
    }

    .RefFailed,.setModeFailed {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 75px;

      image {
        width: 168px;
        height: 168px;
        margin-bottom: 44px;
      }

      .title {
        font-weight: bold;
        font-size: 40px;
        color: #000000;
        margin-bottom: 57px;
      }

      .desc {
        font-weight: 400;
        font-size: 35px;
        color: #000000;
        padding: 0 15px;

        view {
          &:nth-child(2) {
            margin-top: 10px;
          }
        }
      }

      .btn-box {
        position: absolute;
        left: 0;
        bottom: 0;
        display: flex;
        justify-content: center;
        border-top: 1px solid #dcdcdc;
        width: 100%;
        height: 100px;
        line-height: 100px;

        view {
          color: #0173FF;
          text-align: center;
          width: 50%;

          &:active {
            opacity: .85;
          }
        }

        .btn1 {
          border-right: 1px solid #dcdcdc;
          color: #000;
        }
      }
    }
  }
}
</style>
