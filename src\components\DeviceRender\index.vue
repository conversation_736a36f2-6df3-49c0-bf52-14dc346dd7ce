<template>
  <view class="device-list">
    <!-- <view class="no-data">暂无信息</view> -->
    <view
      class="device-item"
      v-for="(device,idx) in transItmes"
      :key="idx"
      :style="{'border-color': device.device[0]?.signal <= 0 && device.device[0]?.net_type == 1 ? 'red' : '#1452fd'}"
      @tap.stop="onDetail(device, $event)"
    >
      <view class="flex device-top">
        <!-- <view class="fast-box" @tap.stop="fastCheckHandel(device)" v-if="globalStore.who != 'tenant'">
          <image src="@/assets/images/fastimg.png" mode="aspectFill" />
          <text class="text" v-if="calculateDaysBetweenDates(new Date(), device?.house?.fast_contract?.expired_at) >= 0 && !isNaN(calculateDaysBetweenDates(new Date(), device?.house?.fast_contract?.expired_at))">租期剩余{{ calculateDaysBetweenDates(new Date(), device?.house?.fast_contract?.expired_at) }}天</text>
          <text class="text" style="color: red;" v-if="calculateDaysBetweenDates(new Date(), device?.house?.fast_contract?.expired_at) < 0 && !isNaN(calculateDaysBetweenDates(new Date(), device?.house?.fast_contract?.expired_at))">租期逾期{{ Math.abs(calculateDaysBetweenDates(new Date(), device?.house?.fast_contract?.expired_at)) }}天</text>
        </view> -->

        <HomeIocnType
          type="1"
          :device="device.device[0]"
          :waterDevice="device.waterDevice[0]"
          :fast_contract="device?.contracts ||{}"
          :curentType="device.curentType"
          :detail="device"
        />
        <!-- 门锁 -->
        <HomeIocnType
          type="2"
          :device="device.device[0]"
          :waterDevice="device.waterDevice[0]"
          :fast_contract="device?.contracts"
          @switchType="updateCurentType(device.device[0]?.id || device.ttlock[0]?.lock_id, 2)"
          :curentType="getCurentType(device.device[0]?.id || device.ttlock[0]?.lock_id, device.device, device.waterDevice)"
          :detail="device"
          v-if="!isAp"
        />
        <!-- 水表 -->
        <HomeIocnType
          type="3"
          :device="device.device[0]"
          :waterDevice="device.waterDevice[0]"
          :fast_contract="device?.contracts"
          @switchType="updateCurentType(device.device[0]?.id || device.ttlock[0]?.lock_id, 3)"
          :curentType="getCurentType(device.device[0]?.id || device.ttlock[0]?.lock_id, device.device, device.waterDevice)"
          :detail="device"
        />
        <!-- 电表 -->
        <HomeIocnType
          type="4"
          :device="!device.device[0] ? false : device.device[0]"
          :waterDevice="device.waterDevice[0]"
          :fast_contract="device?.contracts"
          @switchType="updateCurentType(device.device[0]?.id || device.ttlock[0]?.lock_id, 4)"
          :curentType="getCurentType(device.device[0]?.id || device.ttlock[0]?.lock_id, device.device, device.waterDevice)"
          :detail="device"
        />

        <view style="flex: 1" v-if="device.device[0]">
          <view class="device-name" v-if="globalStore.who == 'tenant'">电表</view>
          <view class="device-sn" v-if="globalStore.who == 'tenant'">
            <text v-if="device.device[0].net_type === 1">4G双模电表号</text
            ><text v-if="device.device[0].net_type !== 1">蓝牙电表号</text>:
            {{ device.device[0].sn }}
          </view>
        </view>
        <view style="width: auto; display: flex; align-items: center">
          <!-- <view class="close-box" v-if="((device.device[0].du || 0) < 0.6) && device.device[0].status == 2">
            欠费断电
          </view> -->
          <!-- marginRight:device.net_type !== 1 && globalStore.who != 'tenant' ? '12rpx' : device.net_type == 1 && globalStore.who != 'tenant' ? '-12rpx' : device.net_type == 1 && globalStore.who == 'tenant' ? '482rpx' : '559rpx' -->
          <!-- <view class="device-icon cus" :style="{marginRight:device.net_type !== 1 ? '12rpx' : '-10rpx'}">
            <MyIcon icon="icon-dianbiao" width="52rpx"></MyIcon>
            <text class="sn-text" :style="{marginLeft:device.net_type !== 1 ? '-30rpx' : '5rpx'}"> {{ device.sn }}</text>
          </view>
          <text class="label" v-if="device.net_type === 1">4G</text>
          <text class="label" v-if="device.net_type !== 1"></text>
          <MyIcon
            :icon="'signal/' + device.signal_num"
            width="60rpx"
            height="49rpx"
            style="margin-left: 0rpx"
            v-if="device.net_type === 1"
          ></MyIcon> -->
        </view>
      </view>

      <!-- <view class="device-info flex flex-space-between flex-v-center">
        <view>
          <view class="device-num"
            ><text class="du-t">{{ device.total || 0 }}</text>
            <text>度</text></view
          >
          <view class="device-lab">总电量</view>
        </view>
        <view>
          <view class="device-num"
            ><text class="du-t">{{ device.du || 0 }}</text
            ><text>度</text></view
          >
          <view class="device-lab">剩余电量</view>
        </view>
        <view>
          <view class="device-num">{{ device.price }}<text>元/度</text></view>
          <view class="device-lab">单价</view>
        </view>
      </view> -->

      <itemHeperlBusiness
        :device="device.device[0]"
        :waterDevice="device.waterDevice[0]"
        :detail="device"
        :fast_contract="device?.contracts"
        :curentType="getCurentType(device.device[0]?.id || device.ttlock[0]?.lock_id, device.device, device.waterDevice)"
      />

      <view style="padding: 10rpx 20rpx 0 20rpx" class="btm-b" :style="device.house_name.length >= 10 ? 'flex-wrap: wrap;' : ''">
        <view class="le">
          <!-- <MyIcon icon="icon-house-mini" width="30rpx" height="26rpx"></MyIcon> -->
          <image
            src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
            mode="scaleToFill"
            class="icon-s"
          />
          <text @tap.stop="editHoseName(device)">
            <text class="text-v-center font-28 _name" :style="device.house_name.length >= 10 ? 'max-width: 548rpx;' : ''" v-if="device.house_name">{{
              device.house_name
            }}</text>
            <text
              v-if="device.house_name && globalStore.who != 'tenant'"
              class="iconfont icon-bianji"
              style="
                font-size: 35rpx;
                display: inline-block;
                margin: 0 0 0 10rpx;
                transform: translateY(-6rpx);
                font-weight: 400;
                color: #333;
              "
            ></text>
          </text>
          <text class="text-v-center font-28 _name" v-if="!device.house_name"
            >未绑定房源</text
          >
        </view>
        <!-- 滚动区域 -->
         <scroll-view :scroll-x="true" :show-scrollbar="false" enhanced style="height: 100%;width: 400rpx;" :style="device.house_name.length >= 10 ? 'width: 100%' : 'width: 400rpx'" class="scrollview">
          <view class="scrllo-right-content" :style="justifyContentStyles(device.device[0],getCurentType(device.device[0]?.id || device.ttlock[0]?.lock_id, device.device, device.waterDevice) == 4,device.waterDevice[0])">
            <view class="recharge-tip" v-if="getCurentType(device.device[0]?.id || device.ttlock[0]?.lock_id, device.device, device.waterDevice) == 4 && device.device[0]?.online_status != 2 && getDaysDifference(device.device[0]?.net_type == 1 ? (device.device[0]?.recharge_callback_at || '') : (device.device[0]?.read_at || '')) <= rechareDay ">
              {{ Math.abs(getDaysDifference(device.device[0]?.net_type == 1 ? (device.device[0]?.recharge_callback_at || '') : (device.device[0]?.read_at || ''))) }}天未充{{ device.device[0].type == 2 ? '水' : '电' }}费！
            </view>
            <!-- 水表未充值 -->
            <view class="recharge-tip" v-if="getCurentType(device.device[0]?.id || device.ttlock[0]?.lock_id, device.device, device.waterDevice) == 3 && device.waterDevice[0]?.online_status != 2 && getDaysDifference(device.waterDevice[0]?.net_type == 1 ? (device.waterDevice[0]?.recharge_callback_at || '') : (device.waterDevice[0]?.read_at || '')) <= rechareWaterDay ">
              {{ Math.abs(getDaysDifference(device.waterDevice[0]?.net_type == 1 ? (device.waterDevice[0]?.recharge_callback_at || '') : (device.waterDevice[0]?.read_at || ''))) }}天未充{{ device.waterDevice[0].type == 2 ? '水' : '电' }}费！
            </view>
            <!-- 电表 -->
            <view class="close-box" v-if="device.device[0] && ((device.device[0].du || 0) < 0.6) && device.device[0].status == 2 && getCurentType(device.device[0].id) == 4">
              欠费断电
            </view>
            <!-- 水表 -->
            <view class="close-box" v-if="device.waterDevice[0] && ((device.waterDevice[0].du || 0) < 0.01) && device.waterDevice[0].status == 2 && getCurentType(device.device[0].id) == 3">
              欠费断水
            </view>
            <!-- <view class="more-device haodian" @tap.stop="toMoreDevice(device)" v-if="device.device.length > 1">
              <text>更多设备 </text>
              <image
                class="ico-r"
                src="https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-right-white.png"
                mode="scaleToFill"
              />
            </view> -->
            <view
              class="haodian"
              :data-ignore="true"
              v-if="getCurentType(device.device[0]?.id || device.ttlock[0]?.lock_id, device.device, device.waterDevice) == 4 && device.device[0] && device.device[0].net_type === 1 && device.device[0].signal_num > 0"
              @tap.stop="toHaoDianDetailHandel(device.device[0])"
            >
              <image
                class="ico-l"
                src="https://yimits.oss-cn-beijing.aliyuncs.com/images/haodian.png"
                mode="scaleToFill"
                v-if="device.device[0] && device.device[0].type != 2"
              />
              <text>{{ device.device[0] && device.device[0].type == 2 ? '耗水记录' : '耗电记录'}} </text>
              <image
                class="ico-r"
                src="https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-right-white.png"
                mode="scaleToFill"
              />
            </view>
            <!-- 水表 -->
            <view
              class="haodian"
              :data-ignore="true"
              v-if="getCurentType(device.device[0]?.id || device.ttlock[0]?.lock_id, device.device, device.waterDevice) == 3 && device.waterDevice[0] && device.waterDevice[0].net_type === 1 && device.waterDevice[0].signal_num > 0"
              @tap.stop="toHaoDianDetailHandel(device.waterDevice[0])"
            >
              <image
                class="ico-l"
                src="https://yimits.oss-cn-beijing.aliyuncs.com/images/haodian.png"
                mode="scaleToFill"
                v-if="device.waterDevice[0] && device.waterDevice[0].type != 2"
              />
              <text>{{ device.waterDevice[0] && device.waterDevice[0].type == 2 ? '耗水记录' : '耗电记录'}} </text>
              <image
                class="ico-r"
                src="https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-right-white.png"
                mode="scaleToFill"
              />
            </view>
            <!-- 添加设备按钮 -->
            <view class="add-water-device" @tap.stop="addDevice(device)" v-if="getCurentType(device.device[0]?.id || device.ttlock[0]?.lock_id, device.device, device.waterDevice) == 3">
              <text class="iconfont icon-jiahao1"></text>
            </view>
          </view>
         </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup>
import MyIcon from "@/components/MyIcon";
import Taro from "@tarojs/taro";
import request from "@/utils/request";
import { ref, computed } from "vue";
import HomeIocnType from "@/components/homeIconType";
import itemHeperlBusiness from "./itemHeperlBusiness.vue";
import { getDaysDifference } from '@/utils/index.js'
const curentType = ref(4);

const isAp = process.env.TARO_ENV === 'alipay'

const rechareDay = ref(-10)//未充值10天
const rechareWaterDay = ref(-30)//未充值30天


import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

const props = defineProps({
  items: {
    type: Array,
    required: false,
    default() {
      return [];
    },
  },
});

const addDevice = (item) => {
  console.log(item);
  Taro.navigateTo({
    url: "/pages/house/addDevice/addDevice?house_id=" + item.id + '&type=' + 2,
  });
}

// 根据device数组平铺数据
// const transItmes = computed(() => {
//   const data = props.items;
//   const result = [];

//   data.forEach(item => {
//     const devicesType1 = item.device.filter(dev => dev.type === 1);
//     const devicesType2 = item.device.filter(dev => dev.type === 2);

//     if (devicesType1.length > 0) {
//       devicesType1.forEach(device => {
//         result.push({ ...item, device: [device], waterDevice: [] }); // 初始化waterDevice为空数组
//       });
//     } 
//     if (devicesType2.length > 0) {
//       devicesType2.forEach(device => {
//         // 如果type 1的设备存在，则将type 2的设备添加到已有的条目中
//         if (devicesType1.length > 0) {
//           const lastItemIndex = result.length - 1;
//           while (lastItemIndex >= 0 && !('device' in result[lastItemIndex]))
//           {
//             lastItemIndex--;
//           }

//            if (lastItemIndex >= 0)
//            {
//               if(!result[lastItemIndex].waterDevice) result[lastItemIndex].waterDevice = []
//               result[lastItemIndex].waterDevice.push(device)
//            } else
//            {
//              result.push({ ...item, device: [], waterDevice: [device] });
//            }

//         } else {
//           // 如果type 1的设备不存在，则创建一个新的条目，device为空数组
//           result.push({ ...item, device: [], waterDevice: [device] });
//         }
//       });
//     }
//     if (devicesType1.length === 0 && devicesType2.length === 0) {
//       result.push({ ...item, device: [], waterDevice: [] }); // 两个类型都没有
//     }
//   });
// console.log(result,"result");

//   return result;
// });

const transItmes = computed(() => {
  const data = props.items;
  const result = [];
 // 1电表-单相表，2水表，3三相表-直通式 4三相表-互感式
  data.forEach(item => {
    const devicesType1 = item.device.filter(dev => dev.type != 2);//电表
    const devicesType2 = item.device.filter(dev => dev.type === 2).map(item2 => {
      return {
        ...item2,
        house: {
          name: item.house_name,
          id: item.id,
        },
      }
    });//水表

    if (devicesType1.length > 0) {
      devicesType1.forEach(device1 => {
      // TODO 需要处理有一个电表有多个水表的场景 或者2个电表三个水表的场景类推
        const newItem = { ...item, device: [device1] };
        if (devicesType2.length > 0) {
          newItem.waterDevice = devicesType2; // 直接添加所有 type 2 设备
        } else {
          newItem.waterDevice = [];
        }
        // 查询是否有id的设备有就删除之前的
        const index = result.findIndex(item => item.device[0]?.id === newItem.device[0]?.id);
        if (index !== -1) {
          result.splice(index, 1);
        }
        // 添加新的设备
        result.push(newItem);
      });
    } else if (devicesType2.length > 0) {
      // TODO 没有电表但有水表-目前这个场景不存在
      // 查询是否有id的设备有就删除之前的
      const index = result.findIndex(item => item.waterDevice[0]?.id === devicesType2[0]?.id);
        if (index !== -1) {
          result.splice(index, 1);
        }
        // 添加新的设备
      result.push({ ...item, device: [], waterDevice: devicesType2 });
    } else {
      // result.push({ ...item, device: [], waterDevice: [] });
    }
  });
  console.log(result, "result-tras");

  return result;
});


// 创建响应式的样式计算函数
const justifyContentStyles = computed(() => {
  // 这里我们依赖 rechareDay 和 rechareWaterDay，当它们变化时会触发重新计算
  const rechareeDayValue = rechareDay.value;
  const rechareeWaterDayValue = rechareWaterDay.value;
  let day = rechareeDayValue;
  let result = 'justify-content: flex-end';
  
  return (deviceData,isDianDevice,waterDevice) => {
    if (isDianDevice) {
      // 当前tab是电表
      if (!deviceData) return 'justify-content: flex-end';
      const condition1 = getDaysDifference(deviceData.recharge_callback_at || '') <= day && deviceData.net_type == 1 && deviceData.online_status != 2;
      const condition2 = deviceData.net_type === 1 && deviceData.signal_num > 0;
      const condition3 = (deviceData.du || 0) < 0.6 && deviceData.status == 2;
      const trueCount = [condition1, condition2, condition3].filter(Boolean).length;
      console.log([condition1, condition2, condition3],"arr",trueCount, "trueCount", deviceData.sn || deviceData.id, 'day:', day);
      result = trueCount <= 1 ? 'justify-content: flex-end' : 'justify-content: space-between';
      console.log('🎯 返回样式:', result, '设备:', deviceData.sn || deviceData.id,day,deviceData.type);
    } else {
      // 当前tab是水表
      if (!waterDevice) return 'justify-content: flex-end';
      day = rechareeWaterDayValue;
      const condition1 = getDaysDifference(waterDevice.recharge_callback_at || '') <= day && waterDevice.net_type == 1 && deviceData.online_status != 2;
      const condition2 = waterDevice.net_type === 1 && waterDevice.signal_num > 0;
      const condition3 = (waterDevice.du || 0) < 0.6 && waterDevice.status == 2;
      const trueCount = [condition1, condition2, condition3].filter(Boolean).length;
      console.log([condition1, condition2, condition3],"arr",trueCount, "trueCount", waterDevice.sn || waterDevice.id, 'day:', day);
      result = trueCount <= 1 ? 'justify-content: flex-end' : 'justify-content: space-between';
      console.log('🎯 返回样式:', result, '设备:', waterDevice.sn || waterDevice.id,day,waterDevice.type);
    }

    return result;
  };
});

const curentTypes = ref({}); // 使用一个对象来存储每个 device 的 curentType

const getCurentType = (deviceId, device, waterDevice) => {
  // 如果水表有一个以上 并且没有电表 默认展示水表
  if (waterDevice && waterDevice.length > 0 && device.length <= 0) {
    return curentTypes.value[deviceId] || 3
  }
  return curentTypes.value[deviceId] || 4
}; // 默认值为 4

const updateCurentType = (deviceId, newType) => {
  console.log(deviceId);
  curentTypes.value[deviceId] = newType;
  console.log(curentTypes.value);
};

function calculateDaysBetweenDates (date1, date2) {
    if(!date1 || !date2) return NaN
  // 将日期转换为当天的 00:00:00
  const d1 = new Date(date1);
  d1.setHours(0, 0, 0, 0);
  
  const d2 = new Date(date2);
  d2.setHours(0, 0, 0, 0);

  const dayInMs = 24 * 60 * 60 * 1000;
  const diffTime = d2.getTime() - d1.getTime();
  const diffDays = Math.round(diffTime / dayInMs);
  
  return diffDays;
} 

const emit = defineEmits(["detail"]);

const editHoseName = (item) => {
  emit("editHoseName", item);
};

const onDetail = (item, e) => {
  console.log(item);
  // console.log(getCurentType(item.device[0].id));
  if (item.waterDevice.length > 0 && item.device.length && getCurentType(item.device[0].id) == 3) {
    emit("detail", item.waterDevice[0].id);
    return
  }
  if (item.waterDevice.length > 0 && item.device.length <= 0) {
    emit("detail", item.waterDevice[0].id);
    return
  }
  emit("detail", item.device[0].id);
};

const device = ref();

// 跳转快捷入驻
const fastCheckHandel = (item) => {
  request
    .get({
      url: "device/" + item.id,
    })
    .then((res) => {
      device.value = res.data;
      const obj = {
        id: device.value.id,
        need_people: device.value.need_people,
        sn: device.value.sn,
      };
      Taro.navigateTo({
        url: "/pages/device/fastCheck/fastCheck?deveice=" + JSON.stringify(obj),
      });
    });
};

// 跳转到耗电记录
const toHaoDianDetailHandel = (item) => {
  console.log(item);
  Taro.navigateTo({
    url: "/pages/device/consumption-record/consumption-record?id=" + item.id + '&type=' + item?.type,
  });
};

const toMoreDevice = (detail) => {
console.log(detail);
globalStore.setMore(detail)
Taro.navigateTo({
    url: "/pages/device/moreList/moreList",
})
}
</script>
<style lang="scss">
.device-list {
  padding: 0 23px;
  .add-water-device {
    .iconfont {
      font-size: 55px;
      color: #1452fd;
      font-weight: 700;
    }
  }

  .device-item {
    background: #ffffff;
    box-shadow: 0rpx 4rpx 32rpx 0rpx rgba(182, 190, 201, 0.38);
    border-radius: 20rpx;
    border: 1px solid #1452fd;
    padding: 15px;
    margin-top: 23px;
    .close-box {
      font-size: 24px;
      color: #f2811a;
      background-color: #fce9d1;
      padding: 5px 10px;
      // margin-right: 10px;
      border-radius: 6px;
    }

    .house-box-title {
      font-size: 36rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
      line-height: 31rpx;
    }
  }
  .device-top {
    margin-bottom: 7px;

    .device-name {
      font-size: 28rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .device-sn {
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .device-icon {
      width: 60px;
      height: 60px;
      background: #e9effa;
      border-radius: 10px;
      margin-right: 12px;
      text-align: center;
      // padding: 2px 0;
      .my-icon {
        margin-top: -10px;
      }
      .sn-text {
        font-size: 16px;
        font-weight: 700;
        margin-top: 10px;
        display: block;
        margin-left: 5px;
      }
      &.cus {
        position: relative;
        width: 88px;
        height: 72px;
        .sn-text  {
          position: absolute;
          left: 50%;
          bottom: 3px;
          font-size: 12px;
          transform: translateX(-50%);
          margin-left: 0 !important;
        }
      }
    }
    .fast-box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      // align-items: center;
      // margin-left: 80px;
      image {
        // width: 125px;
        // height: 90px;
      }
      text {
        color: #000;
        font-size: 20px;
        font-weight: 700;
        // margin-top: -10px;
      }
    }
    .label {
      font-weight: 700;
    font-size: 15px;
    display: inline-block;
    transform: translate(22px, -15px);
    }
  }
  .device-info {
    background: #f0f4ff;
    border-radius: 20px;
    padding: 10px 25px;
    text-align: center;
    > view {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 175px;
      height: 120px;
      background: #f0f4ff;
      border-radius: 20px;
    }

    .device-num {
      font-size: 40rpx;
      color: #1452fd;
      font-family: Bahnschrift;
      font-weight: 400;
      margin-bottom: 10px;
      font-weight: 700;
      display: flex;
      align-items: center;
      .du-t {
        max-width: 150px;
        overflow: hidden;
        display: inline-block;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #1452fd !important;
        font-size: 40px !important;
        margin-top: 0 !important;
      }
      text {
        font-weight: 500;
        font-size: 18px;
        color: #4460b3;
        margin-top: 5px;
      }
    }
    .device-lab {
      font-size: 26rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #879bd6;
    }
  }

  // -------------
  .btm-b {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .le {
      flex-shrink: 0;
    }
    .scrollview {
      /* 滚动条样式 */
      ::-webkit-scrollbar {
        width: 0px; /* 滚动条宽度 */
      }

      /* 滚动条轨道颜色 */
      ::-webkit-scrollbar-track {
        background-color: transparent; /* 透明背景 */
      }

      /* 滚动条滑块颜色 */
      ::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0); /* 半透明黑色滑块 */
        // border-radius: 10px; /* 圆角滑块 */
      }
    }
    .scrllo-right-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      padding: 0 10px;
      >view {
        flex-shrink: 0;
        margin-left: 15px !important;
      }
      .recharge-tip {
        font-size: 24px;
        color: red;
        font-weight: 700;
        // color: #f2811a;
        // background-color: red;
        // padding: 5px 10px;
        // border-radius: 6px;
      }
    }
    ._name {
      font-size: 32px;
      font-weight: 700;
      margin-left: 5px;
      vertical-align: middle !important;
    }
    .icon-s {
      width: 32px;
      height: 32px;
      vertical-align: middle;
    }
    .text-v-center {
      flex-shrink: 0;
      vertical-align: bottom;
    }
    .haodian {
      flex-shrink: 0;

      width: 160px;
      height: 46px;
      background: linear-gradient(90deg, #2a69f6 0%, #519bfd 100%);
      font-size: 20px;
      color: #fff;
      border-radius: 5px;
      line-height: 46px;
      text-align: center;
      .ico-l {
        width: 16px;
        height: 23px;
        vertical-align: middle;
        margin-right: 5px;
      }
      .ico-r {
        width: 9px;
        height: 15px;
        margin-left: 15px;
      }
    }
    .more-device {
      width: 130px;
    }
  }
}
</style>
