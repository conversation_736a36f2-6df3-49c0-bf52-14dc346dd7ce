<!--
 * @Autor: lisong
 * @Date: 2023-08-11 14:46:39
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-17 14:39:45
-->
<template>
  <view class="bottom">
    <view class="bottom-box">
      <slot></slot>
      <view @tap="handleClick" class="btn">{{ btnTxt }}</view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  btnTxt: {
    type: String,
    default: "",
  },
});

const emits = defineEmits(["click"]);
const handleClick = () => {
  emits("click");
};
</script>

<style lang="scss">
.bottom {
  height: calc(170rpx + env(safe-area-inset-bottom));
  .bottom-box {
    width: 700rpx;
    height: 154rpx;
    background: #ffffff;
    box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(54, 69, 193, 0.24);
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 25rpx;
    padding-bottom: env(safe-area-inset-bottom);
    // z-index: 999999;
    .btn {
      flex: 1;
      height: 88rpx;
      background: #1352fd;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #ffffff;
    }
  }
}
</style>
