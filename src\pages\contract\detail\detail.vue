<!--
 * @Autor: lisong
 * @Date: 2023-08-13 09:26:06
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-13 16:00:06
-->
<template>
  <view class="top">
    <view class="top-left">
      <view class="top-title"
        >{{ contract.house?.estate_name }} {{ contract.house?.name }}</view
      >
      <view>合同编号：{{ contract.sn }}</view>
    </view>
    <!-- <image
      class="more"
      src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/list-more.png"
      mode="widthFix"
    ></image> -->
  </view>
  <view class="card">
    <view class="card-top">
      <view>合同详情</view>
      <view class="card-top_right" @tap="handlePreviewImage" v-if="contract.type === 1">查看扫描件</view>
      <view class="card-top_right" v-if="contract.type === 2">
        <text class="btn-text-small btn-inline" @tap="onSignTaskPreview">预览</text>
        <text class="btn-text-small " @tap="onGetDownloadUrl">下载PDF</text>
      </view>
    </view>
    <view class="card-item">
      <view>合同编号</view>
      <view class="card-item_content">{{ contract.sn }}</view>
    </view>
    <view class="card-item">
      <view>合同类型</view>
      <view class="card-item_content">{{
        contract.c_type === 1 ? "租房合同" : ""
      }}</view>
    </view>
    <view class="card-item">
      <view>线上线下</view>
      <view class="card-item_content">{{
        contract.type === 1 ? "纸质合同" : "电子合同"
      }}</view>
    </view>
    <view class="card-item">
      <view>租房押金</view>
      <view class="card-item_content"
        >{{ contract.contract_rent?.deposit }}元</view
      >
    </view>
    <view class="card-item">
      <view>押付方式</view>
      <view class="card-item_content">{{ contract.contract_rent?.rent_num_name }}</view>
    </view>
    <view class="card-item">
      <view>房屋租金</view>
      <view class="card-item_content">{{
        contract.contract_rent?.amount
      }}</view>
    </view>
    <view class="card-item">
      <view>共计租期</view>
      <view class="card-item_content">{{
        contract.contract_rent?.month_num + "个月"
      }}</view>
    </view>
    <view class="card-item">
      <view>租房周期</view>
      <view class="card-item_content">{{
        contract.start_at + " ~ " + contract.expired_at
      }}</view>
    </view>
  </view>
  <view class="card">
    <view class="card-top">
      <view>租客信息</view>
    </view>
    <view class="card-item">
      <view>租客姓名</view>
      <view class="card-item_content">{{ contract.master_tenant?.name }}</view>
    </view>
    <view class="card-item" @tap="handleCall">
      <view>电话号码</view>
      <view class="card-item_content">
        <view>{{ contract.master_tenant?.mobile }}</view>
        <image
          class="card-item_phone"
          src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/icon-phone.png"
        ></image>
      </view>
    </view>
    <view class="card-item">
      <view>证件号码</view>
      <view class="card-item_content">{{ contract.master_tenant?.id_no }}</view>
    </view>
    <view class="card-item">
      <view>证件照片</view>
      <view class="card-item_content">
        <view
          class="card-item_look"
          v-if="
            contract.master_tenant?.id_card_image1 ||
            contract.master_tenant?.id_card_image2
          "
          @tap="handlePreviewIdImage"
        >
          查看
        </view>
        <view v-else>未上传</view>
      </view>
    </view>
  </view>
  <view class="card" v-if="globalStore.who === 'business'">
    <view class="card-top" @tap="changeSelect">
      <view>账单逾期是否断电</view>
      <view class="card-top_select">
        <image
          v-if="contract.is_fail_close"
          class="card-top_icon"
          src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/icon-radio_select.png"
        ></image>
        <image
          v-else
          class="card-top_icon"
          src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/icon-radio_none.png"
        ></image>
        <view>断电</view>
      </view>
    </view>
  </view>
  <view class="card" style="padding: 30rpx;box-sizing: border-box;" v-if="globalStore.who === 'business'">
    <textarea @blur="bindTextAreaBlur" v-model="contract.remark" maxlength="250"  placeholder="可输入水、电、气单价以及家具家电等费用约定" confirm-type="done" @confirm="onSave"/>
    <view class="btn-save" @tap="onSave">
      保存
    </view>
  </view>

  <myBottomBtn v-if="contract.status === 10 && contract.type === 2 && globalStore.who === 'business'" btnTxt="立即签署" @tap="onSignFangDong">
  </myBottomBtn>
  <myBottomBtn v-if="contract.status === 10 && contract.type === 2 && globalStore.who === 'tenant'" btnTxt="立即签署" @tap="onSign">
  </myBottomBtn>

  <myBottomBtn v-if="contract.status === 10 && contract.type === 2 && globalStore.who === 'business'" btnTxt="退回重签" @tap="onModify">

  </myBottomBtn>

  <myBottomBtn btnTxt="账单计划" @tap="onOpen" v-if="contract.status > 10">
    <view
      class="bottom"
      v-if="contract.status === 20 && globalStore.who === 'business'"
    >
      <view @tap="handleXuzu">续租</view>
      <view class="bottom-line"></view>
      <view @tap="handleBreak">退房</view>
    </view>
  </myBottomBtn>

  <view class="mask" v-if="showDel">
    <view class="mask-content">
      <view class="mask-title">删除合同</view>
      <view class="mask-info"
        >删除合同后, 该合同所有内容清零, 不可恢复，是否删除?
      </view>
    </view>
    <view class="mask-bottom">
      <view class="mask-delBtn" @tap="onDestroy">确认删除</view>
      <view class="mask-conBtn" @tap="handleShowDel">取消</view>
    </view>
  </view>

</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import myBottomBtn from "@/components/MyBottomBtn/MyBottomBtn";
import { getApiRoot } from "@/config";
import { addMonth } from "@/utils";
import { useGlobalStore } from "@/stores";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
const globalStore = useGlobalStore();
definePageConfig({
  navigationBarTitleText: "合同详情",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

let contract_id = "";
useLoad((options) => {
  contract_id = options.id;
});

useDidShow(() => {
  getContractDetail(contract_id);
  globalStore.getUserInfo();
});

const contract = ref({});
const getContractDetail = (id) => {
  request
    .get({
      url: "contract/" + id,
    })
    .then((res) => {
      contract.value = res.data;
    });
};

const handlePreviewImage = () => {
  Taro.previewImage({
    current: "", // 当前显示图片的http链接
    urls: contract.value.contract_images || [], // 需要预览的图片http链接列表
  });
};
const handlePreviewIdImage = () => {
  let images = [];
  if (contract.value.master_tenant.id_card_image1) {
    images.push(contract.value.master_tenant.id_card_image1);
  }
  if (contract.value.master_tenant.id_card_image2) {
    images.push(contract.value.master_tenant.id_card_image2);
  }
  Taro.previewImage({
    current: "", // 当前显示图片的http链接
    urls: images, // 需要预览的图片http链接列表
  });
};

const handleCall = () => {
  Taro.makePhoneCall({
    phoneNumber: contract.value.master_tenant?.mobile, //仅为示例，并非真实的电话号码
  });
};

const changeSelect = () => {
  contract.value.is_fail_close = (contract.value.is_fail_close === 1 ? 0 : 1)
  request.post({
    url: 'contract/' + contract_id + '/update',
    data: {
      is_fail_close: contract.value.is_fail_close
    }
  }).then(res => {
    Taro.showToast({
      title: '更新成功！'
    })
  })
};

const onSave = () => {
  request.post({
    url: 'contract/' + contract_id + '/update',
    data: {
      remark: contract.value.remark
    }
  }).then(res => {
    Taro.showToast({
      title: '更新成功！'
    })
  })
}

const handleBreak = () => {
  Taro.navigateTo({
    url: "/pages/contract/breakForm/breakForm?contract_id=" + contract.value.id,
  });
};

const handleXuzu = () => {
  Taro.navigateTo({
    url: `/pages/contract/rent/rent?contract_id=${contract.value.id}&type=xuzu`,
  });
};

const onOpen = () => {
  Taro.navigateTo({
    url: `/pages/contract/bill/bill?contract_id=${contract.value.id}`,
  });
};

const onSign = () => {
  /*if (globalStore.who === 'business' && (!globalStore.userInfo.fdd || globalStore.userInfo.fdd.status !== 2)) {
    Taro.showModal({
      title: '签署提示',
      content: '您尚未进行实名认证，请点击确定前往认证',
      success: (res) => {
        if (res.confirm) {
          Taro.navigateTo({
            url: '/pages/my/fdd/fdd'
          })
        }
      }
    })
    return;
  }*/
  request
    .get({
      url: "fdd/getSignTaskUrl/" + contract.value.id,
    })
    .then((res) => {
      Taro.navigateTo({
        url: '/pages/webview/webview?url=' + encodeURIComponent(res.data.actorSignTaskEmbedUrl)
      })
    });
}
const onSignFangDong = () => {
  request
    .get({
      url: "fdd/getSignTaskUrl/" + contract.value.id,
      data: {
        actorId: '房东'
      }
    })
    .then((res) => {
      Taro.navigateTo({
        url: '/pages/webview/webview?url=' + encodeURIComponent(res.data.actorSignTaskEmbedUrl)
      })
    });
}
const onSignTaskPreview = () => {
  request
    .get({
      url: "fdd/getSignTaskPreviewUrl/" + contract.value.id,
    })
    .then((res) => {
      Taro.navigateTo({
        url: '/pages/webview/webview?url=' + encodeURIComponent(res.data.signTaskPreviewUrl)
      })
    });
}
const showDel = ref(false)
const handleShowDel = () => {
  showDel.value = !showDel.value;
};
const onDestroy = () => {
  request
    .delete({
      url: `contract/${contract.value.id}/destroy`,
    })
    .then((res) => {
      Taro.navigateBack();
    });
};
const onModify = () => {
  Taro.redirectTo({
    url: "/pages/contract/createZcontract/createZcontract?id=" + contract.value.id,
  });
}

const onGetDownloadUrl = () => {
  request
    .get({
      url: "fdd/getSignTaskDownloadUrl/" + contract.value.id,
    })
    .then((res) => {
      Taro.navigateTo({
        url: '/pages/webview/webview?url=' + encodeURIComponent(res.data.downloadUrl)
      })
    });
}
</script>

<style lang="scss">
page, body {
  background: #f7f9ff;
  padding-bottom: 220px;
}

.top {
  height: 158rpx;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
  font-size: 26rpx;
  font-family: OPPOSans;
  font-weight: 500;
  color: #b6bec5;
  .top-title {
    font-size: 32rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
    margin-bottom: 10rpx;
  }
}

.more {
  width: 15rpx;
}

.card {
  width: 700rpx;
  background: #ffffff;
  box-shadow: 0rpx 4rpx 32rpx 0rpx rgba(182, 190, 201, 0.38);
  border-radius: 20rpx;
  margin: 24rpx;
  overflow: hidden;
  textarea{
    font-size: 26px;
    width: 100%;
    position: relative;
    z-index: 0;
  }
  .btn-save{
    width: 100%;
    height: 88rpx;
    background-color: #1352fd;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    border-radius: 10rpx;
    &:active{
      opacity: 0.75;
    }
  }
  .card-top {
    height: 118rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 38rpx;
    font-size: 36rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
    .card-top_right {
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #1352fd;
    }
  }
  .card-item {
    padding: 0 38rpx 24rpx;
    font-size: 26rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #b6bec5;
    display: flex;
    align-items: center;
    .card-item_content {
      color: #000;
      font-size: 24rpx;
      margin-left: 22rpx;
      display: flex;
      align-items: center;
      .card-item_phone {
        width: 30rpx;
        height: 30rpx;
        margin-left: 16rpx;
      }
    }
    .card-item_look {
      color: #1352fd;
    }
  }
}

.card-top_select {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-family: OPPOSans;
  font-weight: 500;
  color: #000000;
  .card-top_icon {
    width: 30rpx;
    height: 30rpx;
    margin-right: 10rpx;
  }
}

.bottomBtn {
  margin: 0 75px 0 50px;
}

.bottom {
  display: flex;
  align-items: center;
  margin-right: 25rpx;
  font-size: 30rpx;
  font-family: OPPOSans;
  font-weight: 500;
  color: #000000;
  .bottom-line {
    width: 4rpx;
    height: 30rpx;
    background: #000;
    margin: 0 25rpx;
  }
}
.mask {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 10;
  background-color: rgba($color: #000000, $alpha: 0.7);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  .mask-bottom {
    border-top: 1rpx solid #dfdfdf;
    width: 750rpx;
    height: 154rpx;
    background: #ffffff;
    padding-bottom: env(safe-area-inset-bottom);
    display: flex;
    align-items: center;
    .mask-delBtn {
      width: 264rpx;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .mask-conBtn {
      flex: 1;
      height: 88rpx;
      background: #1352fd;
      border-radius: 20rpx;
      margin-right: 25rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #ffffff;
    }
  }
  .mask-content {
    width: 750rpx;
    background: #ffffff;
    border-radius: 30rpx 30rpx 0rpx 0rpx;
    .mask-title {
      height: 120rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .mask-info {
      padding: 0 44rpx 50rpx;
      font-size: 30rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .mask-info_list {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 34rpx;
      font-size: 30rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
  }
}
</style>
