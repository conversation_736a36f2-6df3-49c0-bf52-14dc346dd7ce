<template>
  <View class="container-my-house">

    <view
      class="house-item"
      v-for="item in items"
      :key="item.id"
      @tap="onDetail(item.id)"
    >
      <view class="house-name">{{item.name}}</view>
      <view class="my-house-days">租期还剩 <text class="color-primary">{{item.expire_days}}</text> 天</view>
    </view>

  </View>
</template>

<script setup>
  import { ref } from 'vue'
  import './house.scss'
  import Taro, { useDidShow, useDidHide, useReady, useLoad, usePullDownRefresh, useReachBottom} from '@tarojs/taro'
  import request from '@/utils/request'

  import MySearch from '@/components/MySearch'
  import MyIcon from '@/components/MyIcon'

  import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

  usePullDownRefresh(() => {
    console.log('onPullDownRefresh')
  })

  const onDetail = (id) => {
    globalStore.myContractIndex = id
    globalStore.homeHasChange = true
    Taro.navigateBack()
  }

  /** ----------------------接口数据-begin----------------------------------- */
  const items = ref([])

  const getList = () => {
    request.get({
      url: 'index/tenant'
    }).then(res => {
      items.value = res.data.contract
    })
  }
  /** ----------------------接口数据-end----------------------------------- */

  useLoad(() => {
    getList()
  })


</script>
