{"name": "szp", "version": "1.0.0", "private": true, "description": "闪租婆", "templateInfo": {"name": "default", "typescript": false, "css": "sass"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "test": "jest", "postinstall": "patch-package"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@tarojs/cli": "^3.6.11", "@tarojs/components": "3.6.11", "@tarojs/helper": "3.6.11", "@tarojs/plugin-framework-react": "^3.6.11", "@tarojs/plugin-framework-vue3": "3.6.11", "@tarojs/plugin-platform-alipay": "3.6.11", "@tarojs/plugin-platform-h5": "3.6.11", "@tarojs/plugin-platform-jd": "3.6.11", "@tarojs/plugin-platform-qq": "3.6.11", "@tarojs/plugin-platform-swan": "3.6.11", "@tarojs/plugin-platform-tt": "3.6.11", "@tarojs/plugin-platform-weapp": "3.6.11", "@tarojs/runtime": "3.6.11", "@tarojs/shared": "3.6.11", "@tarojs/taro": "3.6.11", "@types/qrcode": "^1.5.5", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "lodash": "^4.17.21", "mini-html-parser2": "^0.3.0", "pinia": "^2.1.6", "qrcode": "^1.5.3", "taro-ui": "^2.3.4", "taro-ui-vue3": "^1.0.0-alpha.21", "vue": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@tarojs/taro-loader": "3.6.11", "@tarojs/test-utils-vue3": "^0.1.1", "@tarojs/webpack5-runner": "3.6.11", "@types/jest": "^29.3.1", "@types/node": "^18.15.11", "@types/webpack-env": "^1.13.6", "@vue/babel-plugin-jsx": "^1.0.6", "@vue/compiler-sfc": "^3.0.0", "babel-preset-taro": "3.6.11", "copy-webpack-plugin": "^11.0.0", "css-loader": "3.4.2", "eslint": "^8.12.0", "eslint-config-taro": "3.6.11", "eslint-plugin-vue": "^8.0.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.5.0", "patch-package": "^8.0.0", "postcss": "^8.4.18", "postinstall-postinstall": "^2.1.0", "style-loader": "1.3.0", "stylelint": "^14.4.0", "ts-node": "^10.9.1", "vue-loader": "^17.0.0", "webpack": "5.78.0"}}