<!-- 此文件是自定义的底部TabBar -->
<template>
  <view class="tabbar-list"  :style="{paddingBottom:`${isIos? safeAreaBottom : '0'}rpx`}">
    <view
      class="tabbar-item"
      :class="{ active: active == index }"
      v-for="(item, index) in curent"
      :key="index"
      @tap="changeActive(item, index)"
    >
      <image
        class="img"
        :src="
          active == index ? '/' + item.selectedIconPath : '/' + item.iconPath
        "
      ></image>
      <view :style="active == index ? 'color:#1e5cfd' : ''">{{
        item.text
      }}</view>
    </view>
  </view>
  <!-- 防止闪烁 -->
  <view class="emty-p-c">
    <image
      v-for="(item, index) in curent"
      :src="'/' + item.selectedIconPath"
      mode="scaleToFill"
    />
    <image :src="scanImg"></image>
  </view>
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";

import Taro, { useDidShow, useDidHide, useReady, useLoad } from "@tarojs/taro";

import { tenant, landlord, curent } from "@/utils/tabbar";
import { active, role, isLogin } from "@/utils/tabActive";
import scanImg from "@/assets/images/scantab.png";
import request from "@/utils/request";
import { useGlobalStore } from "@/stores";

const globalStore = useGlobalStore();

const safeAreaBottom = ref(120)

const info =  Taro.getSystemInfoSync()
const isAp = process.env.TARO_ENV === 'alipay'
const isIos = info.platform === "iOS"

if (process.env.TARO_ENV === 'alipay') {
  if ( info.platform === "iOS") {
    // safeAreaBottom.value = info.screenHeight - info.safeArea.height + 120
    safeAreaBottom.value = ((info.screenHeight - info.safeArea.bottom) * 2)
  }
}

const changeActive = (item, index) => {
  if (item.pagePath == 'scan') { 
    if (!Taro.getStorageSync('token')) {
      isLogin.value = true;
      return;
    } else {
      isLogin.value = false;
      // 扫码
      Taro.scanCode({
      scanType: "qrcode",
      success: (res) => {
        console.log(res, "scan result");
        if (!res.result) {
          Taro.showToast({
            title: "扫码失败",
            icon: "error",
          });
          return;
        }
        let sn = res.result.trim();
        sn.replace(/[\r\n]/g, "");
        if (sn.substring(0, 5) === "https") {
          let _arr = res.result.split("sn=");
          console.log(_arr);
          if (_arr.length > 0) {
            sn = _arr[1];
          }
        }
      // TODO 先判断是否是退费电表 如果是退费电表并且有正在进行的订单 则跳转到退费页面 
      request.get({
          url: 'device/search',
          data: {
            sn
          },
          showLoading:true,
          showToast:false
       }).then(resObj => {
         if (resObj.data?.show_order == 1) {
          // 查询临时用电订单
          request.get({
            url: 'order/tempList',
            data: {
              page: 1,
              pageSize:10
            }
          }).then(tempListRes => {
            console.log(tempListRes, "tempList");
            if (tempListRes.data.data.some(item => item.status == 5)) {
              Taro.navigateTo({
                url: "/pages/tenant/tempOrder/tempOrder",
              });
              return;
            }
            globalStore.setTempDevice()
            globalStore.setWaterTempDevice()
            Taro.navigateTo({
              url: `/pages/tenant/deviceDetail/deviceDetail?sn=${sn}&share=1`,
                success: function (res) {
              }
            })
          })
         } else {
          globalStore.setTempDevice()
          globalStore.setWaterTempDevice()
          Taro.navigateTo({
            url: `/pages/tenant/deviceDetail/deviceDetail?sn=${sn}&share=1`,
              success: function (res) {
            }
          })
        }
       })
      },
  });
    }
    return
  }
  // setTimeout(() => {
  Taro.switchTab({
    url: "/" + item.pagePath,
    success() {
      active.value = index;
    },
  });
  // }, 0);
};
</script>

<style lang="scss">
.emty-p-c {
  display: none;
}
.tabbar-list {
  display: flex;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 120rpx;
  background-color: #fff;
  // border: 1px solid #fff;
  overflow: hidden;
  // border-top-left-radius: 50rpx;
  // border-top-right-radius: 50rpx;
  box-shadow: 10px 10px 10px 10px #dadada;
  // 设置ios刘海屏底部横线安全区域
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  .tabbar-item {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 20rpx;
    color: #999;

    &.active {
      // &::before {
      // 	position: absolute;
      // 	left: 50%;
      // 	top: 0%;
      // 	transform: translateX(-50%);
      // 	content: '';
      // 	display: inline-block;
      // 	width: 34rpx;
      // 	height: 6rpx;
      // 	border-radius: 6rpx;
      // 	background-color: #3c82f6;
      // 	animation: opan .3s forwards;

      // 	@keyframes opan {
      // 		0% {
      // 			opacity: 0;
      // 		}

      // 		100% {
      // 			opacity: 1;
      // 		}
      // 	}
      // }
    }

    .img {
      width: 41rpx;
      height: 41rpx;
      margin-bottom: 4rpx;
    }
  }
}
</style>
