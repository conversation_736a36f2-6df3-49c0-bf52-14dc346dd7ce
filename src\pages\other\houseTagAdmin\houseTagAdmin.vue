<script setup>
import { ref, unref } from "vue";
import emtyImg from "@/assets/pic/emty.png";
import MyPopup from "@/components/MyPopup";
import request from "@/utils/request";
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import { watch } from "vue";

const posCur = ref(-1)

const bindReplaceInput = (e) => {
  let pos = e.detail.cursor
  posCur.value = pos
}

const isMore = ref(false);

const crePopShow = ref(false);

const editPopShow = ref(false);

const tagName = ref("");

const classList = ref([]);

const curentIdx = ref(0);

const curentItem = ref(null);

useLoad((options) => {
  const classId = options?.classId;
  console.log(options);
  getList().then(() => {
    if (classId) {
      classList.value.forEach((item, index) => {
        if (item.id == classId) {
          curentIdx.value = index;
          itemClickHandel(index);
        }
      });
    }
  });
});

const editHandel = (e, item) => {
  e.stopPropagation();

  const obj = {
    id: item?.id,
    label: item?.label,
  };

  Taro.navigateTo({
    url: "/pages/other/houseTagEdit/houseTagEdit?item=" + JSON.stringify(obj),
  });
};

const confirmCreateHandel = () => {
  if (!tagName.value) {
    Taro.showToast({
      title: "请输入分组名称",
      icon: "none",
    });
    return;
  }
  request
    .post({
      url: "houseClass/create",
      data: {
        label: unref(tagName),
      },
    })
    .then((res) => {
      if (res.code != 200) return;
      crePopShow.value = false;
      getList();
      Taro.showToast({
        title: "创建成功",
        icon: "none",
      });
    });
};

const confirmEditCreateHandel = () => {
  if (!tagName.value) {
    Taro.showToast({
      title: "请输入分组名称",
      icon: "none",
    });
    return;
  }
  request
    .post({
      url: "houseClass/create",
      data: {
        label: unref(tagName),
        id: unref(curentItem)?.id,
      },
    })
    .then((res) => {
      if (res.code != 200) return;
      editPopShow.value = false;
      getList();
      Taro.showToast({
        title: "修改成功",
        icon: "none",
      });
    });
};

const delHandel = (item) => {
  console.log(item, "item");
  Taro.showModal({
    title: "提示",
    content: `确定要删除${item?.label}该分组吗？`,
    success: (res) => {
      if (res.confirm) {
        request
          .get({
            url: "houseClass/delete",
            data: {
              id: item.id,
            },
          })
          .then((res) => {
            console.log(res, "res");
            if (res.code != 200) return;
            getList().then(() => {
              Taro.showToast({
                title: "删除成功",
                icon: "none",
              });
            });
          });
      } else {
        console.log("用户点击取消");
      }
    },
  });
};

// 获取分类列表
const getList = () => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "houseClass/list",
        data: {
          type: "edit",
        },
      })
      .then((res) => {
        if (res.code != 200) return;
        console.log(res);
        classList.value = res.data;
        resolve();
      })
      .catch((err) => {
        reject(err);
      });
  });
};

useDidShow(() => {
  getList();
});

const itemClickHandel = (index) => {
  isMore.value = !isMore.value;
  curentIdx.value = index;
};

const editPopHandel = (item) => {
  tagName.value = item?.label;
  curentItem.value = item;
  editPopShow.value = true;
};

watch(
  () => editPopShow.value,
  (newVal) => {
    if (!newVal) {
      tagName.value = "";
      curentItem.value = null;
    }
  }
);
</script>

<template>
  <view class="house-tag-admin">
    <!-- 列表 -->
    <view class="list" v-if="classList.length > 0">
      <view
        class="item"
        @tap="itemClickHandel(index)"
        v-for="(item, index) in classList"
        :key="index"
      >
        <view class="top">
          <view class="left">
            <view
              class="icon-xiajiantou iconfont"
              v-if="item?.houses?.length > 0"
              :style="{
                transform: `rotate(${
                  isMore && curentIdx == index ? '180' : '0'
                }deg) translateY(3rpx)`,
                transition: 'all .3s',
              }"
            ></view>
            <view class="title">{{ item?.label }}</view>
            <view
              class="icon-bianji1 iconfont"
              @tap.stop="editPopHandel(item)"
            ></view>
          </view>
          <view class="tag" @tap.stop="delHandel(item)">删除</view>
          <view
            class="right"
            :style="{
              backgroundColor:
                isMore && curentIdx == index ? '#3562e4' : '#8196d1',
            }"
            @tap="editHandel($event, item)"
          >
            <view class="iconfont icon-setting"></view>
            <view>设置({{ item?.houses?.length }})</view>
          </view>
        </view>
        <template
          v-if="isMore && curentIdx == index && item?.houses?.length > 0"
        >
          <view class="_bottom" v-for="oitem in item.houses" :key="item.id">
            <view class="tit">
              <image
                src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
                mode="scaleToFill"
                class="icon-s"
              />
              <view>{{ oitem?.name }}</view>
            </view>
            <!-- 电表 -->
            <view class="li" v-for="(sitem, idx) in oitem.device" :key="idx">
              <view class="le">
                <text v-if="sitem.type != 2" class="iconfont icon-dianbiao_shiti"></text>
                <text v-else class="iconfont icon-shuibiao"></text>
                <text
                  :style="
                    sitem?.net_type == 1 && sitem?.signal_num <= 0
                      ? 'color: #98A6C3;'
                      : ''
                  "
                  >{{ sitem?.type == 2 ? "水表" : "电表" }}-{{
                    sitem?.sn
                  }}</text
                >
              </view>
              <view
                class="ri"
                v-if="sitem?.signal_num > 0 && sitem?.net_type == 1"
              >
                在线
              </view>
              <view
                class="ri"
                style="color: #98a6c3"
                v-if="sitem?.net_type == 1 && sitem?.signal_num <= 0"
              >
                离线
              </view>
            </view>
            <!-- 门锁 -->
            <view
              class="li"
              v-for="(sitem, idx) in oitem.lock.filter(
                (o, i) =>
                  oitem.lock.findIndex((f) => o.lock_id == f.lock_id) == i
              )"
              :key="idx"
            >
              <view class="le">
                <text
                  class="iconfont icon-a-jigouguanliduantubiao_huaban1fuben13"
                  style="font-size: 35rpx; margin-left: -5rpx"
                ></text>
                <text>门锁-{{ sitem?.lock_id }}</text>
              </view>
            </view>
          </view>
        </template>
      </view>
    </view>
    <!-- emty -->
    <view class="emty-box" v-if="classList.length == 0">
      <image :src="emtyImg" class="emty-img" mode="aspectFill"></image>
      <view>暂无分组</view>
    </view>
    <!-- utils -->
    <view class="footer-fixed">
      <button class="btn-add m33" @tap="crePopShow = true">创建分组</button>
    </view>

    <!-- create弹窗 -->
    <MyPopup :show="crePopShow" @close="crePopShow = false">
      <template #content>
        <view class="pop-content">
          <view class="pop-title">创建分组名称</view>
          <input  :cursor="posCur"  @input="bindReplaceInput" placeholder="请输入分组名称" v-model="tagName" />
          <view class="utils">
            <view>取消</view>
            <button @tap="confirmCreateHandel">确认创建</button>
          </view>
        </view>
      </template>
    </MyPopup>

    <!-- edit弹窗 -->
    <MyPopup :show="editPopShow" @close="editPopShow = false">
      <template #content>
        <view class="pop-content">
          <view class="pop-title">编辑分组名称</view>
          <input :cursor="posCur"  @input="bindReplaceInput" placeholder="请输入分组名称" v-model="tagName" />
          <view class="utils">
            <view @tap="editPopShow = false">取消</view>
            <button @tap="confirmEditCreateHandel">确认编辑</button>
          </view>
        </view>
      </template>
    </MyPopup>
  </view>
</template>

<style lang="scss">
page {
  background: #f8f9ff;
}
.house-tag-admin {
  > .list {
    padding: 0 30rpx;
    box-sizing: border-box;
    padding-bottom: 300px;
    .item {
      border: 1px solid #d7dbe8;
      width: 700px;
      min-height: 110px;
      box-sizing: border-box;
      margin-top: 30px;
      background: #ffffff;
      border-radius: 20px;
      padding: 20px;
      .tag {
        background: #f86811;
        border-radius: 15px;
        display: flex;
        justify-content: center;
        color: #fff;
        align-items: center;
        font-size: 30px;
        transform: translateX(10px);
        padding: 3px 10px;
        font-weight: 700;
      }
      ._bottom {
        border-top: 1px solid #d7dbe8;
        padding-top: 15px;
        padding-bottom: 20px;
        animation: opacity 0.3s;
        @keyframes opacity {
          0% {
            opacity: 0;
          }
          100% {
            opacity: 1;
          }
        }
        .tit {
          display: flex;
          align-items: center;
          font-size: 32px;
          color: #204eca;
          font-weight: 700;
          .icon-s {
            width: 32px;
            height: 32px;
            vertical-align: middle;
            margin-right: 10px;
          }
        }
        .li {
          display: flex;
          // justify-content: space-between;
          margin-top: 10px;
          font-size: 26px;
          .le {
            color: #090909;
            display: flex;
            align-items: center;
            .iconfont {
              font-size: 24px;
              color: #6f7c9a;
              margin-right: 5px;
            }
          }
          .ri {
            color: #0072ff;
            margin-left: 20px;
          }
        }
      }
      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 15px;
        padding-top: 15px;
        .left {
          display: flex;
          align-items: center;
          width: 340px;
          font-size: 24px;
          .iconfont {
            font-size: 25px;
            margin-right: 7px;
            color: #000;
          }
          .title {
            margin-right: 10px;
            font-size: 36px;
            font-weight: 700;
            overflow: hidden;
            text-overflow: ellipsis;
            // width: 100%;
          }
        }
        .right {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 168px;
          height: 43px;
          background: #8196d1;
          border-radius: 10px;
          transition: all 0.3s;
          .iconfont {
            margin-right: 10px;
            color: #ffffff;
          }
          view {
            font-size: 30rpx;
            color: #ffffff;
            line-height: 38px;
            text-align: center;
            font-weight: 700;
          }
        }
      }
    }
  }
  .emty-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    font-size: 28rpx;
    color: #999;
    transform: translateY(-10%);
    .emty-img {
      width: 360px;
      height: 284px;
      margin: 0 auto;
    }
  }

  .pop-content {
    padding: 30px;
    .pop-title {
      font-size: 28px;
      color: #000;
      text-align: center;
      padding-bottom: 40px;
      margin-top: 20px;
    }
    input {
      width: 681px;
      height: 101px;
      background: rgba(87, 114, 150, 0);
      border-radius: 20px;
      border: 2px solid #e9e9e9;
      padding-left: 20px;
    }
    .utils {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      // border-top: 1px solid #E9E9E9;
      padding-top: 80px;
      view {
        width: 181px;
        height: 88px;
        border-radius: 20px;
        font-size: 28px;
        color: #000;
        text-align: center;
        line-height: 88px;
      }
      button {
        width: 460px;
        height: 88px;
        background: #1352fd;
        border-radius: 20px;
        font-size: 28px;
        color: #fff;
        text-align: center;
        line-height: 88px;
      }
    }
  }
}
</style>
