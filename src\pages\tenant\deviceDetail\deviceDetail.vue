<template>
  <view class="container">

    <view class="tdd">

      <view class="ban01">
        <image src="https://yimits.oss-cn-beijing.aliyuncs.com/images/rechare-tip-img.png" mode="widthFix"></image>
      </view>

    </view>

    <view class="tdd-box">
      <view class="flex device-top">
        <!-- 网络刷新 -->
          <view class="reload-btn" @tap="reloadMainHandel" v-if="device.client_id && device.is_master == 0 && device.signal_num == 0">
            <text class="iconfont icon-shuaxin"></text> 
            <text class="text-v-center">刷新网络</text>
          </view>
          <view class="device-icon">
            <MyIcon icon="icon-dianbiao" width="44rpx" height="57rpx"></MyIcon></view>
          <view style="flex: 1"><view class="device-name"><text> {{ device?.type == 2 ? '水表' : '电表' }} </text> <text class="house-name">房间号：{{ device?.house?.name }}</text></view><view class="device-sn">
            <text v-if="device.net_type === 1 || isNetNoSingle">{{ device?.type == 2 ? '4G双模水表号' : '4G双模电表号' }}</text><text v-if="device.net_type !== 1">{{ device?.type == 2 ? '蓝牙水表号' : '蓝牙电表号' }}</text>: {{device.sn}}  </view></view>
            <view style="width: 64rpx;" v-if="device.net_type === 1 || isNetNoSingle">
              <MyIcon :icon="'signal/' +  device.signal_num" width="60rpx" height="49rpx"></MyIcon>
            </view>
          </view>
          <!-- <view class="house-name">
            <text class="house-name">房源名称：{{ device?.house?.name }}</text>
          </view> -->
      <view class="tdi">
        <view class="device-du">
          <view class="device-du1"><MyIcon v-if="device?.type != 2 && (device?.type != 5 && device?.type != 6)" icon="icon-dl2" width="15rpx" height="25rpx"></MyIcon> <text class="text-v-center">{{ device.agent?.type == 2 || device?.type == 5 || device?.type == 6 || confBusiness?.elec_show_type == 2 ? '剩余金额（元）' : device?.type == 2 ? '剩余水量(m³)' : '剩余电量(度)' }}</text>
            <view id="zifei-box-sm" @tap.stop="showZF = true">
                <text>资费</text>
                <text class="iconfont icon-wenhao"></text>
            </view>
          </view>
          <view class="device-du2">{{ device.agent?.type == 2 || confBusiness?.elec_show_type == 2 ? (device.du * (Number(device.price) + Number(device.agent?.service))).toFixed(2)  : confBusiness?.elec_show_type == 1 ? device.du : '--'}}</view>
        </view>
        <view class="text-center">
          <view class="btn-cb" @tap="onQueryStatus"><MyIcon icon="icon-cb2" width="28rpx" height="28rpx"></MyIcon> <text class="text-v-center">抄表</text></view>
        </view>
      </view>
      <view class="d-bar2">
        <view class="tip-box">
          付款后，请点击【已完成】并继续等待3秒!
        </view>
        <!-- 普通用户显示 -->
        <view class="flex flex-space-between mt10" v-if="device?.bus_params">
          <view class="flex-l"><text class="color-gray">公摊信息</text></view>
          <view class="flex-r text-right">{{ device?.bus_params.type == 1 ? '公摊主表' : device?.bus_params.type == 2 ? '公摊子表' : '公摊表' }}</view>
        </view>
        <view class="flex flex-space-between" v-if="device.agent?.type != 2">
          <view class="flex-l">
            <text class="color-gray">{{ device?.type == 2 ? '总水量(m³)' : '总电量(度)' }}</text>
          </view>
          <view class="flex-l text-right">
            {{ Number(device?.total || 0).toFixed(2) }}
          </view>
        </view>
        <view class="flex flex-space-between mt10">
          <view class="flex-l"><text class="color-gray">抄表时间</text></view>
          <view class="flex-r text-right">{{device.read_at || '未抄表'}}</view>
        </view>
        <!-- 普通用户显示 -->
        <!-- <template v-if="device?.type != 5 && device?.type != 6">
          <view class="flex flex-space-between mt10" v-if="device.agent?.type != 2">
            <view class="flex-l"><text class="color-gray">{{ device?.type == 2 ? '综合水价(m³)' : '综合电价(元/度)' }}</text></view>
            <view class="flex-r text-right">{{device.price}}</view>
          </view>
          <view class="flex flex-space-between mt10" v-if="conf?.show_basic_price">
            <view class="flex-l"><text class="color-gray">{{ device?.type == 2 ? '基础水价(m³)' : '基础电价(元/度)' }}</text></view>
            <view class="flex-r text-right">  {{ device.basic_price }}</view>
          </view>
          <view class="flex flex-space-between mt10" v-if="conf?.show_service_price">
            <view class="flex-l"><text class="color-gray">{{ device?.type == 2 ? '服务费(m³)' : '服务费(元/度)' }}</text></view>
            <view class="flex-r text-right">{{device.service_price || '0.00'}}</view>
          </view>
          <view class="flex flex-space-between mt10" v-if="device.agent?.type == 2 && conf?.show_service_price">
            <view class="flex-l"><text class="color-gray">{{ device?.type == 2 ? '设备费(m³)' : '设备费(元/度)' }}</text></view>
            <view class="flex-r text-right">{{device.agent?.service}}</view>
          </view>
        </template>
        <template v-else>
          <view class="flex flex-space-between mt10">
            <view class="flex-l"><text class="color-gray">尖峰价格/度</text></view>
            <view class="flex-r text-right">{{device.coef4}}元</view>
          </view>
          <view class="flex flex-space-between mt10">
            <view class="flex-l"><text class="color-gray">高峰价格/度</text></view>
            <view class="flex-r text-right">{{device.coef3}}元</view>
          </view>
          <view class="flex flex-space-between mt10">
            <view class="flex-l"><text class="color-gray">平段价格/度</text></view>
            <view class="flex-r text-right">{{device.coef2}}元</view>
          </view>
          <view class="flex flex-space-between mt10">
            <view class="flex-l"><text class="color-gray">低谷价格/度</text></view>
            <view class="flex-r text-right">{{device.coef1}}元</view>
          </view>
        </template> -->
        <view class="flex flex-space-between mt10">
          <view class="flex-l"><text class="color-gray">联系房东</text></view>
          <view class="flex-r text-right" @tap="onCall(devAdminInfo?.mobile || (device.business && device.business.user.mobile))">{{ devAdminInfo?.mobile || (device.business && device.business.user.mobile)}}</view>
        </view>
      </view>

    </view>

    <!-- <view class="btn-rc"><button class="btn btn-primary" @tap="onDeviceRecharge">电表充值</button></view> -->
    <view class="footer-fixed">
      <view class="flex flex-v-center p20">
        <view style="width: 28%"><text class="color-primary" @tap="goHome">首页</text></view>
        <view style="width: 70%;"><button class="btn btn-primary" @tap="onDeviceRecharge">{{ device?.type == 2 ? '水表充值' : '电表充值' }}</button></view>
      </view>
    </view>

    <LoginDialog :show="showLogin" @close="onLoginClose" @login="onLogin">
    </LoginDialog>
    <MyPopup :show="showFail" title=" " @close="onChangeShowFail">
      <template #content>
        <view class="text-center"><MyIcon icon="icon-warning" width="150rpx" height="150rpx"></MyIcon></view>
        <view class="fail-title">充值电量未到账</view>
        <view class="fail-tip">
          <view>1、您有一笔充值完成后, 电量未到账的 订单请重新发起充值</view>
          <view>2、此次充值不会产生扣款</view>
        </view>
        <view class="fail-btn flex flex-space-between">
          <view class="fail-btn-w"><button class="btn-second" @tap="onChangeShowFail">稍后再去</button></view>
          <view class="fail-btn-w"><button class="btn-primary" @tap="onJumpFailOrder">补电量</button></view>
        </view>
      </template>
    </MyPopup>
    <GlobDialog @confirm="confirmGlobHandel" :device="device" @reloadMainHandel="reloadMainHandel"/>
    <MyPopup :show="showZF" :isScrlloAnimation="false"  :titleStyle="{ color: '#000', fontSize: '36rpx' }" title="资费说明" @close="showZF = false">
      <template #content>
        <view class="recharge-confirm-zf">
          <!-- 普通用户显示 -->
          <template v-if="device?.type != 5 && device?.type != 6">
            <view class="flex flex-space-between mt10" v-if="device.agent?.type != 2 && confBusiness?.show_price">
              <view class="flex-l"><text class="color-gray">{{ device?.type == 2 ? '综合水价(m³)' : '综合电价(元/度)' }}</text></view>
              <view class="flex-r text-right">{{device.price}}</view>
            </view>
            <view class="flex flex-space-between mt10" v-if="conf?.show_basic_price">
              <view class="flex-l"><text class="color-gray">{{ device?.type == 2 ? '基础水价(m³)' : '基础电价(元/度)' }}</text></view>
              <view class="flex-r text-right">  {{ device.basic_price }}</view>
            </view>
            <view class="flex flex-space-between mt10" v-if="conf?.show_service_price">
              <view class="flex-l"><text class="color-gray">{{ device?.type == 2 ? '服务费(m³)' : '服务费(元/度)' }}</text></view>
              <view class="flex-r text-right">{{device.service_price || '0.00'}}</view>
            </view>
            <view class="flex flex-space-between mt10" v-if="device.agent?.type == 2 && conf?.show_service_price">
              <view class="flex-l"><text class="color-gray">{{ device?.type == 2 ? '设备费(m³)' : '设备费(元/度)' }}</text></view>
              <view class="flex-r text-right">{{device.agent?.service}}</view>
            </view>
          </template>
          <!-- 峰谷表显示 -->
          <template v-else>
            <view class="flex flex-space-between mt10">
              <view class="flex-l"><text class="color-gray">尖峰价格/度</text></view>
              <view class="flex-r text-right">{{device.coef4}}元</view>
            </view>
            <view class="flex flex-space-between mt10">
              <view class="flex-l"><text class="color-gray">高峰价格/度</text></view>
              <view class="flex-r text-right">{{device.coef3}}元</view>
            </view>
            <view class="flex flex-space-between mt10">
              <view class="flex-l"><text class="color-gray">平段价格/度</text></view>
              <view class="flex-r text-right">{{device.coef2}}元</view>
            </view>
            <view class="flex flex-space-between mt10">
              <view class="flex-l"><text class="color-gray">低谷价格/度</text></view>
              <view class="flex-r text-right">{{device.coef1}}元</view>
            </view>
          </template>
        </view>
        <view class="rd-btn-box" style="border:none;">
          <button class="btn-add" style="margin-bottom: 20rpx;width: 88%;" @tap="showZF = false">好的</button>
        </view>
      </template>
    </MyPopup>
    <YModal
      title="no"
      confirmText="no"
      :show="showNetModal"
      @close="showNetModal = false"
      @confirm="confirmNetHandel"
      :maskClose="false"
      :bodyStyle="{width:'680rpx',paddingTop:'100rpx',borderRadius:'30rpx',height: '625rpx'}"
    >
      <template #content>
        <view class=" content-modal2" style="text-align:center">
          <image :src="netErroImg" style="width: 218rpx;height:155rpx"></image>
          <view :style="{
            color: '#000',
            fontWeight: 700,
            marginTop: '79rpx',
            marginBottom: '38rpx',
            fontSize:'40rpx'}">远程网络<text style="color:#db2b2b ;">异常</text></view>
          <view>
            <text style="font-size: 35rpx;font-weight: 500;">与电表保持3米以内距离打开蓝牙进行操作</text>
          </view>
          <view class="btn-box" @tap="showNetModal = false">
            <view @tap="clickYesHandel" style="color: #000;">我知道了</view>
          </view>
        </view>
      </template>
    </YModal>

  </view>
</template>
<script setup>
  import { ref, computed } from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad,useUnload, usePullDownRefresh, useReachBottom} from '@tarojs/taro'
  import request from '@/utils/request'
  import deviceConnect from '@/utils/deviceConnect'
  import {checkNetInfo, formatTime} from '@/utils'

  import MyIcon from '@/components/MyIcon'
  import LoginDialog from '@/components/LoginDialog'
  import MyPopup from '@/components/MyPopup'

  import GlobDialog from '@/components/globDialog/index.vue'
  import YModal from '@/components/YModal/index.vue'

  const showZF = ref(false)
  const netErroImg = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/net-erro.png'

  const isAp = process.env.TARO_ENV === "alipay";

  import { tenant as tenantTab, landlord, curent } from "@/utils/tabbar";
  
  import { role, active } from "@/utils/tabActive";

  import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

  definePageConfig({
    navigationBarTitleText: "闪租婆",
    navigationBarBackgroundColor: "#1352FD",
    navigationBarTextStyle: "white",
  });

  const id = ref(0)
  const sn = ref('')
  const share = ref(false)

  const pageIsAlive = ref(true)

  const timerRed = ref(null)

  const orderFail = ref({})

 const flagRe = ref(false)

 const showNetModal = ref(false)

 const devAdminInfo = ref()//设备管理员信息

 /**
 * 切换身份
 */
const handleChangeWho = (who) => {
  globalStore.setWho(who);
  active.value = 0;

  if (who == "tenant") {
    Taro.setStorageSync("tablist", JSON.stringify(tenantTab));
    curent.value = tenantTab;
  } else {
    Taro.setStorageSync("tablist", JSON.stringify(landlord));
    curent.value = landlord;
  }
  globalStore.homeHasChange = true;
  request
    .post({
      url: "user/update",
      data: {
        type: who,
      },
    })
    .then((res) => {
    });
};


 const sleepHandel = (time) => {
  return new Promise((resolve, reject) => {
     let tim = setTimeout(() => {
        resolve()
        clearTimeout(tim)
      }, time)
    })
  }


  useDidHide(() => {
    pageIsAlive.value = false
    clearInterval(timerRed.value)
    // globalStore.setGlobDialog({show:false,type:''})
    // globalStore.setDeviceTime(15)
  })

  useUnload(() => {
    pageIsAlive.value = false
    clearInterval(timerRed.value)
    globalStore.setGlobDialog({show:false,type:''})
    globalStore.setDeviceTime(15)
  })

  useLoad((option) => {
    console.log(option);
    id.value = option.id
    sn.value = option.sn
    share.value = !!option.share
    globalStore.who = 'tenant'
    globalStore.setGlobDialog({show:false})
    handleChangeWho('tenant')
    console.log('loadadaddadadd');
    // getDetail().then((res) => {
    //   console.log(res);
    //   if (res.net_type == 1 && res.signal <= 0) {
    //     showNetModal.value = true
    //   }
    // })
  })

  useDidShow(async() => {
    // globalStore.setGlobDialog({show:false,type:''})
    showFail.value = false
    flagRe.value = false
    pageIsAlive.value = true
    getDetail().then((res) => {
      console.log(res);
      if (res.net_type == 1 && res.signal <= 0) {
        showNetModal.value = true
      }
    })
    if (!globalStore.isLogin) {
      showLogin.value = true
    } else {
    await globalStore.getUserInfo();
    }
  })

  const device = ref({})
  const conf = ref({})
  const confBusiness = ref({})

  const isNetNoSingle = ref(false)//是否为4g并且没信号

  function calculateDaysBetweenDates (date1, date2) {
    if(!date1 || !date2) return NaN
    // 将日期转换为当天的 00:00:00
    const d1 = new Date(date1);
    d1.setHours(0, 0, 0, 0);
    
    const d2 = new Date(date2);
    d2.setHours(0, 0, 0, 0);

    const dayInMs = 24 * 60 * 60 * 1000;
    const diffTime = d2.getTime() - d1.getTime();
    const diffDays = Math.round(diffTime / dayInMs);
    
    return diffDays;
  } 

  //自动重启
const autoReload = (sn) => {
  return
   if (device.value.client_id && device.value.is_master == 0 && device.value.signal_num == 0) {
     request.get({
       url: 'device/getOff',
       data: {
         sn
       },
       showLoading: false,
       showToast: false
     }).then(res => {
        //如果最后离线时间比现在少一天需要自动重启
        const lastOfflineTime = res.data.created_at//2025-03-05 16:06:02
        const lastOfflineTimeDay = calculateDaysBetweenDates(new Date(),lastOfflineTime)
        if (lastOfflineTimeDay <= -1) {
          console.log('自动重启函数');
          request.get({
            url: 'device/restart_master',
            showLoading: false,
            showToast: false,
            data: {
              mac: device.value.mac
            }
          })
        }
     }).finally(() => {
       // Taro.hideLoading()
     }).catch(e => {
  
     })
   }
  }

    //重启对应主表
const reloadMainHandel = () => {
    console.log('重启对应主表');
    Taro.showModal({
      title: '温馨提示',
      content: '是否确认刷新网络？',
      confirmText: '确认',
      success: res => {
        if (res.confirm) {
          Taro.showLoading({
            title: '刷新中...'
          })
          request.get({
            url: 'device/restart_master',
            showLoading:false,
            data: {
              mac: device.value.mac
            }
          }).then(res => {
            if (res.code == 200) {
              Taro.showToast({
                title: '重刷新成功',
                icon: "success",
                duration: 2000
              })
            }
          }).finally(() => {
            // Taro.hideLoading()
          }).catch(e => {

          })
        }
      }
    })
}

const getDetail = () => {
  return new Promise((resolve, reject) => {
    let opt = {
      url: 'device/' + id.value
    }
    if (sn.value) {
      opt = {
        url: 'device/search',
        data: {
          sn: sn.value
        }
      }
    }

    request.get(opt).then(res => {
      device.value = res.data
      id.value = res.data.id
      sn.value = res.data.sn
      // 渲染管理员联系方式-如果有的话
      request.get({
        url: 'device/getAdmin',
        data: {
          sn:sn.value
        }
      }).then((adminRes) => {
        console.log(adminRes,"adminRes");
        devAdminInfo.value = adminRes?.data
      })
      request.get({
        url: `device/new_business_conf`,
        data: {
          sn:sn.value
        },
        showToast:false
      }).then(res => {
        console.log(res);
        if(res.code != 200) return
        conf.value = res.data
      })
      request.get({
        url: 'device/' + id.value
      }).then(result => {
        device.value = result.data
        autoReload(device.value?.sn)
        request
        .get({
          url: "business/conf",
          data: {
            businessId: result.data?.business_id,
          },
          showLoading:false,
          showToast:false
        })
        .then((res) => {
          confBusiness.value = res.data;
        });
        resolve(res.data)
        // 非4G设备扫描蓝牙
        // deviceConnect.init(device.value.mac, device.value.net_type)
        // if (device.value.net_type !== 1) {
        //   globalStore.listenClient = device.value.mac + '-3'
        //   //listenDeviceMessage()
        // }

        if (res.data.net_type == 1 && res.data.signal <= 0) {
          // 为4G设备且没有信号 走蓝牙操作表的逻辑
            device.value.net_type = 2
            // showNetModal.value = true
            isNetNoSingle.value = true
            globalStore.listenClient = device.value.mac + '-3'
            deviceConnect.init(device.value.mac, device.value.net_type)
          } else if(res.data.net_type !== 1) {
            // 非4G设备扫描蓝牙
            device.value.net_type = 2
            deviceConnect.init(device.value.mac, device.value.net_type)
            globalStore.listenClient = device.value.mac + '-3'
          } else {
            // 为4G设备且有信号 走4G操作表的逻辑
            device.value.net_type = 1
            deviceConnect.init(device.value.mac, device.value.net_type)
          }

          function updateCachedDevice(device, deviceList, updateFunction) {
            if (!deviceList || !Array.isArray(deviceList) || !deviceList.length) {
              return; // 列表为空或无效，直接返回
            }

            const index = deviceList.findIndex(item => item.id === device.value.id);

            if (index !== -1) {
              updateFunction({...device.value,net_type:res.data.net_type});
            }
          }

          if(device.value.type == 2) {
            // 更新水表缓存
            updateCachedDevice(device, globalStore.waterTempDeviceList, globalStore.setWaterTempDevice);
          } else {
            // 更新电表缓存
            updateCachedDevice(device, globalStore.tempDeviceList, globalStore.setTempDevice);
          }

          // 如果是分享则更新缓存
        if (share.value) {
          // globalStore.putTempDevice(device.value)
          if (device.value?.type == 2) {
            // 水表
            globalStore.setWaterTempDevice({...device.value,net_type:res.data.net_type})
          } else {
            globalStore.setTempDevice({...device.value,net_type:res.data.net_type})
          }
          // share.value = false
          if (globalStore.isLogin) {
            showLogin.value = false
            // 接口存进登录态
            // if (device.value?.type == 2) return//水表不存入登录态
            request.get({
              url: 'user/bindDevice',
              data: {
                sn:sn.value
              }
            }).then((_res) => {
            })
          }
        }
        getFailOrder(device.value.id)
      })
    })
  })
  }

  const isRedBleDevice = ref(false)//是否点击抄表

  const confirmGlobHandel = () => {
    // 抄表
    if (isRedBleDevice.value) {
      isRedBleDevice.value = false
      deviceConnect.getConnection()
      .then(() => {
        deviceConnect.queryStatus()
      })
      .catch(() => {
        if (pageIsAlive.value) {
          globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
          globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
          }
      })
      Taro.showLoading({
        title: '正在读表中...',
        icon: 'none',
        mask:true
      })
      listenDeviceReadBle()
      return
    }
    // 4g无信号时 或者蓝牙时
    deviceConnect.queryStatus()
        .catch(() => {
        if (pageIsAlive.value) {
          flagRe.value = false
          globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
          globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
        }
    })
    listenDeviceReadBle(false).then(() => {
      setTimeout(() => {
        deviceConnect.getConnection().then(_ => {
          globalStore.setGlobDialog({show:false,type:''})
          Taro.navigateTo({
            // url: '/pages/device/recharge/recharge?id=' + id.value + '&who=tenant'
            url: '/pages/device/recharge/recharge?id=' + id.value + '&who=tenant' +'&show_order=' + device.value?.show_order
          })
          }).catch(() => {
          if (pageIsAlive.value) {
            flagRe.value = false
            globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
            globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
          }
      })
      },200)
    })
    // 蓝牙表
  }

  const onQueryStatus = async () => {
    if (device.value.net_type === 2) {
      globalStore.setGlobDialog({show:true,type:'BleConnectionTip'})
      isRedBleDevice.value = true
      } else {
        Taro.showToast({
          title: '4G表无需抄表',
          icon: 'none'
        })
      }
  }

  const onChangeAmount = (val) => {
    formState.value.amount = val
  }

  const onChangeInputAmount = (e) => {
    formState.value.amount = e.detail.value
  }

  const onSubmit = () => {

  }

  const goHome = (url) => {
    Taro.switchTab({
      url: '/pages/index/index'
    })
  }

  const fastInfo = ref(null)
    // 判断是否搜集信息
    const checkIsCollectInfo = () => {
      return new Promise((reslove, reject) => {
      // if(globalStore.who != "tenant") return reslove(false)
        request.get({
          url: 'user/checkFast',
          data: {
            device_id: id.value
          }
        }).then(res => {
          const isNeed = res.data.is_need
          fastInfo.value = res.data
          // const isNeed = true
          reslove(isNeed)
        }).catch(() => {
          reslove(false)
        })
      })
}

const handleRechargeSuccess = (orderNo) => {
    return new Promise((resolve, reject) => {
      request.post({
        url: 'order/success',
        data: {
          orderNo
        },
        showLoading: false
      }).then(_=> {
        resolve()
      }).catch(_=> {
        reject()
      })
    })
  }

  /**
   * 检查当前度数是否大于上次充值失败的度数
   * @returns {Promise<boolean>} true:大于 false:小于等于
   * @description 如果大于则认为充值成功了不在进行补充
   */
const checkLastFailOrder = (orderNo,failOrder) => {
  return new Promise(async (resolve, reject) => {
      try {
        if (device.value?.net_type == 2) {
          // 蓝牙表读取最新度数
          console.log('读取最新度数');
          deviceConnect.getConnection()
            .then(() => {
              deviceConnect.queryStatus()
          })
          .catch(() => {
              if (pageIsAlive.value) {
                flagRe.value = false
                globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
                globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
              }
          })
          await listenDeviceReadBle(false)
          console.log('读取最新度数成功');
        } else {
          // 在线表不处理直接返回false
          return resolve(false)
        }
        // 比较上次充值失败的度数
        // const lastFailInfo = Taro.getStorageSync('rechargeFailDuInfo') ? JSON.parse(Taro.getStorageSync('rechargeFailDuInfo')) : null//度数上次充值失败的信息
        if (failOrder && failOrder.device.sn == device.value.sn) {
          console.log('上次充值失败度数' + failOrder.device_du + '当前度数' + device.value?.du);
          // TAG 如果当前度数 大于 (上次充值失败度数的10%+上次充值失败的表度数) 或者 当前总电量大于等于（上次充值失败的总电量+上次充值失败度数）
          if (Number(device.value?.du) > (Number((Number(failOrder.du) * 0.1).toFixed(2)) + Number(failOrder.device_du)) || (Number(device.value.total) >= (Number(failOrder.device_total) + (Number(failOrder.du) * 0.1) + Number(failOrder.device_du)))) {
            // TODO 清楚失败订单
            handleRechargeSuccess(orderNo)
            resolve(true)
          } else {
            resolve(false)
          }
        } else {
          resolve(false)
        }
      } catch {
        resolve(false)
      }
    })
  }

  const onDeviceRecharge = async() => {
    if (!globalStore.isLogin) {
      // Taro.navigateTo({
      //   url: '/pages/login/login'
      // })
      showLogin.value = true
      return
    }
    // 需要检测网络信号
    await checkNetInfo()
    const isneed = await checkIsCollectInfo()
    // p判断是否添加过合同没有的话提示
    if (device.value?.need_people == 1 && (!fastInfo.value?.contract_id || fastInfo.value?.contract_id == 0)) {
       Taro.showModal({
          title: '温馨提示',
          showCancel: false,
          content: '请先联系房东办理快捷入住。',
          success: function (res) {
            // if (res.confirm) {
                Taro.switchTab({
                  url: '/pages/index/index'
                })
            // } else if (res.cancel) {
            //   console.log('用户点击取消')
            // }
          }
        })
        return
    }
    if (device.value?.need_people == 2 && (!fastInfo.value?.contract_id || fastInfo.value?.contract_id == 0)) {
       Taro.showModal({
          title: '温馨提示',
          showCancel: false,
          content: '请联系房东办理快捷入住，并完成入住申报！',
          success: function (res) {
            // if (res.confirm) {
                Taro.switchTab({
                  url: '/pages/index/index'
                })
            // } else if (res.cancel) {
            //   console.log('用户点击取消')
            // }
          }
        })
        return
    }
    if (isneed) {
      if (fastInfo.value?.need_type && fastInfo.value?.need_type == 2) {
          // 公安采集
          return Taro.showModal({
            title: '温馨提示',
            showCancel: false,
            content: '应公安监管部门的要求，请完成入住人员信息登记，感谢您的配合。',
            success: function (res) {
                console.log('用户点击确定')
              if (fastInfo.value.is_master == 1) {
                // 是承租人跳转
                Taro.navigateTo({
                  url:"/pages/tenant/tenantGongAnInfo/tenantGongAnInfo"
                })
            } else {
                Taro.switchTab({
                  url: '/pages/index/index'
                })
            }
            }
          })
        } else {
          // 普通采集
        return Taro.showModal({
          title: '温馨提示',
          showCancel: false,
          content: '应监管部门的要求，请完成入住人员身份信息登记，感谢您的配合。',
          success: function (res) {
            if (fastInfo.value.is_master == 1) {
                // 是承租人跳转
                Taro.navigateTo({
                  url:"/pages/tenant/tenantInfo/tenantInfo"
                })
            } else {
                Taro.switchTab({
                  url: '/pages/index/index'
                })
            }
          }
        })
        }
    }


    // 公安采集 查询是否可充值
    if (device.value?.need_people == 2 && fastInfo.value) {
      try {
        const resChange = await request
           .get({
             url: "contract/checkRecharge",
             data: {
               contract_id: fastInfo.value?.contract_id,
             },
             showToast:true
           })
      if(resChange.code != 200) return
      } catch (e) {
        return
      }
    }
    

    // check_status 0 未审核 1 审核通过 2 审核不通过
    //  判断是否审核过
    if (fastInfo.value?.need_type == 2 && fastInfo.value?.check_status == 0) {
      Taro.showModal({
          title: '温馨提示',
          showCancel: fastInfo.value?.is_master == 1 ? true : false,
          cancelText: '去修改',
          confirmText:'知道了',
          content: '房东未确认，请等待房东确认入住人员身份信息！',
          success: function (res) {
              if (res.confirm) {
                Taro.switchTab({
                 url: '/pages/index/index'
                })
              } else {
                Taro.navigateTo({
                 url: '/pages/tenant/tenantGongAnInfo/tenantGongAnInfo'
                })
              }
          }
        })
      return
    }

    if (fastInfo.value?.need_type == 2 && fastInfo.value?.check_status == 2) {
      Taro.showModal({
          title: '温馨提示',
          showCancel: false,
          content: '您的入住信息审核不通过,请检查信息后重新提交!',
          success: function (res) {
            if (res.confirm) {
                Taro.navigateTo({
                  url: '/pages/tenant/tenantGongAnInfo/tenantGongAnInfo'
                })
            } else {
              Taro.switchTab({
                url: '/pages/index/index'
              })
            }
          }
        })
      return
    }

    // if (fastInfo.value.now_count > fastInfo.value.need_count) {
    //     // 如果认证的人数大于需要认证的数量提示用户去删除房客
    //     return Taro.showModal({
    //       title: '温馨提示',
    //       showCancel: false,
    //       content: '您当前入住人数比已认证的人数有所减少,请删除部分房客才能充值',
    //       success: function (res) {
    //         if (res.confirm) {
    //           console.log('用户点击确定')
    //           Taro.navigateTo({
    //             url:"/pages/tenant/tenantInfo/tenantInfo"
    //           })
    //           Taro.switchTab({
    //             url: '/pages/index/index'
    //            })
    //         } else if (res.cancel) {
    //           console.log('用户点击取消')
    //         }
    //       }
    //     })
    //   }
    if (!device.value.can_recharge) {
      Taro.showToast({
        title: '账单逾期，限制充值',
        icon: 'error'
      })
      return;
    }
        // 查看是否有补充订单 有则提示
   const orderFailRes = await request.get({
        url: 'order/fail',
        data: {
            deviceId: device.value.id
        }
    })
    if (orderFailRes.data.fail) {
      console.log(orderFailRes.data.fail,"orderFailRes.data.fail");
      orderFail.value = orderFailRes.data.fail
      // const isTrue = await checkLastFailOrder(orderFailRes.data.fail?.order_no)
      // console.log(isTrue,"isTrue");
      
      // if (!isTrue) {
        showFail.value = true
      // }
      return
    }
    // 4g无信号时 提示先抄表
    if (device.value.net_type == 1 && device.value?.signal <= 0) {
      if(flagRe.value) return
      flagRe.value = true
      globalStore.setGlobDialog({show:true,type:'BleConnectionTip'})
      return
    }

    if (device.value.net_type === 2) {
      if(flagRe.value) return
      flagRe.value = true
      // 蓝牙 提示先抄表
      globalStore.setGlobDialog({show:true,type:'BleConnectionTip'})
    } else {
      flagRe.value = false
      globalStore.setGlobDialog({show:false,type:''})
      Taro.navigateTo({
          url: '/pages/device/recharge/recharge?id=' + id.value + '&who=tenant' +'&show_order=' + device.value?.show_order
        })
    }
  }

  // const listenDeviceMessage = () => {
  //   if (globalStore.listenClient !== device.value.mac + '-3') {
  //     return
  //   }
  //   setTimeout(() => {
  //     if (globalStore.device.message.length > 0) {
  //       let msg = globalStore.device.message.shift()
  //       console.log(msg, 'listenDeviceMessage3')
  //       if (msg.du > -1) {
  //         device.value.du = msg.du
  //         device.value.read_at = formatTime(new Date())
  //         Taro.showToast({
  //           title: '剩余电量已更新',
  //           duration: 2000
  //         })
  //         let postData = {
  //           du: msg.du,
  //         }
  //         if (msg.total > -1) {
  //           postData.total = msg.total
  //         }
  //         if (msg.power > -1) {
  //           postData.power = msg.power
  //         }
  //         request.post({
  //           url: 'device/' + id.value + '/read',
  //           data: postData,
  //           showLoading: false
  //         })
  //       }
  //       if (msg.speed > -1) {
  //         device.value.speed = msg.speed
  //       }
  //       if (msg.status > -1) {
  //         device.value.status = msg.status
  //       }
  //     }
  //     listenDeviceMessage()
  //   }, 1000)
  // }

  // 操表
  const listenDeviceReadBle = (toast=true) => {
    let s = 0
    s=0
    clearInterval(timerRed.value)
    return new Promise((resolve, reject) => {
      timerRed.value = setInterval(() => {
        s ++

        if (globalStore.device.message.length > 0) {
          let msg = globalStore.device.message.shift()
          console.log(msg, device.value.mac)
          if (msg.mac === device.value.mac) {
            if (msg.event === '0C') {
              device.value.status = msg.status
              device.value.du = msg.du
              device.value.read_at = formatTime(new Date())
              let postData = {
                du: msg.du,
                status: msg.status,
                info: globalStore.who,
                log: 1
              }
              postData.total = msg.total
              postData.power = msg.power
              device.value.total = msg.total
              request.post({
                url: 'device/' + id.value + '/read',
                showLoading: false,
                data: postData
              }).then(_=> {
                s=0
                Taro.hideLoading()
                clearInterval(timerRed.value)
                if (toast) {
                  globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
                  Taro.showToast({
                    title: '抄表成功',
                    duration: 3000
                  })
                }
                resolve()
              }).catch(() => {
                clearInterval(timerRed.value)
                Taro.hideLoading()
              })
            }

            if (s >= 25) {
              s=0
              Taro.hideLoading()
              clearInterval(timerRed.value)
              Taro.showToast({
                title: '抄表超时',
                icon: 'error',
                duration: 2000
              })
              reject()
            }
          }
        }
        
        // if (s >= 20) {
        //   Taro.hideLoading()
        //   clearInterval(timer)
        //   Taro.showToast({
        //     title: '抄表超时',
        //     icon: 'error',
        //     duration: 2000
        //   })
        //   reject()
        // }

      }, 1000)
    })
  }

  const showLogin = ref(false)
  const onLoginClose = () => {
    showLogin.value = false
  }
  const onLogin = () => {
    showLogin.value = false
    // 接口存进登录态
    if (share.value && sn.value) {
      // if (device.value?.type == 2) return//水表不存入登录态
      request.get({
        url: 'user/bindDevice',
        data: {
          sn:sn.value
        }
      }).then((_res) => {
      })
    }
  }

  // 查询是否存在充值失败的订单
  const getFailOrder = (deviceId) => {
    if (!globalStore.isLogin) {
      return
    }
    request.get({
        url: 'order/fail',
        data: {
            deviceId: deviceId
        }
    }).then(async(res) => {
      console.log(res, 'fail order')
        if (res.data.fail) {
          orderFail.value = res.data.fail
          // const isTrue = await checkLastFailOrder(res.data.fail?.order_no)
          // if (!isTrue) {
            showFail.value = true
          // }
        }
    })
  }
  const showFail = ref(false)
  const onChangeShowFail = () => {
    showFail.value = !showFail.value
    console.log(showFail.value)
  }

  const onJumpFailOrder = async () => {
    console.log(orderFail.value,"orderFail");
      const isTrue = await checkLastFailOrder(orderFail.value?.order_no,orderFail.value)
      if (!isTrue) {
        Taro.redirectTo({
          url: '/pages/device/failOrder/failOrder?id=' + device.value.id
        })
      } else {
        showFail.value = false
        Taro.showToast({
          title: '当前度数大于上次充值失败的度数,无需补充订单',
          icon: 'none'
        })
      }
  }

  const onCall = (phone) => {
    Taro.makePhoneCall({
      phoneNumber: phone || device.value.business.user.mobile
    })
  }

  </script>
  <style lang="scss">
  .content-modal2 {
    .btn-box {
    position: absolute;
    left: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    border-top: 1px solid #dcdcdc;
    width: 100%;
    line-height: 100px;
    height: 100px;
    font-weight: 500;

    view {
      color: #0173FF;
      text-align: center;
      width: 50%;

      &:active {
        opacity: .85;
      }
    }

    .btn1 {
      border-right: 1px solid #dcdcdc;
      color: #000;
    }
  }
  }
    .ban01 {
      margin: 0 25px;
      border-radius: 20px;
      background-color: #FFFFFF;
      box-shadow: 0 7px 29px 0px rgba(112,145,178,0.2);
      image {
        border-radius: 20px;
        width: 100%;
        height: 261px;
        object-fit: cover;
      }
    }
    .tdd {
      width: 100%;
      padding-top: 67px;
      padding-bottom: 25px;
      background: linear-gradient(180deg, #1452FD 63%, #FFFFFF 100%);
    }
    .tdd-box {
      margin: 25px;
      background: #FFFFFF;
      box-shadow: 0 7px 29px 0px rgba(112,145,178,0.2);
      border-radius: 25px;
      padding: 31px;
      padding-bottom: 250px;
    }
    .device-top {
      margin-bottom: 25px;
      position: relative;
      .reload-btn {
        position: absolute;
        right: 30px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        justify-content: center;
        align-items: center;
        color: #1A4DF5;
        .iconfont {
          font-size: 40px;
          color: #1A4DF5;
          margin-bottom: 0;
        }
        text {
          margin-bottom: 2px;
        }
      }

      .device-name {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        font-family: OPPOSans;
        font-weight: 500;
        color: #000000;
      }
      .device-sn {
        font-size: 24rpx;
        font-family: OPPOSans;
        font-weight: 500;
        color: #000000;
      }
      .device-icon {
        width: 60rpx;
        height: 60rpx;
        background: #E9EFFA;
        border-radius: 10rpx;
        margin-right: 12px;
        text-align: center;
        padding: 2px 0;
      }
    }
    .house-name {
      // margin-bottom: 20px;
      font-size: 25px;
      margin-left: 22px;
    }
    .device-du {
      text-align: center;
      padding: 71px 0 51px 0;
    }
    .device-du1 {
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #9BA3BA;
    }
    #zifei-box-sm {
      width: 64px;
      height: 26px;
      background: #DAEAFC;
      border-radius: 13px;
      font-size: 18px;
      color: #468FFE;
      display: inline-block;
      margin-left: 15px;
    }
    .device-du2 {
      font-size: 100rpx;
      font-family: Bahnschrift;
      font-weight: 400;
      color: #000000;
    }
    .btn-cb {
      width: 189px;
      background: #E9F3FF;
      border-radius: 38px;
      text-align: center;
      display: inline-block;
      padding: 24px 44px;
    }
    .d-bar2 {
      background: #F1F3F9;
      border-radius: 25rpx;
      padding: 20px;
      margin-top: 45px;
      .tip-box  {
        width: 100%;
        height: 92px;
        background: #FCC8C8;
        border-radius: 15px;
        font-size: 30px;
        color: #F50707;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        font-weight: 700;
      }
    }
    .color-gray {
      font-size: 30rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #9BA3BA;
    }
    .btn-rc {
      padding: 30px;
      button {
        border-radius: 49rpx;
      }
    }
    .fail-title {
  font-size: 40px;
  font-weight: 500;
  color: #000000;
  padding: 56px 0;
  text-align: center;
}
.fail-tip {
  margin-top: 20px;
  margin-left: 67px;
  margin-right: 67px;
  padding: 57px 47px;
  background: #F1F3F9;
  border-radius: 25px;
  font-size: 30px;
  font-weight: 500;
  color: #5A6278;
  line-height: 44px;
}
.fail-btn-w {
  width: 40%;
}
.fail-btn {
  margin-top: 80px;
  padding: 32px 50px;
  border-top: 1px solid #DFDFDF;
}
.recharge-confirm-zf {
  padding: 65px;
  padding-top: 30px;
  >view {
    display: flex;
    margin-top: 32px;
    justify-content: space-between;
    .device-lab {
      font-size: 30px;
      color: #9BA3BA;
    }
    .device-num {
      font-size: 30px;
      color: #000000;
    }
  }
}
  </style>
