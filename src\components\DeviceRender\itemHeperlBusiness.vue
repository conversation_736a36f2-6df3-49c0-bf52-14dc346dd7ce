<script setup>
import MyIcon from "@/components/MyIcon";
import request from "@/utils/request";
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import { onMounted, ref, unref, watch } from "vue";
import MyPopup from "@/components/MyPopup";
import QueryPricesPopUp from '@/components/queryPricesPopUp'
import {
  createPwdHandel,
  toReadRecord,
  getLockPwdList,
  getLockList,
} from "@/utils/lock";

import { AES_Encrypt, AES_Decrypt, MD5_Encrypt } from "@/utils/crypto";
import { useGlobalStore } from "@/stores";
const globalStore = useGlobalStore();

const dian = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/dian-bg.png";
const routeimg = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/route.png";
const yuanimg = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/yuan-bg.png";
const suoimg = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/key-2.png";
const suoimgOpen = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/key-1.png";

import { union } from "lodash";
import { clientId, clientSecret } from "@/config";

const props = defineProps({
  type: {
    type: String,
    required: true,
    default: "1", //1：快速入住 2：门锁 3：水表 4：电表
  },
  fast_contract: {
    type: Object,
    default: () => ({}),
  },
  device: {
    type: Object,
    default: () => ({}),
  },
  waterDevice: {
    type: Object,
    default: () => ({}),
  },
  detail: {
    type: Object,
    default: () => ({}),
  },
  curentType: {
    type: String,
    default: "4",
  },
});

const itemText = () => {
  if (props.device?.type == 5 || props.device?.type == 6) {
    if (props.device?.pay_mode == 1) {
      // 预付费
      return '剩余金额(元)'
    } else {
      //后付费
      return '已用金额(元)'
    }
  } else {
    return '剩余电量(度)'
  }
}

const keyList = ref([]);

const keyInfo = ref(null); //锁信息

const specialValueObj = ref(); //锁特征

const updateDianTime = ref(Date.now()); //电量更新时间

const lockShow = ref(false); //锁弹窗

const isOpenLock = ref(false); //是否正在开锁

const ttlockAccount = ref(); //锁账户

const tokenVal = ref();

const priceShow = ref(false); //是否显示价格

onMounted(async () => {
  // await regsion();
  // await getToken()
  // 初始化
  // console.log("itemheeeeee**onMounted");
  // requirePlugin("myPlugin", ({ parseSpecialValues }) => {
  //   specialValueObj.value = parseSpecialValues(keyInfo.value.featureValue);
  //   console.log(
  //     parseSpecialValues(keyInfo.value.featureValue),
  //     "specialValueObj"
  //   );
  // });
});

/**
 * 注册
 */
const regsion = () => {
  return new Promise((resolve, reject) => {
    const option = {
      username: "***********shanzupo",
      password: MD5_Encrypt("***********"),
      date: new Date().getTime(),
    };
    request
      .post({
        url: "/v3/user/register",
        LOCKYAPI: true,
        data: {
          clientId,
          clientSecret,
          ...option,
        },
      })
      .then((res) => {
        console.log(res);
        resolve();
      });
  });
};

/**
 * 获得token
 */
const getToken = ({ username, password }) => {
  return new Promise((resolve, reject) => {
    request
      .post({
        url: "/oauth2/token",
        LOCKYAPI: true,
        data: {
          username,
          password,
          client_id: clientId,
          client_secret: clientSecret,
          grant_type: "password",
          redirect_uri: "http://www.sciener.cn",
        },
      })
      .then((res) => {
        console.log(res);
        resolve();
        globalStore.setTtlaccessToken(res.access_token);
      });
  });
};

useDidShow(() => {
});

watch(
  () => props.curentType,
  () => {
    console.log("okokokoo");
    if (props.curentType != 2) return;
    // 获取钥匙列表
    console.log("获取钥匙列表");
    getLockList().then((res) => {
      console.log(res);
      keyList.value = res.list;
      keyInfo.value = res.list[0];
      globalStore.setkeyInfo(keyInfo.value);
      console.log(keyInfo.value, "keyInfo.value ");
    });
  },
  { immediate: true }
);

const isOPenLockShow = ref(false); //是否开锁

/**
 * 开锁
 */
const openLockHandel = () => {
  isOpenLock.value = true;
  Taro.showLoading({ title: "正在开锁" });
  requirePlugin("myPlugin", ({ controlLock }) => {
    const start = Date.now();
    // 控制智能锁
    controlLock({
      /* 控制智能锁方式 3 -开锁, 6 -闭锁 */
      controlAction: 3,
      lockData: unref(keyInfo).lockData,
      serverTime: Date.now(),
    }).then((res) => {
      console.log(res);
      if (res.errorCode == 0) {
        Taro.showToast({ icon: "success", title: "已开锁" });
        isOPenLockShow.value = true;
        toReadRecord({ keyInfo: keyInfo.value }).then(() => {
          updateDianTime.value = new Date().getTime();
        });
        isOpenLock.value = false;
      } else {
        Taro.hideLoading();
        isOpenLock.value = false;
        isOPenLockShow.value = false;
        Taro.showToast({
          title: `开锁失败：${res.errorMsg}`,
          icon: "none",
          duration: 5000,
        });
      }
    });
  });
};

/**
 * 删除密码
 */
const delPwdHandel = async (keyboardPwdId) => {
  return new Promise((resolve, reject) => {
    request
      .post({
        url: "/v3/keyboardPwd/delete",
        LOCKYAPI: true,
        data: {
          lockId: unref(keyInfo).lockId,
          // clientId: "42362269013d48b3bdaa85c7b83f6356",
          clientId: "7946f0d923934a61baefb3303de4d132",
          accessToken: "91d30a70ba5bdc691d1004a5e6daef94",
          keyboardPwdId,
          deleteType: 1, //1-通过APP走蓝牙删除，不传默认1，必需先通过APP蓝牙删除后再调用该接口 2-通过网关或WiFi锁删除，如果是WiFi锁或有连接网关，则可以传2，直接调用该接口从锁里删除密码
          date: new Date().getTime(),
        },
      })
      .then((res) => {
        console.log(res);
        resolve(res.list);
      });
  });
};

const keyboardPwd = ref(); //普通自定义密码（永久）

function timestampToDate(timestamp) {
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${("0" + (date.getMonth() + 1)).slice(-2)}-${(
    "0" + date.getDate()
  ).slice(-2)} ${("0" + date.getHours()).slice(-2)}:${(
    "0" + date.getMinutes()
  ).slice(-2)}:${("0" + date.getSeconds()).slice(-2)}`;
}

const addLockHandel = (e) => {
  console.log(props);
  const item = {
    house_name: props.detail.house_name,
    status: props.detail.status,
    house_id: props.device.house_id,
    tenantMobile: props.detail.contracts?.mobile,
  };
  // return
  Taro.navigateTo({
    url: "/pages/house/addLock/addLock?item=" + JSON.stringify(item),
  });
};

// 获取账户
const getAccount = () => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "ttlock/userDetail",
      })
      .then((res) => {
        console.log(res);
        if (!res.data.length || !res.data) {
          // regsion();
        } else {
          ttlockAccount.value = res.data.filter((item) => item.type == "1")[0];
          getToken({
            username: ttlockAccount.value.username,
            password: ttlockAccount.value.password_hash,
          }).then(() => {
            resolve();
          });
        }
      });
  });
};

const lockDetailJump = async () => {
  console.log(props);
  await getAccount();
  const item = {
    lockId: props.detail.ttlock[0].lock_id,
    house_name: props.detail.house_name,
    status: props.detail.status,
    house_id: props.device.house_id,
    fastMobile: props.fast_contract?.mobile,
  };
  Taro.navigateTo({
    url: "/pages/house/lockDetail/lockDetail?item=" + JSON.stringify(item),
  });
};

const toBindDevice = () => {
  Taro.navigateTo({
    url: "/pages/house/addDevice/addDevice?house_id=" + props.detail?.id,
  });
};

// 添加水表
const addWaterDeviceHandel = () => {
  // console.log(props.detail);
  if (!globalStore.isLogin) {
    Taro.showToast({
      title: "请先登录",
      icon: "error",
    });
    return;
  }
  Taro.scanCode({
    scanType: "qrcode",
    success: (res) => {
      console.log(res, "scan result");
      if (!res.result) {
        Taro.showToast({
          title: "扫码失败",
          icon: "error",
        });
        return;
      }
      let sn = res.result.trim();
      sn.replace(/[\r\n]/g, "");
      if (sn.substring(0, 5) === "https") {
        let _arr = res.result.split("sn=");
        console.log(_arr);
        if (_arr.length > 0) {
          sn = _arr[1];
        }
      }
      console.log(sn);
        request
        .get({
          url: "device/search",
          data: {
            sn: sn,
          },
        }).then((res) => {
        globalStore.setIndexPageScrollTop(0)
        globalStore.setIndexRecharegeId(false)
          Taro.navigateTo({
            url:
              `/pages/house/addWaterDevice/addWaterDevice?house_id=${props.detail?.id}&sn=${sn}&house_name=${props.detail?.house_name}`,
          });
        })
    },
  });
};
</script>

<template>
  <!-- 快速入住 -->

  <!-- 水表 -->
  <template v-if="curentType == 3 && !waterDevice?.id">
    <view class="shui-box" @tap.stop="addWaterDeviceHandel">
      <MyIcon icon="icon-scan2" width="34rpx" height="34rpx"> </MyIcon>
      <text>添加水表</text>
    </view>
  </template>

  <!-- 门锁 -->
  <template v-if="curentType == 2">
    <view
      class="lock-container"
      @tap.stop="addLockHandel"
      v-if="!detail.ttlock.length"
    >
      <!-- <view class="top" @tap.stop="lookPwdHandel">
        <text>查看密码</text>
      </view> -->
      <view class="midle">
        <!-- <view class="dian">{{ keyInfo?.electricQuantity }}%</view> -->
        <!-- <view class="desc">剩余电量</view> -->
        <view class="emty-lock"> 请先添加“门锁” </view>
      </view>
    </view>
    <view
      class="lock-container"
      @tap.stop="lockDetailJump"
      v-if="detail.ttlock.length"
    >
      <view class="midle">查看门锁 </view>
    </view>
    <!-- <button class="button" @tap.stop="lockShow = true">一键开锁</button> -->
  </template>
  <!-- 添加电表 -->
  <template v-if="curentType == 4 && !device?.id">
    <view class="add-container" @tap.stop="toBindDevice">
      <view class="midle">
        <view class="emty-lock"> 请先添加“电表” </view>
      </view>
    </view>
  </template>
  <!-- 电表 -->
  <template v-if="curentType == 4 && device?.id">
    <view class="device-info flex flex-space-between flex-v-center">
        <!-- 4g无信号显示异常提示 -->
      <!-- <text class="net-type-wraing" v-if="device.net_type == 1 && device.signal <= 0">
        设备离线
      </text> -->
      <view>
        <view class="device-num">{{ Number(device?.total || 0).toFixed(2) }}</view>
        <view class="device-lab">总电量</view>
      </view>
      <view :class="{red: (device?.type == 5 || device?.type == 6) && device?.pay_mode == 2}">
        <view class="device-num">{{ device.du || 0 }}</view>
        <view class="device-lab">{{ itemText() }}</view>
      </view>
      <view v-if="device?.type == 5 || device?.type == 6" @tap.stop="priceShow = true">
        <view class="device-num">查询</view>
        <view class="device-lab">分段电价</view>
      </view>
      <view v-else>
        <view class="device-num">{{ device.price }}</view>
        <view class="device-lab">单价(元/度)</view>
      </view>
    </view>
    <view
      style="padding: 10rpx 20rpx 0 20rpx"
      class="btm-b"
      v-if="globalStore.who == 'tenant'"
    >
      <view class="le">
        <image
          src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
          mode="scaleToFill"
          class="icon-s"
        />
        <text>
          <text class="text-v-center font-28 _name" v-if="device.house">{{
            device.house.name
          }}</text>
          <text
            class="close-box"
            v-if="(device.du || 0) < 0.6 && device.status == 2"
          >
            欠费断电
          </text>
        </text>
        <text class="text-v-center font-28 _name" v-if="!device.house"
          >未绑定房源</text
        >
      </view>
    </view>
  </template>
    <!-- 水表 -->
  <template v-if="curentType == 3 && waterDevice?.id">
    <view class="device-info flex flex-space-between flex-v-center" style="background: #f0fcfc;">
      <view style="background: transparent;">
        <view class="device-num">{{ Number(waterDevice?.total || 0).toFixed(2) }}</view>
        <view class="device-lab">总水量</view>
      </view>
      <view style="background: transparent;">
        <view class="device-num">{{ waterDevice.du || 0 }}</view>
        <view class="device-lab">剩余水量(m³)</view>
      </view>
      <view style="background: transparent;">
        <view class="device-num">{{ waterDevice.price }}</view>
        <view class="device-lab">单价(元/m³)</view>
      </view>
    </view>
    <view
      style="padding: 10rpx 20rpx 0 20rpx"
      class="btm-b"
      v-if="globalStore.who == 'tenant'"
    >
      <view class="le">
        <image
          src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
          mode="scaleToFill"
          class="icon-s"
        />
        <text>
          <text class="text-v-center font-28 _name" v-if="waterDevice.house">{{
            waterDevice.house.name
          }}</text>
          <text
            class="close-box"
            v-if="(waterDevice.du || 0) < 0.01 && waterDevice.status == 2"
          >
            欠费断水
          </text>
        </text>
        <text class="text-v-center font-28 _name" v-if="!waterDevice.house"
          >未绑定房源</text
        >
      </view>
    </view>
  </template>
  <!-- popup -->
  <MyPopup :show="lockShow" @close="lockShow = false">
    <template #content>
      <view class="contant-lock-pop" @tap.stop="">
        <view class="top-t">
          <text class="close" @tap.stop="lockShow = false">关闭</text>
          <text class="tit">蓝牙开锁</text>
          <text class="look" @tap.stop="lookPwdHandel">查看密码</text>
        </view>
        <view class="content">
          <image :src="dian" class="dianimg" mode="aspectFill"></image>
          <image
            :src="routeimg"
            class="routeimg"
            :class="isOpenLock ? 'route' : 'route paused'"
            mode="aspectFill"
          ></image>
          <image
            :src="yuanimg"
            class="yuanimg"
            mode="aspectFill"
            @tap.stop="openLockHandel"
          ></image>
          <view class="suo" @tap.stop="openLockHandel">
            <image
              :src="isOPenLockShow ? suoimgOpen : suoimg"
              class="suoimg"
              mode="aspectFill"
            ></image>
            <text>{{ isOPenLockShow ? "已开锁" : "点击开锁" }}</text>
          </view>
          <view class="bottom">
            <view class="dian"
              >您的门锁剩余电量
              <text>{{ keyInfo?.electricQuantity }}%</text></view
            >
            <view class="desc"
              >更新于{{ timestampToDate(updateDianTime) }}</view
            >
          </view>
        </view>
      </view>
    </template>
  </MyPopup>
  <QueryPricesPopUp :device="device" :show="priceShow" @close="priceShow = false" />
</template>

<style lang="less">
.red {
  >view {
    color: red !important;
  }
}
.contant-lock-pop {
  height: 75vh;
  width: 100%;
  background-color: #fff;
  border-top-left-radius: 32px;
  border-top-right-radius: 32px;
  .top-t {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    .close {
      font-size: 24px;
      color: #9da5b3;
    }
    .tit {
      font-size: 36px;
      color: #000000;
    }
    .look {
      font-size: 24px;
      color: #1056fa;
    }
  }
  .content {
    position: relative;
    background: url("../../assets//picss/bottom-bg.png") no-repeat;
    background-size: 538px 538px;
    background-position: center 100px;
    height: 100%;
    margin-top: 30px;
    .bottom {
      position: absolute;
      left: 50%;
      bottom: 50px;
      transform: translateX(-50%);
      text-align: center;
      .dian {
        font-weight: 500;
        font-size: 24px;
        color: #000000;
        text {
          color: #196ffc;
        }
      }
      .desc {
        font-weight: 500;
        font-size: 24px;
        color: #9a9a9a;
      }
    }
    .dianimg {
      width: 671px;
      height: 188px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
    .routeimg {
      width: 538px;
      height: 538px;
      position: absolute;
      left: 50%;
      top: 100px;
      transform: translateX(-50%);
      &.route {
        animation: route 5s linear infinite;
      }
      &.paused {
        animation-play-state: paused;
      }
      @keyframes route {
        0% {
          transform: translateX(-50%) rotate(0deg);
        }
        100% {
          transform: translateX(-50%) rotate(360deg);
        }
      }
    }
    .yuanimg {
      width: 308px;
      height: 308px;
      position: absolute;
      left: 50%;
      top: 215px;
      transform: translateX(-50%);
    }
    .suo {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: absolute;
      left: 50%;
      top: 320px;
      transform: translateX(-50%);
      font-size: 18px;
      .suoimg {
        width: 48px;
        height: 59px;
        margin-bottom: 10px;
      }
    }
  }
}
.shui-box {
  position: relative;
  height: 159px;
  background: #f0fcfc;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #1056fa;
  text {
    margin-left: 10px;
  }
}
.lock-container,
.add-container {
  position: relative;
  height: 159px;
  background: #f0f4ff;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  .top {
    position: absolute;
    right: 20px;
    top: 20px;
    width: 111px;
    height: 29px;
    background: #f0f4ff;
    border-radius: 15px 13px 13px 15px;
    font-weight: 500;
    font-size: 18px;
    color: #2f61fd;
    border: 1px solid #1453fd;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .midle {
    text-align: center;
    color: #1f5bfd;

    .dian {
      font-weight: bold;
      font-size: 36px;
      color: #1353fd;
    }
    .desc {
      font-weight: 500;
      font-size: 18px;
      color: #7e869d;
    }
    .emty-lock {
      font-family: OPPOSans;
      font-weight: 400;
      font-size: 30px;
      color: #1f5bfd;
      text-decoration-line: underline;
    }
  }
}
.button {
  width: 100%;
  height: 61px;
  background: #1353fd;
  border-radius: 20px;
  border: none;
  color: #fff;
  font-size: 26px;
  margin-top: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  &:active {
    opacity: 0.9;
  }
}
.device-info {
  position: relative;
  background: #f0f4ff;
  border-radius: 20px;
  padding: 10px 25px;
  text-align: center;
  height: 159px;
  box-sizing: border-box;
  .net-type-wraing {
    position: absolute;
    right: 35px;
    top: 0px;
    font-size: 20px;
    color: red;
    font-weight: 700;
  }

  > view {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 175px;
    height: 120px;
    background: #f0f4ff;
    border-radius: 20px;
  }

  .device-num {
    font-size: 40rpx;
    color: #1452fd;
    font-family: Bahnschrift;
    font-weight: 400;
    margin-bottom: 10px;
    font-weight: 700;
    display: flex;
    align-items: center;

    .du-t {
      max-width: 150px;
      overflow: hidden;
      display: inline-block;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #1452fd !important;
      font-size: 40px !important;
      margin-top: 0 !important;
    }

    text {
      font-weight: 500;
      font-size: 18px;
      color: #4460b3;
      margin-top: 5px;
    }
  }

  .device-lab {
    font-size: 26rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #879bd6;
  }
}

.close-box {
  display: inline-block;
  font-size: 24px;
  color: #f2811a;
  background-color: #fce9d1;
  padding: 5px 10px;
  margin-right: 10px;
  border-radius: 6px;
  margin-left: 20px;
  vertical-align: middle;
}
</style>
