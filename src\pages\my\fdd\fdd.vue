<template>
  <view class="container">
    <view class="form-item" v-if="fdd.status < 2">
      <MyCell prefix="认证类型" prefixWidth="240" align="right" required>
        <template #content>
          <view>
            <view class="my-radio" :class="fdd.is_corp === 0 ? 'my-radio-checked' : ''" @tap="onChange('is_corp', 0)">个人</view>
            <view class="my-radio" :class="fdd.is_corp === 1 ? 'my-radio-checked' : ''" @tap="onChange('is_corp', 1)">企业</view>
          </view>
        </template>
      </MyCell>

      <MyCell prefix="真实姓名" align="right" v-if="fdd.is_corp === 0" required>
        <template #content>
          <input v-model="fdd.user_name" placeholder="请输入真实姓名" />
        </template>
      </MyCell>
      <MyCell prefix="企业名称" align="right" v-if="fdd.is_corp === 1" required>
        <template #content>
          <input v-model="fdd.user_name" placeholder="请输入企业名称" />
        </template>
      </MyCell>
      <MyCell prefix="证件类型" prefixWidth="240" align="right" required>
        <template #content>
          <!--id_card身份证，corp: 企业，individual_biz: 个体工商户-->
          <view>
            <view v-if="fdd.is_corp === 0" class="my-radio" :class="fdd.ident_type === 'id_card' ? 'my-radio-checked' : ''" @tap="onChange('ident_type', 'id_card')">身份证</view>
            <view v-if="fdd.is_corp === 1" class="my-radio" :class="fdd.ident_type === 'corp' ? 'my-radio-checked' : ''" @tap="onChange('ident_type', 'corp')">企业执照</view>
            <view v-if="fdd.is_corp === 1" class="my-radio" :class="fdd.ident_type === 'individual_biz' ? 'my-radio-checked' : ''" @tap="onChange('ident_type', 'individual_biz')">个体户执照</view>
          </view>
        </template>
      </MyCell>
      <MyCell prefix="证件号码" align="right" required>
        <template #content>
          <input v-model="fdd.ident_no" placeholder="请输入证件号码" />
        </template>
      </MyCell>

      <MyCell prefix="手机号码" align="right" >
        <template #content>
          <input v-model="fdd.mobile" disabled placeholder="请输入手机号码" />
        </template>
      </MyCell>

    </view>
    <view v-if="fdd.status === 2">
      <MyCell prefix="认证类型" prefixWidth="240" align="right" >
        <template #content>{{ fdd.is_corp === 1 ? '企业' : '个人' }}</template>
      </MyCell>
      <MyCell prefix="真实姓名" align="right" v-if="fdd.is_corp === 0">
        <template #content>
          {{fdd.user_name}}
        </template>
      </MyCell>
      <MyCell prefix="企业名称" align="right" v-if="fdd.is_corp === 1">
        <template #content>
          {{fdd.user_name}}
        </template>
      </MyCell>
      <MyCell prefix="证件类型" prefixWidth="240" align="right">
        <template #content>
          <!--id_card身份证，corp: 企业，individual_biz: 个体工商户-->
          <view><text v-if="fdd.ident_type === 'id_card'">身份证</text>
            <text v-if="fdd.ident_type === 'corp'">企业执照</text>
            <text v-if="fdd.ident_type === 'individual_biz'">个体户执照</text></view>
        </template>
      </MyCell>
      <MyCell prefix="证件号码" align="right">
        <template #content>
          {{fdd.ident_no}}
        </template>
      </MyCell>

      <MyCell prefix="手机号码" align="right" >
        <template #content>
          {{fdd.mobile}}
        </template>
      </MyCell>
    </view>
    <view class="footer-fixed" >
      <view class="p20">
        <button class="btn-primary" v-if="fdd.status < 2" @tap="onSubmit">立即认证</button>
        <view v-if="fdd.status === 2 && !fdd.seal_id && globalStore.who === 'business'" @tap="onFreeSign">
          <button class="btn-primary" >免签授权</button>
          <view class="p20 color-low">(授权后创建电子合同时房东自动签名)</view>
        </view>
        <view><button class="btn-second" v-if="fdd.status === 2" @tap="onUnbind">取消认证</button></view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";

import MyCell from '@/components/MyCell'
import {useGlobalStore} from "@/stores";

definePageConfig({
  navigationBarTitleText: "实名认证",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});
const globalStore = useGlobalStore()

const fdd = ref({})

const onChange = (field, val) => {
  fdd.value[field] = val
}

const fetch = () => {
  request.get({
    url: 'fdd/info'
  }).then(res => {
    globalStore.userInfoHasChange = true
    fdd.value = res.data
  })
}

const onSubmit = () => {
  request.post({
    url: 'fdd/updateInfo',
    data: {
      ... fdd.value
    }
  }).then(res => {
    request.get({
      url: 'fdd/authUrl'
    }).then(res => {
      globalStore.userInfoHasChange = true
      globalStore.getUserInfo()
      Taro.redirectTo({
        url: '/pages/webview/webview?url=' + encodeURIComponent(res.data.authUrl)
      })
    })
  })
}

const onFreeSign = () => {
  request.get({
    url: 'fdd/getFreeSignUrl'
  }).then(res => {
    Taro.redirectTo({
      url: '/pages/webview/webview?url=' + encodeURIComponent(res.data.freeSignUrl)
    })
  })
}

const onUnbind = () => {
  Taro.showModal({
    title: '取消认证提示',
    content: '确认要取消电子签实名认证吗',
    success: (res) => {
      if (res.confirm) {
        request.get({
          url: 'fdd/userUnbind'
        }).then(res => {
          globalStore.userInfoHasChange = true
          globalStore.getUserInfo()
          fetch()
        })
      }
    }
  })
}

useDidShow(() => {
  fetch()
})

</script>
<style lang="scss">
.my-radio {
  background-color: #F1F3F7;
  border-radius: 6px;
  font-size: 24px;
  font-weight: 400;
  color: #B6BEC5;
  padding: 9px 15px;
  display: inline-block;
  margin-left: 16px;
}
.my-radio-checked {
  background-color: #1352FD;
  color: #FFFFFF;
}
</style>
