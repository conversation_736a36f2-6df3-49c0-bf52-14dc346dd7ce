<template>
  <view>
    <view class="house-form">
      <MyCell prefix="设备类型" required align="right">
        <template #content>
          <view class="device-type">
            <view class="device-type-item" :class="{active: deviceAll.type != 2}" @tap="changeDeviceType(1)">电表</view>
            <view class="device-type-item" :class="{active: deviceAll.type == 2}" @tap="changeDeviceType(2)">水表</view>
          </view>
        </template>
      </MyCell>
      <MyCell :prefix="deviceAll.type == 2 ? '水表编号' : '电表编号'" required align="right">
        <template #content>
          <input
            type="text"
            name="sn"
            v-model="formState.sn"
            placeholder="请输入"
            @input="changeSnHandel"
            :cursor="posCur"
          />
        </template>
        <template #suffix>
          <MyIcon
            icon="icon-scan2"
            width="34rpx"
            height="34rpx"
            @tap="onScanCode"
          ></MyIcon>
        </template>
      </MyCell>
      <MyCell v-if="deviceAll?.type != 5 && deviceAll?.type != 6" :prefix="deviceAll.type == 2 ? '基础单价' : '基础单价'" required align="right" :suffix="deviceAll.type == 2 ? '元/m³' : '元/度'">
        <template #content>
          <input
            type="digit"
            name="rent"
            v-model="formState.price"
            placeholder="请输入"
            :cursor="posCur"
            @input="bindReplaceInput"
          />
        </template>
      </MyCell>
      <MyCell v-if="deviceAll?.type != 5 && deviceAll?.type != 6" prefix="服务费"  align="right" :suffix="deviceAll.type == 2 ? '元/m³' : '元/度'">
        <template #content>
          <input  :cursor="posCur" @input="bindReplaceInput" type="digit"  name="price" cursor-spacing="120"  v-model="ServicePrice" placeholder="输入服务费" />
        </template>
      </MyCell>

       <!-- 谷峰表电价 -->
      <MyCell v-if="deviceAll?.type == 5 || deviceAll?.type == 6" prefix="尖峰电价" required align="right" suffix="元/度">
        <template #content>
          <input type="digit"  :cursor="posCur" @input="bindReplaceInput"  @focus="utilsClass = ''" @blur="utilsClass = 'footer-fixed'" name="price" cursor-spacing="120" v-model="gfPrice.coef4" placeholder="" />
        </template>
      </MyCell>
      <MyCell v-if="deviceAll?.type == 5 || deviceAll?.type == 6" prefix="高峰电价" required align="right" suffix="元/度">
        <template #content>
          <input type="digit"  :cursor="posCur" @input="bindReplaceInput"  @focus="utilsClass = ''" @blur="utilsClass = 'footer-fixed'" name="price" cursor-spacing="120" v-model="gfPrice.coef3" placeholder="" />
        </template>
      </MyCell>
      <MyCell v-if="deviceAll?.type == 5 || deviceAll?.type == 6" prefix="平段电价" required align="right" suffix="元/度">
        <template #content>
          <input type="digit"  :cursor="posCur" @input="bindReplaceInput"  @focus="utilsClass = ''" @blur="utilsClass = 'footer-fixed'" name="price" cursor-spacing="120" v-model="gfPrice.coef2" placeholder="" />
        </template>
      </MyCell>
      <MyCell v-if="deviceAll?.type == 5 || deviceAll?.type == 6" prefix="低谷电价" required align="right" suffix="元/度">
        <template #content>
          <input type="digit"  :cursor="posCur" @input="bindReplaceInput"  @focus="utilsClass = ''" @blur="utilsClass = 'footer-fixed'" name="price" cursor-spacing="120" v-model="gfPrice.coef1" placeholder="" />
        </template>
      </MyCell>

      <!-- <MyCell prefix="设备费"  align="right" :suffix="deviceAll.type == 2 ? '元/m³' : '元/度'" v-if="deviceAll.agent?.type == 2">
        <template #content>
          <text style="color: #333;"> {{ deviceAll.agent?.service }} </text>
        </template>
      </MyCell> -->
      <!-- <MyCell :prefix="deviceAll.type == 2 ? '水表名称' : '电表名称'" align="right">
        <template #content>
          <input
            type="text"
            name="alias"
            v-model="formState.alias"
            placeholder="请输入"
            :cursor="posCur"
            @input="bindReplaceInput"
          />
        </template>
      </MyCell> -->
      <view class="cell" v-if="deviceAll?.type != 5 && deviceAll?.type != 6">
        <!-- <text style="color: #333;">综合电价(基础单价+服务费{{ deviceAll.agent?.type == 2 ? '+设备费' : ''}})：{{ totalPrice }} 元/度</text> -->
        <text style="color: #333;">{{ deviceAll.type == 2 ? '综合水价(基础单价+服务费)：' : '综合电价(基础单价+服务费)：' }}{{ totalPrice }} {{ deviceAll.type == 2 ? '元/m³' : '元/度'}}</text>
      </view>
    </view>
    <view class="footer-fixed">
      <button class="btn-add m33" @tap="handleSubmit">确认保存</button>
    </view>
  </view>
</template>
<script setup>
import { ref ,computed} from "vue";
import "./addDevice.scss";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
import request from "@/utils/request";
import { debounce } from "lodash";

import MyInput from "@/components/MyInput";
import MyCell from "@/components/MyCell";
import MyIcon from "@/components/MyIcon";

import { useGlobalStore } from "@/stores";
const globalStore = useGlobalStore();

const ServicePrice = ref('')//服务费

const posCur = ref(-1)

const bindReplaceInput = (e) => {
  let pos = e.detail.cursor
  posCur.value = pos
}

const deviceAll = ref({
  agent: {
    type: '1',
    service: '0'
  }
})

const changeDeviceType = (type) => {
  deviceAll.value.type = type
}

const formState = ref({
  alias: "",
  sn: "",
  price: "",
  total: 0,
});

const gfPrice = ref({
  coef1: '',
  coef2: '',
  coef3: '',
  coef4: '',
})

useLoad((options) => {
  globalStore.getUserInfo();
  formState.value.house_id = options.house_id;
  if(options.type) {
    deviceAll.value.type = options.type
  }
});

  // 综合电价
  const totalPrice = computed(() => {
  //  return  deviceAll.value.agent?.type == 2 ? (+formState.value.price + Number(ServicePrice.value) + Number(deviceAll.value.agent?.service)).toFixed(2)  : (+formState.value.price + +ServicePrice.value).toFixed(2)
  // 统一 服务费+基础单价
   return  (+formState.value.price + +ServicePrice.value).toFixed(2)
  })

const handleSubmit = debounce(function () {
  if ((!formState.value.sn || !formState.value.price) && deviceAll.value.type != 5 && deviceAll.value.type != 6) {
    Taro.showToast({
      title: "请完善表单内容",
      icon: "none",
    });
    return;
  }
  if (isNaN(totalPrice.value) && deviceAll.value.type != 5 && deviceAll.value.type != 6) {
    Taro.showToast({
      title: '请输入正确的价格!',
      icon: 'none'
    })
    return
  }
  // 更新峰谷表电价
  if (deviceAll.value.type == 5 || deviceAll.value.type == 6) {
    request.post({
      url: 'business/updateCoef',
      data: {
        coef1: gfPrice.value.coef1,
        coef2: gfPrice.value.coef2,
        coef3: gfPrice.value.coef3,
        coef4: gfPrice.value.coef4,
        sn: deviceAll.value.sn
      }
    })
  }
  request
    .post({
      url: `device/create`,
      data: {
        ...formState.value,
        basic_price: formState.value.price,
        service_price: ServicePrice.value,
        price: totalPrice.value
      },
    })
    .then((res) => {
      Taro.showModal({
        title: "提示",
        cancelText: "返回房源",
        confirmText: "继续新增",
        content: "设备保存成功",
        success: function (res) {
          if (res.confirm) {
            formState.value.name = "";
            formState.value.sn = "";
          } else if (res.cancel) {
            Taro.navigateBack();
          }
        },
      });
    });
});

const onScanCode = () => {
  Taro.scanCode({
    success: (res) => {
      console.log(res);
      if (!res.result) {
        Taro.showToast({
          title: '扫码失败',
          icon: 'error'
        })
        return
      }
      let sn = res.result.trim()
      sn.replace(/[\r\n]/g,"")
      if (sn.substring(0, 5) === 'https') {
        let _arr = res.result.split('sn=')
        console.log(_arr)
        if (_arr.length > 0) {
          sn = _arr[1]
        }
      }
      formState.value.sn = sn;
      getDeviceDetail(sn)
    },
  });
};

const getDeviceDetail = (sn) => {
  request.get({
      url: 'device/search',
      data: {
        sn: sn
      }
  }).then((res) => {
    if(res.code != 200) return
    deviceAll.value = res.data
    gfPrice.value.coef1 = res.data.coef1
    gfPrice.value.coef2 = res.data.coef2
    gfPrice.value.coef3 = res.data.coef3
    gfPrice.value.coef4 = res.data.coef4

  })
}

const changeSnHandel = (e) => {
  bindReplaceInput(e)
  const v = e.detail.value.trim()
  if (v.length == 9) {
    getDeviceDetail(v)
  } else {
    deviceAll.value = {
      agent: {
        type: '1',
        service: '0'
      }
    }
  }
}
</script>
