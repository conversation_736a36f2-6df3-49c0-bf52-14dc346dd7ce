<template>
  <View class="container">
    <!-- <MySearch placeholder="关健词搜索" @search="onSearch"></MySearch> -->
    <MySearchHome placeholder="房间名称搜索" @search="onSearch"></MySearchHome>
    <scroll-view
      class="scrollview"
      scroll-x="true"
      :show-scrollbar="false"
      enhanced
      :scroll-left="scrollLeft"
      style="height: 70rpx; padding: 0 20rpx; box-sizing: border-box"
    >
      <view class="status-filter">
        <text
          @tap="onChangeStatus(item.value)"
          v-for="(item, index) in statusLs"
          :key="index"
          class="tag"
          :class="params.status == item.value ? 'tag-selected' : 'tag-default'"
          >{{ item.name }}</text
        >
      </view>
    </scroll-view>

    <DeviceRender
      :items="items"
      @detail="onDetail"
      @editHoseName="editHoseNameHandel"
    ></DeviceRender>
    <view class="emty" v-if="items.length <= 0"> 您还没有电表，请马上添加吧~ </view>

    <MyPopup :show="isEditShow" title="房源名修改" @close="isEditShow = false">
      <template #content>
        <view
          style="color: #000000; padding: 35rpx"
          :style="{ paddingBottom: isAp ? '160rpx' : '100rpx' }"
        >
          <view class="font-24"> 房源名称 </view>
          <view class="p20"
            ><MyInput2>
              <template #content
                ><input
                  class="input-custom"
                  cursor-spacing="120"
                  v-model="formState.name"
                  placeholder="请输入房源名称"
              /></template> </MyInput2
          ></view>

          <view class="text-center" style="padding: 30rpx">
            <button
              class="btn-primary btn-inline"
              style="width: 220rpx"
              @tap="onSubmitHoseName"
            >
              确定
            </button>
          </view>
        </view>
      </template>
    </MyPopup>

    <view class="footer-fixed">
      <button class="btn-add m33" @tap="onBindDevice">添加设备</button>
    </view>
  </View>
</template>

<script setup>
import { ref } from "vue";
import "./device.scss";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
import request from "@/utils/request";
import MyPopup from "@/components/MyPopup";

import MyInput2 from "@/components/MyInput2";

import MySearch from "@/components/MySearch";
import MySearchHome from "@/components/MySearchHome";

import MyIcon from "@/components/MyIcon";

import DeviceRender from "@/components/DeviceRender/indexScouce";

import { useGlobalStore } from "@/stores";
const globalStore = useGlobalStore();

const statusLs = ref([
  {
    name: "全部",
    value: "0",
  },
  {
    name: "30天未充值",
    value: "7",
  },
  {
    name: "断电中",
    value: "2",
  },
  {
    name: "通电中",
    value: "1",
  },
  {
    name: "已离线",
    value: "3",
  },
  {
    name: "用电异常",
    value: "6",
  },
  {
    name: "电量不足",
    value: "5",
  },
  {
    name: "已过载",
    value: "4",
  },
]);

usePullDownRefresh(() => {
  console.log("onPullDownRefresh");
});

const onChangeFilter = (child, key) => {
  console.log(child, key);
  params.value[key] = child.value;
  fetch();
};
const onSearch = (keyword) => {
  params.value.keyword = keyword;
  fetch();
};

const onDetail = (id) => {
  Taro.navigateTo({
    url: "/pages/device/detail/detail?id=" + id,
  });
};

const isEditShow = ref(false);

const formState = ref({
  estate_name: "",
  name: "",
  province: "",
  city: "",
  district: "",
  fang: 1,
  ting: 1,
  wei: 1,
  chu: 1,
  size: 0,
  rent: 0,
});

const editHoseNameHandel = (item) => {
  console.log(item);
  isEditShow.value = !isEditShow.value;
  // 获取
  request
    .get({
      url: "house/" + item.house.id,
    })
    .then((res) => {
      formState.value = {
        id: res.data.house.id,
        estate_name: res.data.house.estate_name,
        name: res.data.house.name,
        province: res.data.house.province,
        city: res.data.house.city,
        district: res.data.house.district,
        fang: res.data.house.fang,
        ting: res.data.house.ting,
        wei: res.data.house.wei,
        chu: res.data.house.chu,
        size: res.data.house.size,
        rent: res.data.house.rent,
      };
    });
};

const onSubmitHoseName = () => {
  if (!formState.value.name)
    return Taro.showToast({
      title: "请输入房源名称！",
      icon: "none",
    });
  // 更新
  request
    .post({
      url: "house/update/" + formState.value.id,
      data: {
        house: formState.value,
      },
    })
    .then(async (data) => {
      isEditShow.value = false;
      Taro.showToast({
        title: "修改成功！",
        icon: "success",
        success: () => {
          fetch();
        },
      });
    });
};

/** ----------------------接口数据-begin----------------------------------- */
const items = ref([]);
const total = ref(0);

const params = ref({
  page: 1,
  status: 0,
  keyword: "",
  sort: "created_at",
  order: "desc",
});

const onChangeStatus = (val) => {
  params.value.status = val;
  params.value.recharge_time = 0
  if (val == 7) {
    // params.value.status = "0";
    params.value.recharge_time = 1
  }
  fetch();
};

const fetch = () => {
  items.value = [];
  params.value.page = 1;
  getList();
};

const getList = () => {
  request
    .get({
      url: "device",
      data: {
        ...params.value,
        is_warning:params.value.status == 6 ? 1 : ""
      },
    })
    .then((res) => {
      if (items.value.length > 0) {
        items.value = [...items.value, ...res.data.items];
      } else {
        items.value = res.data.items;
      }
      total.value = res.data.total;
      if (res.data.currentPage >= res.data.lastPage) {
        isLastPage.value = true;
      }
    });
};
/** ----------------------接口数据-end----------------------------------- */

useLoad((q) => {
  const type = q?.type || "0";
  params.value.status = type;
  params.value.recharge_time = 0
  if (type == 7) {
    // params.value.status = "0";
    params.value.recharge_time = 1
  }
  fetch();
});

useDidShow(() => {
});

usePullDownRefresh(() => {
  Taro.stopPullDownRefresh();
  fetch();
});

const isLastPage = ref(false);

useReachBottom(() => {
  if (!isLastPage.value) {
    params.value.page += 1;
    getList();
  }
});

const onBindDevice = () => {
  console.log("test");
  Taro.scanCode({
    scanType: "qrcode",
    success: (res) => {
      if (!res.result) {
        Taro.showToast({
          title: "扫码失败",
          icon: "error",
        });
        return;
      }
      let sn = res.result.trim();
      sn.replace(/[\r\n]/g, "");
      if (sn.substring(0, 5) === "https") {
        let _arr = res.result.split("sn=");
        console.log(_arr);
        if (_arr.length > 0) {
          sn = _arr[1];
        }
      }
      Taro.navigateTo({
        url: "/pages/bindDevice/bindDevice?sn=" + sn,
      });
    },
  });
};
</script>
