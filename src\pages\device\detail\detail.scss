page,body {
  background-color: #468FFE;
}

@import "taro-ui-vue3/dist/style/components/checkbox.scss";
@import "taro-ui-vue3/dist/style/components/switch.scss";

.set-c-f {
  margin-bottom: 15px;
  font-size: 35px;
}




.confirm {
  position: relative;
  .share-btn {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  }
}


.content-modal {
  padding: 20px;
  >view{
    margin-bottom: 20px;
    text-align: center;
    // font-weight: 700;
    font-size: 35px;
  }
}

.content-modal2 {
  >view {
    font-weight: 700;
    font-size: 32px;
    margin-bottom: 20px;
  }
  .content-ipt {
    display: flex;
    align-items: center;
    justify-content: center;
    input {
      width: 260px;
    }
  }
  .btn-box {
    position: absolute;
    left: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    border-top: 1px solid #dcdcdc;
    width: 100%;
    line-height: 100px;
    height: 85px;
    font-weight: 500;

    view {
      color: #0173FF;
      text-align: center;
      width: 50%;

      &:active {
        opacity: .85;
      }
    }

    .btn1 {
      border-right: 1px solid #dcdcdc;
      color: #000;
    }
  }
}

.container {
  padding: 54px;
  color: #FFFFFF;
  background-color: #468FFE;
  .input-custom {
    padding: 15px;
    border-radius: 10px;
    font-size: 27px;
  }
  .input-box {
    padding: 10px;
    padding-top: 20px;
    .my-input2-suffix {
      transform: translateX(-150px);
    }
  }
  .input-custom-c {
    border: 1px solid #ddd;
    padding: 30px;
    border-radius: 10px;
    font-size: 27px;
  }
  .gongtaninput {
    padding: 30px 10px;
    }
  .custom-top {
    font-size: 35px;
    >view {
      margin-bottom: 20px;
      margin-right: 30px;
    }
  }
  .phone{
    font-size: 35px;
  }
  .bot-btn-box {
    .device-price {
      position: relative;
    }
    .icon-bianji {
      position: absolute;
      right: 35px;
      top: 0px;
      color: #fff;
      font-size: 40px;
      width: 60px;
      height: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .icon-wenhao {
    font-size: 35px;
    position: absolute;
    right: 10px;
    top: 10px;
    font-weight: 700;
  }

  .device-top {
    margin-bottom: 25px;
    align-items: center;
    .params_box {
      width: 101px;
      height: 49px;
      background-color: #fff;
      border-radius: 15px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 25px;
      color: #1352fd;
      .iconfont {
        margin-right: 10px;
        font-size: 25px;
      }
      font-size: 22px;
    }
    .device-name {
      font-size: 28rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #fff;
    }
    .device-sn {
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #ffff;
    }
    .device-icon {
      width: 60rpx;
      height: 60rpx;
      background: #E9EFFA;
      border-radius: 10rpx;
      margin-right: 12px;
      text-align: center;
      padding: 2px 0;
    }
    .device-icon-new {
      padding-top: 7px;
    }
  }
}

.device-du {
  width: 555px;
  height: 555px;
  margin-left: 45px;
  margin-top: 55px;
  background: url("https://yimits.oss-cn-beijing.aliyuncs.com/images/bg-device.png") no-repeat center;
  background-size: contain;
  text-align: center;
  color: #000000;

  .device-du1 {
    font-size: 24rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
    padding-top: 160px
  }
  .device-du2 {
    font-size: 106px;
    font-family: Bahnschrift;
    font-weight: 400;
    color: #000000;
  }
  .device-du3 {
    font-size: 26rpx;
    font-family: OPPOSans;
    color: #1352FD;
    padding-top: 20px;
  }
  .device-du4 {
    font-size: 24rpx;
    font-family: OPPOSans;
    color: #B6BEC5;
    padding-top: 10px;
  }
}

.device-total {
  width: 49%;
}
.device-price {
  width: 49%;
}
.device-line1 {
  width: 2px;
  height: 54px;
  background: #6AA5FF;
}
.val {
  font-size: 60rpx;
  font-family: Bahnschrift;
  font-weight: 400;
  color: #FFFFFF;
}
.lab {
  font-size: 24rpx;
  font-family: OPPOSans;
  font-weight: 500;
  color: #A3C8FF;
}
.main-btn {
  margin-top: 50px;
  background: #5A9BFF;
  border-radius: 11rpx;
  padding: 34px 38px;
  width: 100%;
  box-sizing: border-box;
  justify-content: space-around;
}
.main-btn-col {
  font-size: 22px;
}

.other-btn {
  margin-top: 22px;
}
.other-btn-col {
  position: relative;
  width: 310rpx;
  padding: 24rpx 0;
  background: #679FFE;
  border-radius: 11rpx;
  text-align: center;
  margin-bottom: 22rpx;
  .btn-share-c {
    position: absolute;
    width: 100%;
    left: 0;
    height: 100%;
    top: 0;
    opacity: 0;
  }
}
.btn-share {
  position: relative;
}
.text-btn{
  left: 0;
  right: 0;
  border: none;
  outline: none;
  color: #FFFFFF;
  position: absolute;
  width: 100%;
  height: 94rpx;
}
.text-btn[plain] {
  border: 0;
  padding: 0;
  line-height: 160%;
  color: #FFFFFF;
  font-size: 22px;
}
.text-btn::after {
  outline: none;
  border: none;
}

.params-container,.gf-container {
  color: #000;
  padding: 0 54px;
  height: 600px;
  position: relative;
  padding-top: 40px;
  .li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding:  15px 0;
  }
  .btn {
    position: absolute;
    left: 50%;
    bottom: 50px;
    transform: translateX(-50%);
    width: 688px;
    height: 97px;
    background-color: #1f62dc;
    border-radius: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
  }
}

.gf-container {
  // height: 80vh;
  height: auto;
  max-height: 80vh;
  // min-height: 67vh;
  // max-height: 80vh;
  padding: 0 21px;
  padding-bottom: 200px;
  .btn2 {
    width: 100%;
    height: 97px;
    background-color: #1352fd;
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    margin-top: 20px;
  }
  .title {
    position: relative;
    text-align: center;
    font-size: 30px;
    padding: 50px 30px;
    .t {
      font-weight: 700;
    }
    .r {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 24px;
      color: #0173FF;
      margin-left: 50px;
      image {
        width: 26px;
        height: 26px;
        vertical-align: middle;
        margin-right: 10px;
      }
      text {
        vertical-align: middle;
      }
    }
  }
  .container {
    padding: 0px;
    color: #000;
    background-color: #fff;
    font-size: 24px;
    // height: 84%;
    height: auto;
    max-height: 71vh;
    overflow-y: auto;
  }
  
  .table-wrapper {
    margin: 0 auto;
    max-width: 960px;
    background-color: #fff;
    overflow-x: auto;
  }
  
  .table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .thead .tr {
    display: flex;
    width: 100%;
  }
  
  .tbody .tr {
    display: flex;
    width: 100%;
  }
  
  .th,
  .td {
    border: 1px solid #000;
    text-align: left;
    flex: none; /* 让每个单元格平分宽度 */
    word-break: break-all; /* 防止内容溢出 */
    box-sizing: border-box; /* 确保padding包含在元素宽度内 */
    text-align: center;
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .td-son {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #000;
      height: 100%;
      >view {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        border-right: 1px solid #000;
        height: 100%;
        padding: 10rpx;
        box-sizing: border-box;
        width: 83px;
        &:last-child {
          border:none;
          width: 382px;
        }
      }
      &:last-child {
        border:none;
      }
    }
  }
  
  .th {
    flex: none;
    background-color: #f9f9f9;
    font-weight: 500;
    padding: 12px 16px;
    height: 71px;
    border-bottom: none;
  }
  .btn {
    border-radius: 15px !important;
    background-color: #1352fd !important;
  }
}

.gf-config-container {
  .btn2 {
    width: 100%;
    height: 97px;
    background-color: #1352fd;
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    margin-top: 20px;
  }
  height: 80vh;
  .container {
    height: 84%;
    overflow-y: auto;
    .label {
      font-size: 30px;
      color: #B6BEC5;
      width: 200px;
    }
    .top {
      .item ,.item3{
        font-size: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 35px;
        .right {
          display: flex;
          align-items: center;
          color: #000;
          text {
            vertical-align: middle;
          }
          .iconfont {
            color: #B6BEC5;
            font-size: 30px;
            vertical-align: middle;
            margin-top: 10px;
          }
        }
      }
      .item {
        .list {
          display: flex;
          width: 100%;
          flex-wrap: wrap;
          padding-right: 10px;
          .month {
            width: 126px;
            height: 63px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            background: #F3F6F8;
            color: #000;
            border-radius: 2px;
            margin-right: 28px;
            margin-bottom: 13px;
            border: 1px solid transparent;
            &:nth-child(2n) {
              margin-right: 0;
            }
            &.disabled {
              opacity: .4;
            }
            &.active {
              position: relative;
              border: 1px solid #1352fd;
              &::after {
                content: "\eaf1";
                font-size: 20px;
                position: absolute;
                right: -2px;
                bottom: -4px;
                color: #fff;
              }
              &::before {
                content: '';
                // 画一个直角三角形,直角在右下角，底边占满
                position: absolute;
                right: -1px;
                bottom: -1px;
                width: 0;
                height: 0;
                border-style: solid;
                border-width: 0 0 30px 30px;
                border-color: transparent transparent #1352fd transparent;
              }
            }
          }
        }
      }
      .item3 {
        align-items: start;
        .label {
          width: 140px;
          flex-shrink: 0;
        }
        .list {
          display: flex;
          width: 100%;
          flex-wrap: wrap;
          .month {
            width: 86px;
            height: 63px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            background: #F3F6F8;
            color: #000;
            border-radius: 2px;
            margin-right: 28px;
            margin-bottom: 13px;
            border: 1px solid transparent;
            &:nth-child(5n) {
              margin-right: 0;
            }
            &.disabled {
              opacity: .4;
            }
            &.active {
              position: relative;
              border: 1px solid #1352fd;
              &::after {
                content: "\eaf1";
                font-size: 20px;
                position: absolute;
                right: -2px;
                bottom: -4px;
                color: #fff;
              }
              &::before {
                content: '';
                // 画一个直角三角形,直角在右下角，底边占满
                position: absolute;
                right: -1px;
                bottom: -1px;
                width: 0;
                height: 0;
                border-style: solid;
                border-width: 0 0 30px 30px;
                border-color: transparent transparent #1352fd transparent;
              }
            }
          }
        }
      }
    }

    .middle {
      .utils {
        width: 100%;
        height: 53px;
        background: #F3F6F8;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        .li {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #B6B6B6;
          font-size: 30px;
          .iconfont {
            font-size: 23px;
            color: #B6B6B6;
            margin: 0 10px;
            margin-top: 6px;
          }
          &.active {
            color: #000;
          }
        }
      }
    }

    ._bottom {
      .label {
        width: 400px;
        margin: 28px 0;
      }
      .list {
        display: flex;
        width: 100%;
        flex-wrap: wrap;
        .time {
          width: 152px;
          height: 63px;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          justify-content: center;
          background: #F3F6F8;
          color: #000;
          border-radius: 2px;
          margin-right: 28px;
          margin-bottom: 13px;
          border: 1px solid transparent;
          font-size: 20px;
          &:nth-child(4n) {
            margin-right: 0;
          }
          &.disabled {
            opacity: .4;
          }
          &.active {
            position: relative;
            border: 1px solid #1352fd;
            &::after {
              content: "\eaf1";
              font-size: 20px;
              position: absolute;
              right: -2px;
              bottom: -4px;
              color: #fff;
            }
            &::before {
              content: '';
              // 画一个直角三角形,直角在右下角，底边占满
              position: absolute;
              right: -1px;
              bottom: -1px;
              width: 0;
              height: 0;
              border-style: solid;
              border-width: 0 0 30px 30px;
              border-color: transparent transparent #1352fd transparent;
            }
          }
        }
      }
    }
  }
}