.tip {
  width: 100%;
  height: 67px;
  text-align: center;
  line-height: 67px;
  color: #FFFFFF;
  background: #FF9A0A;
}
.content-modal2 {
  .btn-box {
    position: absolute;
    left: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    border-top: 1px solid #dcdcdc;
    width: 100%;
    line-height: 100px;
    height: 85px;
    font-weight: 500;

    view {
      color: #0173FF;
      text-align: center;
      width: 50%;

      &:active {
        opacity: .85;
      }
    }

    .btn1 {
      border-right: 1px solid #dcdcdc;
      color: #000;
    }
  }
}
.recharge-confirm-box {
  padding: 20px;
  padding-top: 0;
  .recharge-tip {
    padding: 40px 20px;
    .text-box {
      >view {
        margin-top: 22px;
        font-size: 30px;
        text {
          color: #cf0f00;
        }
        &:nth-of-type(1) {
          margin-top: 0;
        }
      }
    }
  }
}
.content-modal {
  padding: 20px;
  >view{
    margin-bottom: 20px;
    text-align: center;
    // font-weight: 700;
    font-size: 35px;
  }
}

.content-modal2 {
  >view {
    font-weight: 700;
    font-size: 32px;
    margin-bottom: 20px;
  }
}
.du {
  margin: 37px 25px 0 25px;
  background: url('https://yimits.oss-cn-beijing.aliyuncs.com/images/bg-dl.png') no-repeat top right;
  background-size: contain;
  background-color: #1352FD;
  border-radius: 20rpx 20rpx 0rpx 0rpx;
  padding: 42px 0 50px 39px;
  color: #FFFFFF;

  .du-lab {
    font-size: 20rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #8EACFF;
  }
  .du-num {
    font-size: 60rpx;
    font-family: Bahnschrift;
    font-weight: 400;
  }
}

.form {
  padding: 67px 29px 50px 29px;
  background: #FFFFFF;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(54,69,193,0.24);

  .form-title {
    font-size: 36rpx;
    font-family: OPPOSans;
    font-weight: bold;
    color: #000000;
  }
  .log-link {
    font-size: 30rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #B6BEC5;
  }

  .form-amount {
    padding: 27px 0 10px 0;
    color: #FFFFFF;

    .fa-item {
      width: 210px;
      height: 100px;
      text-align: center;
      line-height: 100px;
      background: #B0C5FF;
      border-radius: 10rpx;
      margin-top: 17px;

      font-size: 60rpx;
      font-family: Bahnschrift;
      font-weight: 400;
      margin-right: 30px;
      &:nth-child(3n) {
        margin-right: 0;
      }

      .rmb {
        font-size: 30rpx;
        font-family: OPPOSans;
        font-weight: 500;
      }
    }
    .fa-checked {
      background: #1352FD;
    }
  }
}

.d-bar {
  background: #F1F3F9;
  border-radius: 20rpx;

  padding: 18px 18px 45px 18px;

  .d-cell {
    margin-top: 27px;
    display: flex;
    justify-content: space-between;

    .d-lab {
      font-size: 30rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #9BA3BA;
    }
    .d-val {
      font-size: 30rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #1352FD;

      .d-val-txt {
        font-size: 24rpx;
        font-family: OPPOSans;
        font-weight: 500;
        color: #000000;
      }
    }
  }
}
.icon-radio {
  width: 26px;
  height: 26px;
  vertical-align: middle;
  margin-right: 10px;
}
.mt33 {
  margin-top: 33px;
}
.recharge-tip {
  padding: 20px;
  background: #F1F3F9;
  border-radius: 25px;
  color: #9BA3BA;
  font-size: 30px;
}
.recharge-detail {
  padding: 40px 0;
  .rd-left {
    color: #B6BEC5;
    width: 150px;
    font-size: 30px;
    font-weight: 500;
  }
  .rd-right {
    font-size: 30px;
    font-weight: 500;
    color: #000000;
    text-align: right;
    .red {
      color: #cf0f00;
    }
    .ico {
      font-size: 24px;
      margin-right: 10px;
      color: #9BA3BA;
      .iconfont {
        font-size: 25px;
      color: #9BA3BA;
      margin-left: 5px;
      }
    }
  }
  .rd-fee {
    background: #F1F3F9;
    border-radius: 10px;
    padding: 20px;
    font-size: 24px;
    font-weight: 500;
    color: #000000;
  }
}
.rd-btn-box {
  border-top: 1px solid #DFDFDF;
  padding: 33px 40px 0 40px;
}

.recharge-confirm-box-business {
  text-align: center;
  padding: 90px 0;
  font-weight: 700;
  font-size: 50px;
  .text {
    color: red;
    font-size: 60px;
    vertical-align: middle;
    margin: 0 10px;
    display: inline-block;
    transform: translateY(-5px);
  }
}

.recharge-confirm-xy {
  color: #9BA3BA;
  font-size:30px;
  text-align: center;
  padding: 70px 0;
  .link {
   font-size:30px;
   color:#000;
  }
}