<!--
 * @Autor: lisong
 * @Date: 2023-08-13 10:52:06
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-13 12:46:40
-->
<template>
  <view class="box">
    <view class="list">
      <view class="list-label">房源信息</view>
      <view class="list-content"
        >{{ contract.house?.estate_name }}{{ contract.house?.name }}</view
      >
    </view>
    <view class="list">
      <view class="list-label">租客姓名</view>
      <view class="list-content">{{ contract.master_tenant?.name }}</view>
    </view>
    <view class="list">
      <view class="list-label">手机号码</view>
      <view class="list-content">{{ contract.master_tenant?.mobile }}</view>
    </view>
    <view class="list">
      <view class="list-label">房屋租金</view>
      <view class="list-content">{{ contract.contract_rent?.amount }}</view>
    </view>
    <view class="list">
      <view class="list-label">房屋周期</view>
      <view class="list-content"
        >{{ contract.start_at }} ~ {{ contract.expired_at }}</view
      >
    </view>
    <view class="list">
      <view class="list-label">退房类型</view>
      <view class="list-content">{{ breakTypeName }}</view>
    </view>
    <view class="list">
      <view class="list-label">退房时间</view>
      <view class="list-content">{{ form.break_at }}</view>
    </view>
    <view class="list-title">退费</view>
    <view class="list">
      <view class="list-label">房租押金</view>
      <view class="list-content">{{ contract.contract_rent?.deposit }}元</view>
    </view>
  </view>
  <textarea
    class="textarea"
    v-model="form.note"
    placeholder="请输入备注"
  ></textarea>
  <myBottomBtn btnTxt="线下已付" @click="onSubmit" />
</template>

<script setup>
//  constant
import { ref } from "vue";
import request from "@/utils/request";
import myBottomBtn from "@/components/MyBottomBtn/MyBottomBtn";
import { formatDate } from "@/utils";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
definePageConfig({
  navigationBarTitleText: "退房详情",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

let contract_id = 0;
const contract = ref({});
const breakTypeName = ref("到期退房");
const form = ref({
  break_at: formatDate(new Date()),
});
useLoad((options) => {
  contract_id = options.contract_id;
  getContractDetail();
});

const getContractDetail = () => {
  request
    .get({
      url: "contract/" + contract_id,
    })
    .then((res) => {
      contract.value = res.data;
    });
};

const onSubmit = () => {
  Taro.showModal({
    title: "确定要退房吗？",
    content: "未支付或逾期账单将强行失效",
    confirmText: "确定",
    cancelText: "取消",
    success: (res) => {
      if (res.confirm) {
        request
          .post({
            url: "contract/" + contract.value.id + "/break",
            data: form.value,
          })
          .then((res) => {
            Taro.navigateBack({
              delta: 2,
            });
          });
      }
    },
  });
};
</script>

<style lang="scss">
.box {
  padding: 20rpx 32rpx;
}

.list {
  padding: 20rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 30rpx;
  font-weight: 500;
  color: #b6bec5;
  .list-label {
    flex-shrink: 0;
  }
  .list-content {
    padding-left: 10rpx;
    color: #000;
  }
  .list-title {
    font-size: 30rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
    padding: 20rpx 0;
  }
}

.textarea {
  height: 380rpx;
  background: #ffffff;
  padding: 35rpx;
}
</style>
