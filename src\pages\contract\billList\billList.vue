<!--
 * @Autor: lisong
 * @Date: 2023-08-12 20:38:00
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-12 20:56:09
-->
<template>
  <view class="list" v-for="item in dataList" :key="item.pay_date">
    <view class="list-title">
      <view>{{ item.title }}</view>
      <view class="list-amount">{{ item.amount }}</view>
    </view>
    <view class="list-sub"
      >账单区间 {{ item.start_date }} ~ {{ item.end_date }}</view
    >
    <view class="list-sub">应付时间 {{ item.pay_date }}</view>
  </view>
  <myBottomBtn v-if="type !== 'xuzu'" btnTxt="保存" @click="handleSubmit" />
  <myBottomBtn v-else btnTxt="返回" @click="onBack" />
</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import myBottomBtn from "@/components/MyBottomBtn/MyBottomBtn";
import { getApiRoot } from "@/config";
import { addMonth } from "@/utils";
import { useGlobalStore } from "@/stores";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
const globalStore = useGlobalStore();
definePageConfig({
  navigationBarTitleText: "账单明细",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

const dataList = ref([]);
const type = ref('')

const isAp = process.env.TARO_ENV === "alipay";


useLoad((options) => {
  type.value = options.type || ''
  if (type.value !== 'xuzu') {
    fetch();
  } else {
    fetchXuzu()
  }
})

const fetch = () => {
  request
    .get({
      url: "contract/bill/preview",
      data: {
        ...globalStore.yjData,
        start_at:isAp ? globalStore.yjData?.start_at.replaceAll('/','-') : globalStore.yjData?.start_at
      },
    })
    .then((res) => {
      dataList.value = res.data;
    });
}

const fetchXuzu = () => {
  request
    .get({
      url: "contract/" + globalStore.yjData.contract_id + "/changeExpirePreviewBill",
      data: {
        ...globalStore.yjData,
      },
    })
    .then((res) => {
      dataList.value = res.data;
    });
}

const handleSubmit = () => {
  request
    .post({
      url: "contract/updateRent",
      data: {
        ...globalStore.yjData,
      },
    })
    .then((res) => {
      Taro.navigateBack({
        delta: 2,
      });
    });
};

const onBack = () => {
  Taro.navigateBack();
}

</script>

<style lang="scss">
page {
  background: #f7f9ff;
}

.list {
  margin: 20rpx 24rpx;
  height: 200rpx;
  background: #ffffff;
  border-radius: 14rpx;
  padding: 0 30rpx;
  .list-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 36rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #000000;
    height: 104rpx;
    .list-amount {
      font-size: 48rpx;
      font-family: Bahnschrift;
      font-weight: normal;
      color: #000000;
      &::before {
        content: "￥";
        font-size: 30rpx;
      }
    }
  }
  .list-sub {
    font-size: 26rpx;
    font-family: OPPOSans;
    font-weight: 500;
    color: #a8b1ca;
  }
}
</style>
