page, body {
  background-color: #f1f2f3;
}
.mini-input {
  width: 60rpx;
  text-align: center;
}
.mini-txt {
  width: 60rpx;
  text-align: right;
}
.mini-txt2 {
  width: 60px;
  text-align: right;
}
.house-form {
  background-color: #FFFFFF;
  border-radius: 40px 40px 0 0;
  margin-top: 136px;
  position: relative;
  padding-top: 202px;
}
.device {
  position: absolute;
  width: 100%;
  top: -79px;
  left: 0;

  .device-img {
    margin-left: 60px;
    image {
      width: 178px;
      height: 267px;
    }
  }
  .device-info2 {
    padding: 117px 0 0 26px;

    .device-sn {
      font-size: 24rpx;
      font-weight: 500;
      color: #B6BEC5;
    }
  }

}