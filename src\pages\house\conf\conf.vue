<template>
  <view>

    <view class="my-tab">
      <view class="my-tab-item" :class="index === 0 ? 'my-tab-active' : ''" @tap="onChangeTab(0)">
        <view>出租设置</view>
        <view class="my-tab-btm"></view>
      </view>
      <view class="my-tab-item" :class="index === 1 ? 'my-tab-active' : ''" @tap="onChangeTab(1)">
        <view>设备设置</view>
        <view class="my-tab-btm"></view>
      </view>
      <view class="my-tab-item" :class="index === 2 ? 'my-tab-active' : ''" @tap="onChangeTab(2)">
        <view>消息推送</view>
        <view class="my-tab-btm"></view>
      </view>
    </view>

    <view class="container" v-if="index === 0">
      <view class="conf-title">账单逾期设置</view>
      <view class="form-item">
        <MyCell prefix="断电设置(可多选)" prefixWidth="240" align="right">
          <template #content>
            <view>
              <view class="my-radio" :class="conf.is_fail_close ? 'my-radio-checked' : ''" @tap="onChangeRadio('is_fail_close')">账单逾期断电</view>
              <view class="my-radio" :class="conf.is_auto_close ? 'my-radio-checked' : ''" @tap="onChangeRadio('is_auto_close')">电表透支断电</view>
            </view>
          </template>
        </MyCell>
        <MyCell prefix="账单逾期限制充值" prefixWidth="250" align="right">
          <template #content>
            <checkbox value="1" :checked="!!conf.is_limit_recharge"></checkbox>
          </template>
        </MyCell>
        <MyCell prefix="待缴账单逾期，电表自动断电 /限制充值天数" prefixWidth="380" align="right" suffix="天">
          <template #content>
            <input v-model="conf.is_limit_recharge_days" placeholder="1" />
          </template>
        </MyCell>
      </view>
      <view class="conf-title" v-if="globalStore.userInfo.agent.type != 2">支付相关手续</view>
      <view class="form-item" v-if="globalStore.userInfo.agent.type != 2">
        <MyCell prefix="手续费承担方" prefixWidth="240" align="right">
          <template #content>
            <view>
              <view class="my-radio" :class="conf.fee_payer === 1 ? 'my-radio-checked' : ''" @tap="onChange('fee_payer', 1)">房东</view>
              <view class="my-radio" :class="conf.fee_payer === 2 ? 'my-radio-checked' : ''" @tap="onChange('fee_payer', 2)">租客</view>
            </view>
          </template>
        </MyCell>
      </view>
    </view>

    <view class="container" v-if="index === 1">
      <view class="conf-title">租客电表显示方式</view>
      <view class="form-item">
        <MyCell prefix="租客电表" align="right">
          <template #content>
            <view>
              <MyIcon :icon="conf.elec_show_type == 2 ? 'icon-radio-checked' : 'icon-radio'" width="26rpx" height="26rpx"></MyIcon> <text class="text-v-center"  @tap="onChange('elec_show_type', 2)" style="margin-right: 20rpx;">金额</text>
              <MyIcon :icon="conf.elec_show_type != 2 ? 'icon-radio-checked' : 'icon-radio'" width="26rpx" height="26rpx"></MyIcon> <text class="text-v-center" @tap="onChange('elec_show_type', 1)">度数</text>
            </view>
          </template>
        </MyCell>
        <view class="conf-title">费用充值额度设置</view>
        <MyCell prefix="租客电费充值最小金额" prefixWidth="300" align="right" suffix="元" arrow>
          <template #content>
            <input v-model="conf.elec_recharge_min" placeholder="1" />
          </template>
        </MyCell>
        <MyCell prefix="租客水费充值最小金额" prefixWidth="300" align="right" suffix="元" arrow>
          <template #content>
            <input v-model="conf.water_recharge_min" placeholder="请输入金额" />
          </template>
        </MyCell>
      </view>
      <view class="conf-title">租客电费充值最小金额</view>
      <view class="form-item">
        <MyCell prefix="何时清零" prefixWidth="240" align="right">
          <template #content>
            <view>
              <view class="my-radio" :class="conf.elec_clear_in === 1 ? 'my-radio-checked' : ''" @tap="onChangeRadio('elec_clear_in')">入住时清零</view>
              <view class="my-radio" :class="conf.elec_clear_out === 1 ? 'my-radio-checked' : ''" @tap="onChange('elec_clear_out')">退房时清零</view>
            </view>
          </template>
        </MyCell>
        <MyCell prefix="退房时,电表自动充值度数" prefixWidth="300" align="right" suffix="度" arrow>
          <template #content>
            <input v-model="conf.elec_add_out" placeholder="1" />
          </template>
        </MyCell>
        <MyCell prefix="租客是否显示基础电价" prefixWidth="300" align="right">
          <template #content>
            <switch
              :checked="Boolean(conf?.show_basic_price)"
              @change="changeSwitch($event,1)"
              color="#1652F8"
            />
          </template>
        </MyCell>
        <MyCell prefix="租客是否显示服务费" prefixWidth="300" align="right">
          <template #content>
            <switch
              :checked="Boolean(conf?.show_service_price)"
              @change="changeSwitch($event,2)"
              color="#1652F8"
            />
          </template>
        </MyCell>
        <MyCell prefix="租客是否显示综合电价" prefixWidth="300" align="right">
          <template #content>
            <switch
              :checked="Boolean(conf?.show_price)"
              @change="changeSwitch($event,3)"
              color="#1652F8"
            />
          </template>
        </MyCell>
      </view>
    </view>

    <view class="container" v-if="index === 2">
      <view class="conf-title">当日设备使用异常</view>
      <view class="form-item">
        <MyCell prefix="已出租房源推送消息给租客及房东 " prefixWidth="380"></MyCell>
        <MyCell prefix="当日电表超出度数" prefixWidth="300" align="right" suffix="度" arrow>
          <template #content>
            <input v-model="conf.msg_hired_daily_elec" placeholder="1" />
          </template>
        </MyCell>
        <MyCell prefix="未出租房源推送消息给房东" prefixWidth="380"></MyCell>
        <MyCell prefix="当日电表超出度数" prefixWidth="300" align="right" suffix="度" arrow>
          <template #content>
            <input v-model="conf.msg_vacant_daily_elec" placeholder="1" />
          </template>
        </MyCell>
      </view>

      <view class="conf-title">合同到期消息</view>
      <view class="form-item">
        <MyCell prefix="到期提醒租客及房东" prefixWidth="380"></MyCell>
        <MyCell prefix="提醒方式(可多选)" prefixWidth="220" align="right">
            <template #content>
              <view>
                <view class="my-radio" :class="conf.msg_contract_expired === 1 ? 'my-radio-checked' : ''" @tap="onChangeRadio('msg_contract_expired')">短信通知</view>
              </view>
            </template>
          </MyCell>
          <MyCell prefix="提前推送消息给房东天数" prefixWidth="300" align="right" suffix="天" arrow>
            <template #content>
              <input v-model="conf.msg_contract_expire_days" placeholder="" />
            </template>
          </MyCell>
      </view>

    </view>

    <view class="footer-fixed">
      <view class="p20">
        <button class="btn-primary" @tap="onSubmit">确认保存</button>
      </view>
    </view>

  </view>
</template>

<script setup>
  import { ref } from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad } from '@tarojs/taro'
  import request from '@/utils/request'
  import './conf.scss'
  import { AtSwitch } from "taro-ui-vue3";
    

  import MyIcon from '@/components/MyIcon'
  import MyCell from '@/components/MyCell'

  import { useGlobalStore } from '@/stores'
  import {getApiRoot} from "@/config";
  const globalStore = useGlobalStore()

  const conf = ref({})

  const index = ref(0)

  const isShowBase = ref(false)
  const isShowServe = ref(false)

  const onChangeTab = (tab) => {
    index.value = tab
  }

  const changeSwitch = (e,type) => {
    console.log(conf.value);
    if (type == 1) {
      console.log(e,type);
      isShowBase.value = e.detail.value
      conf.value.show_basic_price = e.detail.value ? 1 : 0
      onSubmit()
    } else if (type == 2) {
      console.log(e, type);
      isShowServe.value = e.detail.value
      conf.value.show_service_price = e.detail.value ? 1 : 0
      onSubmit()
    } else if (type == 3) {
      console.log(e, type);
      conf.value.show_price = e.detail.value ? 1 : 0
      onSubmit()
    }
  }

  const onSubmit = () => {
    request.post({
      url: 'business/conf',
      data: {
        ...conf.value
      }
    }).then(res => {
      Taro.showToast({
        title: '保存成功！',
        icon: 'success'
      })

    })
  }

  const onChangeRadio = (field) => {
    conf.value[field] = conf.value[field] === 1 ? 0 : 1
  }

  const onChange = (field, val) => {
    conf.value[field] = val
  }

  useLoad(() => {
    fetch()
  })

  const fetch = () => {
    request.get({
      url: 'business/conf'
    }).then(res => {
      conf.value = res.data
    })
  }

</script>

<style lang="scss">

</style>
