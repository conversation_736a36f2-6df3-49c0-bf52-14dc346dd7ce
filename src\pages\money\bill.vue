<template>
  <view>
    <MySearch placeholder="关健词搜索" @search="onSearch"></MySearch>
    <MyFilter :options="options" @change="onChangeFilter"></MyFilter>
    <view class="container">
      <view class="my-bill-item" v-for="item in items" :key="item.id">
        <view class="flex flex-v-center">
          <view class="bill-con">
            <view class="house-name flex flex-space-between flex-v-center">
              <view v-if="item.house">{{ item.house.name}}</view>
              <view v-if="!item.house">房源已删除</view>
              <!-- <view><MyIcon icon="icon-arrow-right" width="15rpx" height="28rpx"></MyIcon></view> -->
            </view>
            <view class="flex flex-space-between bill-m flex-v-center">
              <view class="flex-l bill-title">{{item.title}}</view>
              <view class="flex-r bill-amount" style="text-align: right;"><text class="cny">￥</text>{{item.amount}}</view>
            </view>
            <view class="bill-m bill-info">
              <view v-if="item.contract">合同编号 {{item.contract.sn}}</view>
              <view class="mt10" v-if="item.pay_date">应付日期 {{item.pay_date}}</view>
              <view class="mt10" v-if="item.is_paid">付款日期 {{item.paid_at}}</view>
              <view class="mt10" v-if="item.is_paid">付款方式 {{item.bill_pay && item.bill_pay.payment_name}}</view>
              <view class="mt10" >房客姓名 {{item.user && item.user.nickname}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

  </view>
</template>

<script setup>
import {computed, ref} from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad, usePullDownRefresh, useReachBottom } from '@tarojs/taro'
  import request from '@/utils/request'

  import MyFilter from '@/components/MyFilter'
  import MySearch from '@/components/MySearch'

  import MyIcon from '@/components/MyIcon'
  import MyPopup from '@/components/MyPopup'



  import { useGlobalStore } from '@/stores'

  definePageConfig({
    navigationBarTitleText: "我的账单",
    navigationBarBackgroundColor: "#F7F9FF",
  });

  if (process.env.TARO_ENV === 'alipay') {
    my.setNavigationBar({
      frontColor: '#ffffff',
      backgroundColor: '#1452fd',
    })
  }

  const globalStore = useGlobalStore()

  const options = ref([
    {
      name: '付款状态',
      key: 'status',
      options: [
        {
          label: '已付款',
          value: 2
        }
      ]
    },
    {
      name: '账单类型',
      key: 'type',
      options: [
        {
          label: '房租',
          value: 1
        },
        {
          label: '押金',
          value: 2
        },
        {
          label: '电费',
          value: 3
        }
      ]
    }
  ])

  const onChangeFilter = (child, key) => {
    console.log(child, key)
    params.value[key] = child.value
    fetch()
  }
  const onSearch = (keyword) => {
    params.value.keyword = keyword
    fetch()
  }

  /** ----------------------接口数据-begin----------------------------------- */
  const items = ref([])
  const total = ref(0)
  const conf = ref({})

  const params = ref({
    page: 1,
    type: 0
  })

  const onChangeStatus = (val) => {
    params.value.status = val
    fetch()
  }

  const fetch = () => {
    items.value = []
    params.value.page = 1
    getList()
  }

  const getList = () => {
    request.get({
      url: 'business/bill',
      data: {
        ... params.value
      }
    }).then(res => {
      if (items.value.length > 0) {
        items.value = [...items.value, ...res.data.items]
      } else {
        items.value = res.data.items
      }
      total.value = res.data.total
      if (res.data.currentPage >= res.data.lastPage) {
        isLastPage.value = true
      }
    })
  }

  /** ----------------------接口数据-end----------------------------------- */

  useLoad((options) => {

  })

  useDidShow(() => {
    fetch()
  })

  usePullDownRefresh(() => {
    Taro.stopPullDownRefresh()
    fetch()
  })

  const isLastPage = ref(false)

  useReachBottom(() => {
    if (!isLastPage.value) {
      params.value.page += 1
      getList()
    }
  })


</script>
<style lang="scss">
page, body {
  background-color: #f1f2f3;
}
.container {
  padding: 27px 27px 200px 27px;
}
.my-bill-item {
  margin-bottom: 26px;

  .bill-check {
    width: 68px;
  }
  .bill-con {
    background: #FFFFFF;
    text-align: left;
    border-radius: 14px;
    flex: 1;

    .house-name {
      font-size: 26px;
      font-weight: 500;
      color: #A8B1CA;
      padding: 39px 16px 36px 42px;
      border-bottom: 1px solid #DFDFDF;
    }
    .bill-m {
      margin: 31px 17px 36px 44px;
    }

    .bill-title {
      font-size: 36px;
      font-weight: 500;
      color: #000000;
    }

    .bill-amount {
      font-size: 48px;
      font-weight: normal;
      color: #000000;
    }
    .cny {
      font-size: 30px;
      font-weight: 500;
      color: #000000;
    }

    .bill-info {
      font-size: 26px;
      font-weight: 500;
      color: #A8B1CA;
    }

    .bill-fail {
      width: 100%;
      height: 60px;
      background: #FFECE6;
      border-radius: 8px;
      font-size: 30px;
      font-weight: 500;
      color: #F93F22;
      padding: 17px 22px;
    }

    .bill-paid {
      width: 100%;
      height: 60px;
      background: #E3E7EF;
      border-radius: 8px;
      font-size: 30px;
      font-weight: 500;
      color: #1352FD;
      padding: 17px 22px;
    }

  }
}
.b-pay-amount {
  font-size: 72px;
  font-weight: 400;
  color: #000000;
  .cny {
    font-size: 30px;
  }
}
.b-pay-tips {
  height: 79px;
  background: #E9F1FE;
  text-align: center;
  line-height: 79px;
  font-size: 26px;
  font-weight: 500;
  color: #1352FD;
}
.b-pay-fee {
  text-align: center;
  font-size: 24px;
  font-weight: 500;
  color: #B6BEC5;
  line-height: 31px;
}

</style>
