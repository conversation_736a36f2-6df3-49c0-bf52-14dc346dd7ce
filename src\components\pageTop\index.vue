<template>
  <view
    class="top-card"
    :style="
      'height:' +
      globalData.navBarHeight +
      'px;line-height:' +
      (globalData.navBarHeight + globalData.menuButtonInfoHeight + 18) +
      'px'
    "
  >
    <!-- <view
      class="left"
      :style="
        'line-height:' +
        (globalData.navBarHeight + globalData.menuButtonInfoHeight) +
        'px'
      "
    >
      <text class="iconfont icon-zuojiantou" @click="backHandel"></text>
    </view> -->
    <!-- :style="'line-height:'+(globalData.navBarHeight+globalData.menuButtonInfoHeight)+'px'" -->
    <text>{{ props.title }}</text>
  </view>
</template>

<script setup>
import Taro from "@tarojs/taro";
import { onMounted, reactive } from "vue";

const props = defineProps({
  title: {
    type: String,
    default: "标题",
  },
});

const emit = defineEmits();

// 顶部的信息
const globalData = reactive({
  navBarHeight: 0,
  menuButtonInfoHeight: 0,
});
// 获取页面信息
const getMenuInfo = () => {
  // 胶囊按钮位置信息
  const menuButtonInfo = Taro.getMenuButtonBoundingClientRect();
  globalData.navBarHeight = menuButtonInfo.top + menuButtonInfo.height + 5;
  globalData.menuButtonInfoHeight = menuButtonInfo.height;
  console.log(globalData);
};

onMounted(() => {
  getMenuInfo();
});

// 点击返回
const backHandel = () => {
  // 触发事件
  emit("backHandel");
};
</script>

<style lang="scss">
.top-card {
  position: relative;
  left: 0;
  top: 0;
  z-index: 99;
  width: 100%;
  color: #fff;
  // overflow: hidden;
  text-align: center;
  background-color: #1452fd;

  .left {
    position: absolute;
    left: 15rpx;
    height: 100%;

    text {
      margin-right: 45rpx;
      font-size: 40rpx;
    }
  }

  > text {
    display: block;
    transform: translateY(20px);
    // line-height: 90rpx;
    width: 100%;
    text-align: center;

    font-family: OPPOSans;
    font-weight: bold;
    font-size: 36px;
    color: #ffffff;
  }
}
</style>
