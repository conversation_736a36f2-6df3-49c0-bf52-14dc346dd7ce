<template>
  <view>
    <view class="container">

      <view class="repair-item" v-for="item in items" :key="item.id">
        <view class="house-name" @tap="onDetail(item.id)">
          <view class="estate-name">{{ item.house.estate_name }}</view>
          <view class="fang">{{ item.house.name }}</view>
        </view>
        <view class="repair-info" @tap="onDetail(item.id)">
          <view class="text-ellipsis">报修项目：{{item.title}}</view>
          <view class="mt10">报修日期：{{item.created_at}}</view>
        </view>
        <view class="repair-status">
          <text class="status1" v-if="item.status === 1">待处理</text>
          <text class="status2" v-if="item.status === 2">已处理</text>
        </view>

        <view class="repair-btn"><button v-if="item.status === 1" class="btn-primary-small btn-inline" @tap="handleConfirm">确认已处理</button></view>
      </view>

      <view class="empty" v-if="items.length === 0">
          <view class="empty-text" style="text-align: center;">您暂无报修的设备~</view>
      </view>

    </view>

  </view>
</template>

<script setup>
  import { ref } from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad, usePullDownRefresh, useReachBottom } from '@tarojs/taro'
  import request from '@/utils/request'
  import './repair.scss'

  import MyIcon from '@/components/MyIcon'



  import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

  usePullDownRefresh(() => {
    console.log('onPullDownRefresh')
  })

  const onAdd = (keyword) => {
    Taro.navigateTo({
      url: '/pages/tenant/repairAdd/repairAdd'
    })
  }


  /** ----------------------接口数据-begin----------------------------------- */
  const items = ref([])
  const total = ref(0)

  const params = ref({
    page: 1,
    status: 1
  })

  const onChangeStatus = (val) => {
    params.value.status = val
    fetch()
  }

  const fetch = () => {
    items.value = []
    params.value.page = 1
    getList()
  }

  const getList = () => {
    request.get({
      url: 'repair',
      data: {
        ... params.value
      }
    }).then(res => {
      if (items.value.length > 0) {
        items.value = [items.value, ... res.data.items]
      } else {
        items.value = res.data.items
      }
      total.value = res.data.total
      if (res.data.currentPage >= res.data.lastPage) {
        isLastPage.value = true
      }
    })
  }
  /** ----------------------接口数据-end----------------------------------- */

  useLoad((options) => {
    if (options.status) {
      params.value.status = options.status
    }
  })

  useDidShow(() => {
    fetch()
  })

  usePullDownRefresh(() => {
    Taro.stopPullDownRefresh()
    fetch()
  })

  const isLastPage = ref(false)

  useReachBottom(() => {
    if (!isLastPage.value) {
      params.value.page += 1
      getList()
    }
  })

  const handleConfirm = (id) => {
    request.post({
      url: 'repair/' + id + '/confirm'
    }).then(res => {
      items.value = items.value.map(item => {
        if (item.id === id) {
          item.status = 2
        }
        return item
      })
    })
  }

  const onDetail = (id) => {
    Taro.navigateTo({
      url: '/pages/repair/detail/detail?id=' + id
    })
  }


</script>
