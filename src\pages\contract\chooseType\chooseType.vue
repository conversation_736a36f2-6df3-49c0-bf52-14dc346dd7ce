<!--
 * @Autor: lisong
 * @Date: 2023-08-11 14:11:02
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-11 15:22:25
-->
<template>
  <view
    :class="[
      'list',
      {
        'list-select': index === selectIndex,
      },
    ]"
    v-for="(item, index) in list"
    :key="item.title"
    @tap="handleChange(index)"
  >
    <image
      v-if="index === selectIndex"
      class="list-select_icon"
      src="https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/contract/select.png"
    />
    <view class="list-title">{{ item.title }}</view>
    <view class="list-content">{{ item.content }}</view>
  </view>
  <myBottomBtn btnTxt="确认选择" @click="handleClick"></myBottomBtn>
</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import myBottomBtn from "@/components/MyBottomBtn/MyBottomBtn";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";

//  constant
definePageConfig({
  navigationBarTitleText: "合同类型",
  navigationBarBackgroundColor: "#1352FD",
  navigationBarTextStyle: "white",
});

import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

let house_id = 0;

useLoad((e) => {
  house_id = e.id;
});

const list = [
  {
    title: "纸质合同",
    content: "适用于存续租房用户，将合同上传云端保存，便于日常 管理归档。",
  },
  {
    title: "电子合同",
    content:
      "提供专业的租房电子合同，引入电子签名技术具备法律效力，支持自定义模板，提高签约效率，实行无纸化管 理。",
  },
];

const selectIndex = ref(0);
const handleChange = (index) => {
  selectIndex.value = index;
};

const handleClick = () => {

  if (selectIndex.value === 1 && (!globalStore.userInfo.bank || !globalStore.userInfo.account_id)) {
    Taro.showModal({
      title: '签署提示',
      content: '您尚未绑定银行卡，请点击确定前往绑卡',
      success: (res) => {
        if (res.confirm) {
          Taro.navigateTo({
            url: '/pages/money/bindCard'
          })
        }
      }
    })
    return;
  }
  request
    .post({
      url: "contract/create",
      data: {
        house_id,
        type: (selectIndex.value === 0 ? 1 : 2),
      },
    })
    .then((res) => {
      Taro.redirectTo({
        url:
          "/pages/contract/createZcontract/createZcontract?id=" + res.data.id
      });
    });
};
</script>

<style lang="scss">
.list {
  margin: 40rpx;
  height: 260rpx;
  background: #eef1f5;
  border-radius: 20rpx;
  overflow: hidden;
  color: #98a9c0;
  position: relative;
  .list-title {
    line-height: 1;
    font-size: 40rpx;
    font-family: OPPOSans;
    font-weight: bold;
    margin: 55rpx 0 24rpx 26rpx;
  }
  .list-content {
    font-size: 26rpx;
    font-family: OPPOSans;
    font-weight: 500;
    padding: 0 26rpx;
  }
  .list-select_icon {
    width: 68rpx;
    height: 73rpx;
    position: absolute;
    top: 0;
    right: 0;
  }
}

.list-select {
  border: 4px solid #1352fd;
  color: #1352fd;
  background: #fff;
}
</style>
