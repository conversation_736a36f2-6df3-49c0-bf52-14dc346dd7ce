<!--
 * @Autor: lisong
 * @Date: 2023-08-04 11:44:52
 * @LastEditors: lisong
 * @LastEditTime: 2023-08-10 15:08:52
-->
<template>
  <view class="my-search">
    <view class="my-search-box flex flex-v-center" :class="isAlipay ? 's-p-17-a' : 's-p-17'">
      <view class="my-search-icon"
        ><image :src="IconSearch" class="icon-search"></image
      ></view>
      <view class="my-search-input"
        ><input
          class="_my-search-input"
          :placeholder="placeholder"
          confirmType="search"
          @confirm="onChange"
          @input="onInput"
      /></view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  placeholder: {
    type: String,
    required: false,
    default: "",
  },
});

const IconSearch = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-search.png";

const isAlipay = process.env.TARO_ENV === 'alipay';

const emit = defineEmits(["search"]);

const onChange = (e) => {
  emit("search", e.detail.value);
};

const onInput = (e) => {
  emit("input", e.detail.value);
}
</script>
<style>
/*search*/
.my-search {
  background-color: #1352fd;
  height: 72px;
  padding: 18px;
}
.my-search-box {
  background-color: #ffffff;
  border-radius: 36px;
}
.s-p-17 {
  padding: 17px;
}
.s-p-17-a {
  padding: 0 17px;
}
.my-search-icon {
  width: 38px;
}
.my-search-input {
  flex: 1;
  margin-left: 10px;
}
._my-search-input {
  font-size: 30px;
}
._my-search-input:-ms-input-placeholder {
  color: #c0cbe7;
}
.icon-search {
  width: 38px;
  height: 38px;
  display: block;
}
</style>
