import Taro from '@tarojs/taro'
import { useGlobalStore } from '@/stores'
const globalStore = useGlobalStore()


export const checkPhoneNumber = (phoneNumber) => {
    var regex = /^1[3-9][0-9]{9}$/
    return regex.test(phoneNumber)
}
export const checkPassword = (pwd) => {
  if (pwd.length < 6) {
    return false
  }
  return true
}

export const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('-')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

export const formatDate = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  return `${[year, month, day].map(formatNumber).join('-')}`
}

export const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

export const addMonth = (date, months) => {
  date.setMonth(date.getMonth() + (months * 1))
  return formatDate(date)
}

export const addDay = (date, days) => {
  date.setDate(date.getDate() + (days * 1))
  return formatDate(date)
}

export const ab2hex = (buffer) => {
  var hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    function(bit) {
      return ('00' + bit.toString(16)).slice(-2)
    }
  )
  return hexArr.join('');
}

export const ab2hexArr = (buffer) => {
  return Array.prototype.map.call(
    new Uint8Array(buffer),
    function (bit) {
      return ('00' + bit.toString(16)).slice(-2)
    }
  );
}

export const int2hex = (decimalNumber) => {
  let hexStr = parseInt(decimalNumber).toString(16) + ''
  // console.log('int2hex', decimalNumber, hexStr)
  let len = hexStr.length
  for (let i = 0; i < (6 - len); i ++) {
    hexStr = '0' + hexStr
  }
  return hexStr
}

export const int2hex2 = (int, len) => {
  if (len) {
    return (int).toString(16).toUpperCase().padStart(len, "0")
  } else {
    return (int).toString(16).toUpperCase()
  }
}

/**
 * 16进制字符串转 ArrayBuffer
 */
export const hexToArrayBuffer = (hex) => {
  let hexArr = hex
  if (typeof hex === 'string') {
    hexArr = hex.match(/[\da-f]{2}/gi)
  }
  return new Uint8Array(
    hexArr.map((byte) => {
      return parseInt(byte, 16);
    })
  ).buffer;
}

export const int2Byte = (number, length) => {
  var bytes = [];
  var i = 0;
  do {
    bytes[i++] = number & (255);
    number = number >> 8;
    console.log("右移后的number"+number);
  } while (i < length)
  return bytes;
}

export const toFixed = (n, len) => {
  Number.prototype.toFixed=function(len)
  {
  var add = 0;
  var s,temp;
  var s1 = this + "";
  var start = s1.indexOf(".");
  if(s1.substr(start+len+1,1)>=5)add=1;
  var temp = Math.pow(10,len);
  s = Math.floor(this * temp) + add;
  return s/temp;
  }
  return n.toFixed(len)
}

export const pxToRpx = (px) => {
  const systemInfo = Taro.getSystemInfoSync();
  const pixelRatio = systemInfo.pixelRatio;
  return px * (750 / systemInfo.windowWidth);
}

/**
 * 计算给定日期与当前日期相差的天数。
 *
 * @param {string} dateString 日期字符串，格式为 'YYYY-MM-DD HH:mm:ss'。
 * @returns {number}  返回给定日期与当前日期相差的天数，可以是正数或负数。
 */
export function getDaysDifference(dateString) {
  const date = new Date(dateString);

  if (isNaN(date.getTime())) {
    // console.error("Invalid date string:", dateString);
    return NaN; // 返回 NaN 表示无效日期
  }

  const now = new Date();

   // 获取日期部分，忽略时间
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const currentDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());


  const diff = targetDate.getTime() - currentDate.getTime();
  const diffDays = diff / (1000 * 60 * 60 * 24);

  return diffDays;
}

/**
 * 合并连续的时间段
 * @param {string[]} timeRanges - 原始时间段数组，例如 ["08:00-09:00", "09:00-10:00", "10:00-11:00", "13:00-14:00", "14:00-15:00", "15:00-16:00", "16:00-17:00"]
 * @returns {string[]} - 合并后的时间段数组，例如 ["08:00-11:00", "13:00-17:00"]
 */
export function mergeTimeRanges(timeRanges) {
  if (!timeRanges || timeRanges.length === 0) {
    return [];
  }

  // 排序时间段，确保它们按时间顺序排列
  timeRanges.sort();

  const mergedRanges = [];
  let currentRange = timeRanges[0].split('-'); // 将第一个时间段分割成开始和结束时间

  for (let i = 1; i < timeRanges.length; i++) {
    const nextRange = timeRanges[i].split('-');
    // 检查当前时间段的结束时间是否与下一个时间段的开始时间相邻
    if (currentRange[1] === nextRange[0]) {
      // 如果相邻，则更新当前时间段的结束时间
      currentRange[1] = nextRange[1];
    } else {
      // 如果不相邻，则将当前时间段添加到合并后的数组中，并开始一个新的时间段
      mergedRanges.push(currentRange.join('-'));
      currentRange = nextRange;
    }
  }

  // 将最后一个时间段添加到合并后的数组中
  mergedRanges.push(currentRange.join('-'));

  return mergedRanges;
}


/**
 * 处理数据
 */
export function processData(data) {
  const result = [
    { name: '春秋季', month: [], coef1: [], coef2: [], coef3: [], coef4: [],provinceName: [],provinceCode:[] },
    { name: '夏冬季', month: [], coef1: [], coef2: [], coef3: [], coef4: [],provinceName: [],provinceCode:[] }
  ];

  for (const item of data) {
    const season = item.season;
    const month = item.month;
    const coefType = item.coef_type;
    const timeRange = `${item.start}-${item.end}`;
    const provinceName = item.area.name;
    const provinceCode = item.area.adcode;

    let targetSeason;
    if (season === 1) {
      targetSeason = result[0]; // 春秋季
    } else if (season === 2) {
      targetSeason = result[1]; // 夏冬季
    } else {
      // Handle cases where season is not 1 or 2 (optional)
      continue;
    }

    if (!targetSeason.month.includes(month)) {
      targetSeason.month.push(month);
    }

    if (!targetSeason[coefType].includes(timeRange)) { // 去重
      targetSeason[coefType].push(timeRange);
    }

    if (!targetSeason.provinceName.includes(provinceName)) {
      targetSeason.provinceName.push(provinceName);
    }

    if (!targetSeason.provinceCode.includes(provinceCode)) {
      targetSeason.provinceCode.push(provinceCode);
    }
  }

  // Sort the months array
  result.forEach(season => {
    season.month.sort((a, b) => a - b);
    season.coef1 = [...new Set(season.coef1)];  // 去重
    season.coef2 = [...new Set(season.coef2)];  // 去重
    season.coef3 = [...new Set(season.coef3)];  // 去重
    season.coef4 = [...new Set(season.coef4)];  // 去重
  });
  return result;
}

/**
 * 展开时间范围
 */
export function expandTimeRanges(timeRanges) {
  if (!timeRanges) return [];

  const expandedRanges = [];

  for (const timeRange of timeRanges) {
    const [start, end] = timeRange.split('-');
    const [startHour, startMinute] = start.split(':').map(Number);
    const [endHour, endMinute] = end.split(':').map(Number);

    let currentHour = startHour;

    while (currentHour < endHour) { // 注意这里是 < 而不是 <= , 保证生成的是相邻一个小时的单位
      const formattedStartHour = String(currentHour).padStart(2, '0');
      const formattedEndHour = String(currentHour + 1).padStart(2, '0'); // 下一个小时
      expandedRanges.push(`${formattedStartHour}:00-${formattedEndHour}:00`);
      currentHour++;
    }
  }

  return expandedRanges;
}


/**
 * 时间范围处理成后端需要的格式
 */
export function combineTimeRanges(data) {
  const result = [];

  for (const key in data) {
      if (key.endsWith("Expand") && Array.isArray(data[key]) && data[key].length > 0) {
          const coefType = key.replace("Expand", "");
          const timeRanges = data[key];

          for (const range of timeRanges) {
              const [start, end] = range.split("-");
              result.push({
                  "start": start,
                  "end": end,
                  "coef_type": coefType
              });
          }
      }
  }

  return result;
}

/**
 * 封装一个 Promise，当数据变为 true 时 resolve，否则一直等待。
 *
 * @param {() => boolean} getData - 获取布尔类型数据的函数。
 * @param {number} [interval=100] - 轮询检查数据变化的间隔时间（毫秒）。
 * @returns {Promise<boolean>} - 当数据变为 true 时 resolve 的 Promise。
 */
export function waitForTrue(getData, interval = 100) {
  return new Promise((resolve) => {
    function checkData() {
      const currentValue = getData();
      // console.log(`Checking data... Current value: ${currentValue}`);
      
      if (currentValue === true) {
        resolve(true); // 数据变为 true，resolve Promise
      } else {
        setTimeout(checkData, interval); // 数据不是 true，等待一段时间后再次检查
      }
    }

    checkData(); // 立即开始检查
  });
}

// export function processData(data) {
//   const result = [
//     { name: '春秋季', month: [], coef1: [], coef2: [], coef3: [], coef4: [] },
//     { name: '夏冬季', month: [], coef1: [], coef2: [], coef3: [], coef4: [] }
//   ];

//   for (const item of data) {
//     const season = item.season;
//     const month = item.month;
//     const coefType = item.coef_type;
//     const timeRange = `${item.start}-${item.end}`;

//     let targetSeason;
//     if (season === 1) {
//       targetSeason = result[0]; // 春秋季
//     } else if (season === 2) {
//       targetSeason = result[1]; // 夏冬季
//     } else {
//       // Handle cases where season is not 1 or 2 (optional)
//       continue;
//     }

//     if (!targetSeason.month.includes(month)) {
//       targetSeason.month.push(month);
//     }

//     targetSeason[coefType].push(timeRange);
//   }

//   // Sort the months array
//   result.forEach(season => {
//     season.month.sort((a, b) => a - b);
//   });

//   return result;
// }

  /**
   * 检测网络信号
   * @description 2g 3g 不允许充值
   * @description 4g 5g 检测信号需要大于 -110dbm
   */
   export const checkNetInfo = () => {
     return new Promise((resolve, reject) => {
      Taro.getNetworkType({
        success (res) {
          const networkType = res.networkType//网络类型
          const weakNet = res.weakNet//是否弱网
          const signalStrength = res?.signalStrength//信号强弱，单位 dbm
          console.log(res, '***getNetworkType***');

          if (networkType === 'none') { 
            Taro.showToast({
              title: '请打开网络！',
              icon: 'none',
            })
            return reject('请打开网络！')
          }
          // || networkType === '3g'
          if (networkType === '2g') { 
            Taro.showToast({
              title: '2g不允许充值,请使用wifi或4g或5g！',
              icon: 'none',
            })
            return reject('2g和3g不允许充值,请使用wifi或4g或5g！')
          }

          //wifi 
          if (networkType === 'wifi') {
            if (signalStrength && Math.abs(signalStrength) >= Math.abs(-110)) { 
              Taro.showToast({
                title: '当前wifi信号较弱,请切换到信号稳定的区域进行充值!',
                icon: 'none',
              })
              return reject('当前wifi信号较弱,请切换到信号稳定的区域进行充值!')
            }
          }
          //4g或者5g
          if (signalStrength && Math.abs(signalStrength) >= Math.abs(-120)) { 
            Taro.showToast({
              title: '网络信号不稳定,请重新打开小程序后重试',
              icon: 'none',
            })
            return reject('网络信号不稳定,请重新打开小程序后重试')
          }
          resolve()
        }
      })
    })
  }

  /**
   * 检测蓝牙信号
   * @param typwe: 2:水表  其他：电表
   * @description 水表需要大于-90
   * @description 电表需要大于-85
   */
  export const checkIsConnectRssi = (type = 2) => {
    return new Promise((resolve, reject) => {
      let RSSI = type  == 2 ? -90 : -85
      if (!globalStore.device.deviceId) {
        console.log('当前rssi',globalStore.deviceRssi);
        
        // 如果没连接则用，搜索设备里的rssi
        if (globalStore.deviceRssi && Math.abs(globalStore.deviceRssi) >= Math.abs(RSSI)) { 
          Taro.showToast({
            title: '当前蓝牙信号不稳定,请靠近设备！',
            icon: 'none',
          })
          return reject('当前蓝牙信号不稳定')
        }
        resolve()
        return
      }
      Taro.getBLEDeviceRSSI({
        deviceId: globalStore.device.deviceId,
        success: (res) => {
          const rssi = res.RSSI
          console.log(res, '***getBLEDeviceRSSI***')

          if (rssi && Math.abs(rssi) >= Math.abs(RSSI)) { 
            Taro.showToast({
              title: '当前蓝牙信号不稳定,请靠近设备！',
              icon: 'none',
            })
            return reject('当前蓝牙信号不稳定')
          }
          resolve()
        },
        fail: (res) => {
          if (res.errCode == 10006) {
            // 断开连接
            Taro.showModal({
              title: '提示',
              content: '设备已断开连接，请重新连接！',
              showCancel: false,
              success: function (res) {
                  Taro.navigateBack()
              }
            })
          } else {
            Taro.showModal({
              title: '提示',
              content: res.errMsg || '蓝牙通信失败请重试',
              showCancel: false,
              success: function (res) {
                  Taro.navigateBack()
              }
            })
          }
          console.log(res, '***getBLEDeviceRSSIErro***');
          reject()
        }
      })
    })
  }
