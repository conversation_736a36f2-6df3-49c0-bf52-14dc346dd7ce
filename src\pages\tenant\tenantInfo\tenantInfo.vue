<template>
  <view>
    <view class="tabs">
      <scroll-view scroll-x="true" class="tabs-scr">
        <view class="content">
          <view
            class="tab-item"
            :class="{ active: item === tabActiveIndex }"
            v-for="(item, idx) in num"
            :key="item"
            @tap="tabActiveIndex = item"
          >
            第{{ item }}位租客
            <text
              class="icon-close iconfont"
              @tap.stop="del(idx)"
              v-if="isEdit"
            ></text>
          </view>
        </view>
      </scroll-view>
    </view>
    <scroll-view scroll-y class="scroll-box">
      <view
        class="box"
        v-for="(item, idx) in num"
        :key="item"
        v-show="item == tabActiveIndex"
      >
        <view class="top-card">
          <view>
            <view class="left">
              <text class="b">头像面</text>
              <text>上传您的证件头像面</text>
            </view>
            <view class="right" @tap="uploadImage(0, idx, $event)">
              <image :src="userFrom[idx].idRen" mode="scaleToFill" />
              <text
                class="icon-close iconfont"
                v-if="userFrom[idx].id_card_image1"
                id="close"
                @tap.stop="delImg(0, idx)"
              ></text>
            </view>
          </view>
          <view>
            <view class="left">
              <text class="b">国徽面</text>
              <text>上传您的证件国徽面</text>
            </view>
            <view class="right" @tap="uploadImage(1, idx, $event)">
              <image :src="userFrom[idx].idBei" mode="scaleToFill" />
              <text
                class="icon-close iconfont"
                v-if="userFrom[idx].id_card_image2"
                id="close"
                @tap.stop="delImg(1, idx)"
              ></text>
            </view>
          </view>
        </view>
        <view class="bot-box">
          <my-cell prefix="联系电话" align="right" required>
            <template #content
              ><input
                class="my-input-m"
                v-model="userFrom[idx].mobile"
                placeholder="请输入"
            /></template>
          </my-cell>
          <my-cell
            prefix="承租人(乙方)"
            align="right"
            prefixWidth="280"
            required
          >
            <template #prefix>
              <text>{{
                userFrom[idx].is_master == 0 ? "入住人(乙方)" : "承租人(乙方)"
              }}</text>
            </template>
            <template #content
              ><input
                class="my-input-m"
                v-model="userFrom[idx].name"
                placeholder="请输入"
            /></template>
          </my-cell>
          <my-cell prefix="证件号码" align="right" required>
            <template #content
              ><input
                class="my-input-m"
                v-model="userFrom[idx].id_no"
                placeholder="请输入"
            /></template>
          </my-cell>
          <my-cell prefix="是否承租人" prefixWidth="280" align="right" required>
            <template #content>
              <view class="select-box">
                <view
                  :class="{ active: userFrom[idx].is_master == 1 }"
                  @tap="userFrom[idx].is_master = 1"
                  >承租人</view
                >
                <view
                  :class="{ active: userFrom[idx].is_master == 0 }"
                  @tap="userFrom[idx].is_master = 0"
                  v-if="num > 1"
                  >入住人</view
                >
              </view>
            </template>
          </my-cell>
        </view>
      </view>
    </scroll-view>
    <view class="footer-fixed">
      <view class="p20">
        <button class="btn-primary" @tap="onSubmitBindBank">确定保存</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, unref, computed, watch } from "vue";
import request from "@/utils/request";
import MyCell from "@/components/MyCell";
import MyPopup from "@/components/MyPopup";
import MyInput from "@/components/MyInput";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
import { useGlobalStore } from "@/stores";
definePageConfig({
  navigationBarTitleText: "承租人信息",
  navigationBarBackgroundColor: "#1352fd",
  navigationBarTextStyle: "white",
});

const globalStore = useGlobalStore();

const tabActiveIndex = ref(1); //第几人

const num = ref(1); //几人

const contract_id = ref("");

const idRen = ref("https://yimits.oss-cn-beijing.aliyuncs.com/images/ren-id.png");

const idBei = ref("https://yimits.oss-cn-beijing.aliyuncs.com/images/ren-id-2.png");

const isEdit = ref(false);

const userFrom = ref([
  {
    id: null,
    mobile: "",
    name: "",
    id_no: "",
    id_card_image1: "",
    id_card_image2: "",
    is_master: 0,

    idRen: idRen.value,
    idBei: idBei.value,
  },
]);

const init = () => {
  request
    .get({
      url: "user/checkFast",
    })
    .then((res) => {
      if (res.code == 200) {
        console.log(res.data, "res.data");
        contract_id.value = res.data.contract_id;
        userFrom.value.splice(0);
        for (let i = 1; i <= res.data.need_count; i++) {
          userFrom.value.push({
            id: null,
            mobile: "",
            name: "",
            id_no: "",
            id_card_image1: "",
            id_card_image2: "",
            is_master: 0,

            idRen: idRen.value,
            idBei: idBei.value,
          });
          if (i == 1) {
            userFrom.value[0].is_master = 1;
          }
        }
        num.value = res.data.need_count || 1;

        if (res.data.data && res.data.data.length > 0) {
          // userFrom.value = res.data.data;
          res.data.data.forEach((el, idx) => {
            userFrom.value[idx] = el;
            userFrom.value[idx].idRen = el.id_card_image1;
            userFrom.value[idx].idBei = el.id_card_image2;
          });
          if (res.data.now_count > res.data.need_count) {
            num.value = res.data.now_count;
            isEdit.value = true;
            Taro.showModal({
              title: "温馨提示",
              content: "请点击上方叉号删除部分房客,然后重新提交认证！",
              showCancel: false,
              success: function (res) {
                if (res.confirm) {
                  console.log("用户点击确定");
                } else if (res.cancel) {
                  console.log("用户点击取消");
                }
              },
            });
          } else {
            isEdit.value = false;
          }
        }
      }
    });
};

useLoad(() => {
  init();
});

const qiyeFrom = reactive({
  userPhone: "",
  name: "",
  id: "",
  yingImg: "",
  pname: "",
  idCode: "",
  phone: "",
});

// 校验
const rules = {
  mobile: [{ message: "请输入联系电话" }],
  name: [{ message: "请输入承租人" }],
  id_no: [{ message: "请输入证件号码" }],
  id_card_image1: [{ message: "请上传头像面" }],
  id_card_image2: [{ message: "请上传国徽面" }],
};

const rulesQiye = {
  userPhone: [{ message: "请输入承租人电话" }],
  name: [{ message: "请输入企业名称" }],
  id: [{ message: "请输入统一社会代码" }],
  yingImg: [{ message: "请上传营业执照！" }],
  // pname: [{ message: "请输入法人" }],
  // idCode: [{ message: "请输入身份证号码" }],
  // phone: [{ message: "请输入联系电话" }],
};

const validate = (from, rules, index) => {
  let valid = true;
  for (let i = 0; i < Object.keys(from).length; i++) {
    const key = Object.keys(from)[i];
    if (from[Object.keys(from)[i]] == "") {
      if (rules[key]) {
        tabActiveIndex.value = index + 1;
        Taro.showToast({
          title: rules[key][0].message + `(第${index + 1}位租客)`,
          icon: "none",
        });
        valid = false;
        break;
      }
    }
  }
  return valid;
};

import upload from "@/utils/upload";

const isAp = process.env.TARO_ENV === "alipay";

// 点击预览
const previewImage = (type, index) => {
  if (type == 0) {
    Taro.previewImage({
      urls: [userFrom.value[index].idRen],
      current: userFrom.value[index].idRen,
    });
  } else if (type == 1) {
    Taro.previewImage({
      urls: [userFrom.value[index].idBei],
      current: userFrom.value[index].idBei,
    });
  } else if (type == 2) {
    console.log("cloick");
    Taro.previewImage({
      urls: [qiyeFrom.yingImg],
      current: qiyeFrom.yingImg,
    });
  }
};

const imageDataHandel = (type, serverUrl, index) => {
  Taro.showLoading({
    title: "识别中",
    mask: true,
  });
  request
    .post({
      url: "account/identifyImg",
      data: {
        img_url: serverUrl,
        api_name: type == 2 ? "bizlicense" : "idcard",
      },
      showLoading: false,
    })
    .then((resImgData) => {
      try {
        Taro.hideLoading();
        const data = JSON.parse(resImgData.data);
        console.log(data);
        if (type == 1) {
          // 反面
          if (data.type != "Back") {
            return Taro.showToast({
              icon: "error",
              title: "请上传国徽面！",
              success() {
                // idBei.value = "https://yimits.oss-cn-beijing.aliyuncs.com/images/ren-id-2.png";
                userFrom.value[index].idBei =
                  "https://yimits.oss-cn-beijing.aliyuncs.com/images/ren-id-2.png";
                userFrom.value[index].id_card_image2 = "";
              },
            });
          }
        } else if (type == 0) {
          // 正面
          if (data.type != "Front") {
            return Taro.showToast({
              icon: "error",
              title: "请上传人像面！",
              success() {
                // idRen.value = "https://yimits.oss-cn-beijing.aliyuncs.com/images/ren-id.png";
                userFrom.value[index].idRen =
                  "https://yimits.oss-cn-beijing.aliyuncs.com/images/ren-id.png";
                userFrom.value[index].id_card_image1 = "";
              },
            });
          }
          userFrom.value[index].id_no = data.id;
          userFrom.value[index].name = data.name;
        }
      } catch (e) {
        //TODO handle the exception
        console.log("识别失败！手动填写");
      }
      Taro.hideLoading();
    })
    .catch(() => {
      Taro.hideLoading();
    });
};

// 删除
const delImg = (type, index) => {
  if (type == 0) {
    userFrom.value[index].idRen = "https://yimits.oss-cn-beijing.aliyuncs.com/images/ren-id.png";
    userFrom.value[index].id_card_image1 = "";
  } else if (type == 1) {
    userFrom.value[index].idBei = "https://yimits.oss-cn-beijing.aliyuncs.com/images/ren-id-2.png";
    userFrom.value[index].id_card_image2 = "";
  }
};

// 预览

const uploadImage = (type, index, event) => {
  console.log(event);
  if (event.mpEvent.target.id == "close") return;
  if (type == 0 && !unref(userFrom)[index].idRen.includes("ren-id"))
    return previewImage(type, index);
  if (type == 1 && !unref(userFrom)[index].idBei.includes("ren-id"))
    return previewImage(type, index);
  // type: 0是身份证人像  1是身份证背面
  upload.chooseImage(1).then((images) => {
    images.map((img) => {
      upload.uploadFile(img).then((url) => {
        if (type == 0) {
          userFrom.value[index].idRen = url;
          userFrom.value[index].id_card_image1 = url;
          // idRen.value = url;
        } else if (type == 1) {
          // idBei.value = url;
          userFrom.value[index].idBei = url;
          userFrom.value[index].id_card_image2 = url;
        }
        console.log(url);
        imageDataHandel(type, url, index);
      });
    });
  });
};

const getDate = (type, ap = false) => {
  const date = new Date();
  let year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();

  if (type === "start") {
    year = year - 60;
  } else if (type === "end") {
    year = year + 2;
  }
  month = month > 9 ? month : "0" + month;
  day = day > 9 ? day : "0" + day;
  if (ap) {
    return `${year}-${month}-${day}`;
  } else {
    return `${year}${month}${day}`;
  }
};

const del = (idx) => {
  console.log(userFrom.value[idx]);
  Taro.showModal({
    title: "温馨提示",
    content: `是否删除${userFrom.value[idx]?.name}当前租客?`,
    showCancel: true,
    success: function (res) {
      if (res.confirm) {
        console.log("用户点击确定");
        request
          .get({
            url: "contract/removeTenant",
            data: {
              tenant_id: userFrom.value[idx]?.id,
            },
          })
          .then(() => {
            Taro.showToast({
              title: "删除成功",
              icon: "none",
            });
            init();
          });
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });
};

/**
 * 如果有sn并且是租客端 说明分享绑定过设备 跳转到电表充值页面
 */
const autoJumpRecharge = () => {
  return new Promise(async (reslove) => {
    const userInfo = await globalStore.getUserInfo();
    console.log(userInfo);
    if (userInfo?.sn && Boolean(userInfo?.sn) && userInfo?.type == "tenant") {
      console.log("autoJumpRecharge");
      request
        .get({
          url: "device/search",
          data: {
            sn: userInfo?.sn,
          },
        })
        .then((response) => {
          globalStore.setTempDevice(response.data);
          reslove();
          request
            .get({
              url:
                "device/" +
                globalStore.tempDeviceList[
                  globalStore.tempDeviceList.length - 1
                ].id,
            })
            .then((res) => {
              reslove();
              Taro.navigateTo({
                url: `/pages/tenant/deviceDetail/deviceDetail?sn=${userInfo.sn}&share=1'`,
                success: function (res) {
                  isLoadAuto.value = true;
                },
              });
            });
        });
    } else {
      globalStore.setTempDevice();
      reslove();
    }
  });
};

const onSubmitBindBank = () => {
  console.log(userFrom.value);
  const is = userFrom.value.every((item, index) => {
    return validate(item, rules, index) == true;
  });
  const isMasterArr = userFrom.value.filter((item, index) => {
    return item.is_master == 1;
  });
  // console.log(isMasterArr);
  if (isMasterArr.length > 1) {
    return Taro.showToast({
      icon: "none",
      title: "只能绑定一个承租人",
    });
  }
  if (isMasterArr.length <= 0) {
    return Taro.showToast({
      icon: "none",
      title: "至少绑定一个承租人",
    });
  }
  if (is) {
    // return  console.log("ok");
    const transArr = userFrom.value.map((item) => {
      return {
        mobile: item.mobile,
        name: item.name,
        id_no: item.id_no,
        id_card_image1: item.id_card_image1,
        id_card_image2: item.id_card_image2,
        is_master: item.is_master,
        id: item.id,
      };
    });
    request
      .post({
        url: "contract/batchTenant",
        data: {
          data: transArr,
          contract_id: contract_id.value,
        },
      })
      .then((res) => {
        if (res.code == 200) {
          Taro.showToast({
            icon: "none",
            title: "绑定成功",
          });
          // setTimeout(() => {
            // Taro.navigateBack({
            //   delta: 1,
            // });
            autoJumpRecharge();
          // }, 1500);
        }
      });
    console.log(transArr);
    console.log(JSON.stringify(transArr));
  }
};
</script>

<style lang="scss">
page {
  background-color: #f1f3f7;
  position: relative;
  .scroll-box {
    padding-bottom: 154px;
    .box {
      height: 76vh;
      overflow: scroll;
    }
  }
  .top-card {
    width: 750px;
    height: 626px;
    background: #ffffff;
    padding: 75px 69px 76px 91px;
    box-sizing: border-box;
    > view {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:nth-of-type(2) {
        margin-top: 89px;
      }
    }
    .right {
      position: relative;
    }
    .iconfont {
      position: absolute;
      top: 10px;
      right: 10px;
      font-size: 30px;
      background-color: #fff;
      color: #333;
      border-radius: 50%;
      padding: 5px;
    }
    image {
      width: 294px;
      height: 193px;
    }
    .left {
      display: flex;
      flex-direction: column;
      text {
        font-family: OPPOSans;
        font-weight: 500;
        font-size: 24px;
        color: #7a839d;
        margin-top: 10px;
      }
      .b {
        font-family: OPPOSans;
        font-weight: 500;
        font-size: 30px;
        color: #000000;
      }
    }
  }

  .bot-box {
    margin-top: 35px;
    background-color: #fff;
    .select-box {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      > view {
        padding: 5px 15px;
        font-size: 25px;
        background-color: #ccc;
        border-radius: 5px;
        margin-left: 15px;
        &.active {
          background-color: #1352fd;
          color: #fff;
        }
      }
    }
  }

  .ri-up {
    .iconfont {
      color: #b6b6b6;
      font-size: 26px;
    }
  }

  .footer-fixed {
    width: 750px;
    height: 154px;
    background: #ffffff;
    box-shadow: 0px -4px 32px 0px rgba(54, 69, 193, 0.24);
    .btn-primary {
      width: 700px;
      height: 88px;
      background: #1352fd;
      border-radius: 20px;
      transition: all 0.3s;
      &:active {
        opacity: 0.7;
      }
    }
  }

  .select-custom-com-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    overflow: hidden;
    z-index: 999999999;

    .select-custom-com {
      background-color: #fff;
      width: 100%;
      height: 300rpx;
      position: fixed;
      bottom: 0;
      left: 0;
      z-index: 99999;

      .li {
        border-bottom: 1px solid #ccc;
        text-align: center;
        height: 100rpx;
        line-height: 100rpx;

        &:active {
          background-color: #e3e3e3;
        }
      }
    }
  }
}
.bind-check-form {
  background: #ffffff;
}
.tabs {
  background-color: #1352fd;

  .tabs-scr {
    .content {
      display: flex;
      align-items: center;
      height: 120px;
      // overflow-x: auto;
    }
  }

  .tab-item {
    position: relative;
    width: 200px;
    flex-shrink: 0;
    position: relative;
    color: rgba(255, 255, 255, 0.6);
    text-align: center;
    .iconfont {
      position: absolute;
      top: 0;
      right: -6px;
      font-size: 20px;
      background-color: #fff;
      color: #333;
      border-radius: 50%;
      padding: 5px;
    }

    &.active {
      color: #fff;

      &::before {
        position: absolute;
        left: 50%;
        bottom: -17rpx;
        background-color: #fff;
        width: 40rpx;
        height: 10rpx;
        display: block;
        content: "";
        transform: translateX(-50%);
        border-radius: 5rpx;
      }
    }
  }
}

.qiye-box {
  padding-bottom: calc(env(safe-area-inset-bottom) + 204rpx);
  .top-box {
    background-color: #fff;
    .yingImg {
      width: 50px;
      height: 50px;
    }
  }
  .bot {
    background-color: #fff;
    margin-top: 37px;
  }
  .form-item {
    border-bottom: 1px solid #dfdfdf;
    height: 120rpx;
    justify-content: center;
    background-color: #fff;
    line-height: 119rpx;
    box-sizing: border-box;

    .u-form-item__body__right__message {
      transform: translateY(-15rpx);
    }
  }

  .item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    padding-left: 30px;
    position: relative;
    padding-right: 30px;

    .select-date {
      font-size: 32rpx;
      color: #ccc;
    }

    .ri {
      color: #b6bec5;
      font-size: 32rpx;
    }

    input {
      text-align: right !important;
      font-size: 32rpx !important;
      z-index: 0;
    }

    .phone {
      color: #b6bec5;
      position: absolute;
      right: 40rpx;
      font-size: 30rpx;
    }

    .label {
      margin-right: 40rpx;
      font-size: 32rpx;

      .b {
        color: red;
        margin-right: 10rpx;
      }
    }

    .wrap {
      position: absolute;
      right: 40rpx;
      font-size: 32rpx;
    }
  }

  .top-card {
    padding: 25rpx;

    .title {
      font-size: 40rpx;
      font-weight: 700;
    }

    .label {
      color: #999;
      font-size: 30rpx;

      .label-t {
        margin-top: 10rpx;
      }
    }
  }

  .upload-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 25rpx;
    width: 100%;
    box-sizing: border-box;

    .card {
      width: 327rpx;
      height: 200rpx;
      border-radius: 15rpx;
      overflow: hidden;
      position: relative;

      image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .close {
        position: absolute;
        top: 15rpx;
        right: 15rpx;
        color: #fff;
        width: 50rpx;
        height: 50rpx;
        background-color: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        text-align: center;
        line-height: 50rpx;
        font-size: 32rpx;
      }
    }
  }

  .yingye-box {
    padding: 25rpx;

    .top {
      .title {
        font-size: 40rpx;
        font-weight: 700;
      }
    }

    .upload-box-2 {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left {
        width: 150rpx;
        height: 200rpx;
        flex-shrink: 0;
        background-color: #f5f8ff;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        color: #999;
        margin: 15rpx 0;
        margin-right: 20rpx;
        position: relative;

        .close {
          position: absolute;
          top: 15rpx;
          right: 15rpx;
          color: #fff;
          width: 50rpx;
          height: 50rpx;
          background-color: rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          text-align: center;
          line-height: 50rpx;

          .iconfont {
            font-size: 32rpx !important;
          }
        }

        image {
          width: 100%;
          height: 100%;
        }

        text {
          font-size: 25rpx;
        }

        .iconfont {
          font-size: 60rpx;
        }
      }

      .right {
        font-size: 26rpx;

        .top {
          color: #999;
        }

        .bottom {
          color: red;
          height: auto;
        }
      }
    }
  }
}
</style>
