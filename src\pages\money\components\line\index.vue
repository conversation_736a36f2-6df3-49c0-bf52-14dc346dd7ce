<template>
  <view>
    <canvas
      canvas-id="line"
      id="line"
      class="charts"
      @touchend="tap"
      :width="cWidthR"
      :height="cHeightR"
    />
  </view>
</template>

<script setup>
import { reactive, ref, watch } from "vue";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";

import uCharts from "../../sdk/u-charts";

const cWidthR = ref();
const cHeightR = ref();

const uChartsInstance = reactive({});

const props = defineProps({
  dataObj: {
    type: Object,
    required: true,
    default: () => {},
  },
  isUpdate: {
    type: Boolean,
    default: false,
  },
});

useReady(() => {
  // initMultiBarChart();
  // getServerData();
});

watch(
  () => props.isUpdate,
  (v) => {
    if (v) {
      getServerData();
    }
  }
);

const getServerData = () => {
  //模拟从服务器获取数据时的延时
  setTimeout(() => {
    //模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
    let res = {
      // categories: ["2016", "2017", "2018", "2019", "2020", "2021"],
      categories: props.dataObj.dataKey,
      series: [
        {
          name: "收入",
          // data: [35, 36, 31, 33, 13, 34],
          data: props.dataObj.dataValue,
          // linearColor: [
          //   [0, "#1890FF"],
          //   [0.25, "#00B5FF"],
          //   [0.5, "#00D1ED"],
          //   [0.75, "#00E6BB"],
          //   [1, "#90F489"],
          // ],
          // setShadow: [0, 10, 15, "#1890FF"],
        },
      ],
    };
    drawCharts("line", res);
  }, 0);
};

const drawCharts = (id, data) => {
  const ctx = Taro.createCanvasContext(id, this);
  const sysInfo = Taro.getSystemInfoSync();
  let pixelRatio = 1;
  //这里的第一个 750 对应 css .charts 的 width
  let cWidth = (690 / 750) * sysInfo.windowWidth;
  //这里的 500 对应 css .charts 的 height
  let cHeight = (400 / 750) * sysInfo.windowWidth;
  if (Taro.getEnv() === Taro.ENV_TYPE.ALIPAY) {
    pixelRatio = sysInfo.pixelRatio;
    cWidth = cWidth * pixelRatio;
    cHeight = cHeight * pixelRatio;

    cWidthR.value = cWidth;
    cHeightR.value = cHeight;
  }
  uChartsInstance[id] = new uCharts({
    type: "area",
    context: ctx,
    width: cWidth,
    height: cHeight,
    categories: data.categories,
    series: data.series,
    pixelRatio: pixelRatio,
    animation: true,
    timing: "easeOut",
    duration: 1000,
    rotate: false,
    rotateLock: false,
    canvas2d: true,
    background: "#FFFFFF",
    color: [
      "#1352fc",
      "#91CB74",
      "#FAC858",
      "#EE6666",
      "#73C0DE",
      "#3CA272",
      "#FC8452",
      "#9A60B4",
      "#ea7ccc",
    ],
    padding: Taro.getEnv() === Taro.ENV_TYPE.ALIPAY ? [15, 25, 0, 15] :  [15, 15, 0, 15],
    fontSize: 13,
    fontColor: "#666666",
    dataLabel: false,
    dataPointShape: true,
    dataPointShapeType: "solid",
    touchMoveLimit: 60,
    enableScroll: false,
    enableMarkLine: false,
    legend: {
      show: false,
      position: "top",
      float: "center",
      padding: 5,
      margin: 5,
      backgroundColor: "rgba(0,0,0,0)",
      borderColor: "rgba(0,0,0,0)",
      borderWidth: 0,
      fontSize: 13,
      fontColor: "#666666",
      lineHeight: 11,
      hiddenColor: "#CECECE",
      itemGap: 10,
    },
    xAxis: {
      disableGrid: true,
      disabled: false,
      axisLine: true,
      axisLineColor: "#CCCCCC",
      calibration: true,
      fontColor: "#666666",
      fontSize: 11,
      lineHeight: 20,
      marginTop: 0,
      rotateLabel: false,
      rotateAngle: 45,
      itemCount: 5,
      boundaryGap: "justify",
      splitNumber: 5,
      gridColor: "#CCCCCC",
      gridType: "solid",
      dashLength: 4,
      gridEval: 1,
      scrollShow: false,
      scrollAlign: "left",
      scrollColor: "#A6A6A6",
      scrollBackgroundColor: "#EFEBEF",
      title: "",
      titleFontSize: 13,
      titleOffsetY: 0,
      titleOffsetX: 0,
      titleFontColor: "#666666",
      formatter: "",
      labelCount: 1,
    },
    yAxis: {
      gridType: "dash",
      dashLength: 2,
      disabled: false,
      disableGrid: true,
      splitNumber: 5,
      gridColor: "#CCCCCC",
      padding: 10,
      showTitle: false,
      data: [],
    },
    extra: {
      area: {
        type: "curve",
        opacity: 0.9,
        addLine: true,
        width: 1.5,
        gradient: true,
        activeType: "hollow",
      },
      tooltip: {
        showBox: true,
        showArrow: false,
        showCategory: true,
        borderWidth: 0,
        borderRadius: 10,
        borderColor: "#1352FC",
        borderOpacity: 0.8,
        bgColor: "#1352fc",
        bgOpacity: 0.7,
        gridType: "dash",
        dashLength: 4,
        gridColor: "#CCCCCC",
        boxPadding: 3,
        fontSize: 13,
        lineHeight: 20,
        fontColor: "#FFFFFF",
        legendShow: false,
        legendShape: "auto",
        splitLine: true,
        horizentalLine: false,
        xAxisLabel: false,
        yAxisLabel: false,
        labelBgColor: "#FFFFFF",
        labelBgOpacity: 0.7,
        labelFontColor: "#666666",
      },
      markLine: {
        type: "solid",
        dashLength: 4,
        data: [],
      },
    },
  });
};

const tap = (e) => {
  uChartsInstance[e.target.id].touchLegend(e);
  uChartsInstance[e.target.id].showToolTip(e);
};
</script>

<style lang="scss" scoped></style>
