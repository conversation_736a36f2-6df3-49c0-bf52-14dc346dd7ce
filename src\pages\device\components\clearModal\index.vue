<script setup>
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import { watch } from 'vue';
import { useGlobalStore } from "@/stores";
const clearImg = 'https://yimits.oss-cn-beijing.aliyuncs.com/miniProgram/clear.png'

const globalStore = useGlobalStore();

const props = defineProps({
  title: String,
  confirmText: String,
  cancelText: String,
  showCancel: {
    type: Boolean,
    default: false,
  },
  show: {
    type: Boolean,
    required: true,
    default: false,
  },
  maskClose: {
    type: Boolean,
    default: false,
  },
  bodyStyle: {
    type: Object,
    default: () => {},
  },
  cancelStyle: {
    type: Object,
    default: () => {},
  },
  maskStyle: {
    type: Object,
    default: () => {},
  },
  isTagSetting: {
    type: Boolean,
    default: false,
  },
  showClose: {
    type: Boolean,
    default: true,
  },
  price: {
    type: Number,
    default: 0,
  },
  device: {
    type: Object,
    default: () => {},
  }
});

watch(() => props.show, (newVal) => { 
  try {
    if (process.env.TARO_ENV === 'weapp') {
      if(newVal) {
        wx.setPageStyle({
          style: {
            overflow: 'hidden' // ‘auto’
          }
        })
      } else {
        wx.setPageStyle({
          style: {
            overflow: 'auto' // ‘auto’
          }
        })
      }
    } 
  } catch (e) {
    console.log(e);
  }
})

const emit = defineEmits(["confirm", "close"]);

const confirmHandel = () => {
  emit("confirm");
};

const cancelHandel = () => {
  emit("cancel");
  emit("close");
}

const maskHandel = (e) => {
  if(e.mpEvent.target.id != 'mask') return
  if (props.maskClose) {
    emit('close')
  }
}

const toTagAdmin = () => {
  Taro.navigateTo({
    url: "/pages/other/houseTagAdmin/houseTagAdmin",
  });
}
</script>

<template>
  <image
    :src="clearImg"
    mode="scaleToFill"
    style="opacity: 0; position: absolute; top: -1000px; left: -1000px;"
  />
  <view class="y-modal-mask-clear" :style="maskStyle" v-if="props.show" @tap.stop="maskHandel" id="mask">
    <view class="y-modal-container" :style="{...props.bodyStyle}">
      <view class="bg-box">
        <!-- <view class="text">水</view> -->
          <image
            :src="clearImg"
            mode="scaleToFill"
          />
      </view>
      <view class="content">
        <view class="top">  
          <view class="title">当前剩余{{ props.device?.type == 2 ? '水' : '电' }}费</view>
          <view class="con">
            <text class="p">{{ props.price }}</text>
            <text>元</text>
          </view>
        </view>
        <view class="bottom" v-if="props?.device?.created_at >= '2025-03-01 00:00:00'">
          <view>总{{ props.device?.type == 2 ? '水' : '电' }}量也同时清零</view>
          <view>请谨慎操作！</view>
        </view>
      </view>
      <view class="bottom-utils" v-if="props.confirmText !== 'no'" >
        <view class="confirm" v-if="!$slots?.confirm" @tap="confirmHandel"
          >{{ props.confirmText }}
        </view>
        <slot name="confirm" v-else></slot>
        <view class="cancel" :style="props.cancelStyle" v-if="props.showCancel"
          ><text @tap="cancelHandel">{{
            props.cancelText || "取消"
          }}</text></view
        >
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.y-modal-mask-clear {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  .icon-close {
    position: absolute;
    right: 20px;
    top: 0px;
    font-size: 45px;
    font-weight: normal;
  }
  .setting {
    position: absolute;
    left: 20px;
    top: 42px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 168px;
    height: 43px;
    background: #3562e4;
    border-radius: 10px;
    transition: all 0.3s;
    .iconfont {
      margin-right: 10px;
      color: #ffffff;
    }
    view {
      font-size: 30rpx;
      color: #ffffff;
      line-height: 38px;
      text-align: center;
      font-weight: 700;
    }
  }
  // .icon-setting {
  //   font-size: 32px;
  //   color: #000;
  //   // margin-left: 160px;
  //   position: absolute;
  //   right: 85px;
  //   top: 0px;
  // }
  .bg-box {
    position: absolute;
    top: 0;
    left: 20px;
    width: 570px;
    height: 556px;
    z-index: -1;
    .text {
    width: 75rpx;
    height: 80rpx;
    position: absolute;
    left: 16rpx;
    top: 36rpx;
    background-color: #fff;
    color: #2976eb;
    font-size: 67rpx;
    font-weight: 700;
    padding: 0 0 10rpx 0;
    }
    image {
      width: 100%;
      height: 100%;
    }
  }
  .y-modal-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 587px;
    min-height: 760px;
    border-radius: 10px;
    color: #000;
    padding: 20px;
    padding-left: 42px;
    z-index: 1000;
    box-sizing: border-box;
    animation: open 0.3s forwards;
    padding-bottom: 30px;
    background-size: 570px;
    background-repeat: no-repeat;
    background-position-x: 17px;
    @keyframes open {
      0% {
        opacity: 0;
        transform: translate(-50%, -30%);
      }
      100% {
        opacity: 1;
        transform: translate(-50%, -50%);
      }
    }
    .title {
      text-align: center;
      font-size: 35px;
      color: #000;
      height: 80px;
      line-height: 80px;
      font-weight: 700;
    }
    .bottom-utils {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      .confirm {
        width: 539px;
        height: 94px;
        background: #468FFE;
        border-radius: 100%;
        border-radius: 32px;
        text-align: center;
        line-height: 94px;
        color: #fff;
        font-size: 32px;
        margin: 0 auto;
        &:active {
          background-color: blue;
        }
      }
      .cancel {
        color: #fff;
        font-size: 32px;
        text-align: center;
        margin-top: 25px;
      }
    }
    .content {
      .top {
        margin-top: 100px;
        .title {
          width: auto;
          height: 28px;
          font-weight: bold;
          font-size: 29px;
          color: #000000;
          margin-bottom: 35px;
          text-align: left;
        }
        .con {
          font-size: 26px;
          color: #000000;
          .p {
            font-weight: bold;
            font-size: 79px;
            color: #000000;
          }
        }
      }
      .bottom {
        width: 235px;
        height: 70px;
        font-weight: 500;
        font-size: 26px;
        color: #000000;
        margin-top: 175px;
      }
    }
  }
}
</style>
