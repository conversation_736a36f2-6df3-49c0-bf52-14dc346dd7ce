<template>
  <view class="my-cell">
    <view class="my-cell-prefix" :style="{width: prefixWidth + 'rpx'}">
      <text v-if="required" class="txt-required">* </text>
      <slot v-if="$slots.prefix" name="prefix"></slot>
      <text v-else>{{prefix}}</text>
    </view>
    <view class="my-cell-content" :class="'text-' + align">
      <slot name="content"></slot>
    </view>
    <view class="my-cell-suffix" :style="'width:' + suffixWidth" v-if="$slots.suffix"><slot name="suffix"></slot></view>
    <view class="my-cell-suffix" :style="'width:' + suffixWidth" v-if="!$slots.suffix && suffix">{{suffix}}</view>
    <view class="my-cell-arrow" v-if="arrow"><MyIcon icon="icon-arrow-right" width="15rpx" height="28rpx"></MyIcon></view>
  </view>
</template>

<script setup>
  import MyIcon from '../MyIcon'

  const props = defineProps({
    val: {
      type: String,
      required: false,
      default: ''
    },
    placeholder: {
      type: String,
      required:false,
      default: ''
    },
    prefix: {
      type: String,
      required:false,
      default: ''
    },
    prefixWidth: {
      type: String,
      required: false,
      default: '140'
    },
    suffix: {
      type: String,
      required:false,
      default: ''
    },
    suffixWidth: {
      type: String,
      required: false,
      default: '100rpx'
    },
    type: {
      type: String,
      required:false,
      default: 'text'
    },
    arrow: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    align: {
      type: String,
      required: false,
      default: 'left'
    },
  })

  const emit = defineEmits(['change'])

  const onChange = (e) => {
    emit('change', e.detail.value)
  }

</script>
<style>
.my-cell {
  border-bottom: 1px solid #DFDFDF;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 30px;
}
.my-cell-prefix {
  width: 140px;
  text-align: left;
  font-size: 28px;
  padding: 20px 0;
  font-family: OPPOSans;
  font-weight: 500;
}
.txt-required {
  color:  #FF0000;
}
.my-cell-content {
  flex: 1;
  font-size:  28px;
}
.text-left input {
  text-align: left;
}
.text-right input {
  text-align: right;
}
.my-cell-content input:-ms-input-placeholder {
  color: #B6BEC5;
}
.my-cell-suffix {
  text-align: right;
  font-size: 28px;
  color: #B6BEC5;
}
.my-cell-arrow {
  width: 30px;
  text-align: right;
}
.icon-arrow-right {
  width: 15px;
  height: 28px;
}
</style>
