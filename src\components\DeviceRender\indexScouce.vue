<template>
  <view class="device-list2">
    <!-- <view class="no-data">暂无信息</view> -->
    <view
      class="device-item"
      v-for="(device,devIdx) in items"
      :key="device.id"
      :style="{'border-color': device?.signal <= 0 && device?.net_type == 1 ? 'red' : '#1452fd'}"
      @tap.stop="onDetail(device, $event)"
    >
      <view class="flex device-top">
        <!-- <view class="device-icon">
          <MyIcon icon="icon-dianbiao" width="52rpx"></MyIcon>
          <text class="sn-text"> {{ device.sn }}</text>
        </view> -->
        <view
          class="fast-box"
          @tap.stop="fastCheckHandel(device)"
          v-if="device.type != 2 && globalStore.who != 'tenant' || globalStore.moreDetail"
        >
          <image src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fastimg.png" mode="aspectFill" />
          <!-- <text>租期剩余：200天</text> -->
          <text
            class="text"
            v-if="
              calculateDaysBetweenDates(
                new Date(),
                device?.house?.fast_contract?.expired_at
              ) >= 0 &&
              !isNaN(
                calculateDaysBetweenDates(
                  new Date(),
                  device?.house?.fast_contract?.expired_at
                )
              )
            "
            >租期剩余{{
              calculateDaysBetweenDates(
                new Date(),
                device?.house?.fast_contract?.expired_at
              )
            }}天</text
          >
          <text
            class="text"
            style="color: red"
            v-if="
              calculateDaysBetweenDates(
                new Date(),
                device?.house?.fast_contract?.expired_at
              ) < 0 &&
              !isNaN(
                calculateDaysBetweenDates(
                  new Date(),
                  device?.house?.fast_contract?.expired_at
                )
              )
            "
            >租期逾期{{
              Math.abs(
                calculateDaysBetweenDates(
                  new Date(),
                  device?.house?.fast_contract?.expired_at
                )
              )
            }}天</text
          >
        </view>
        <view style="flex: 1">
          <view class="device-name" v-if="globalStore.who == 'tenant' && !globalStore.moreDetail"
            >{{ device?.type == 2 ? '水表': '电表' }}</view
          >
          <view class="device-sn" v-if="globalStore.who == 'tenant' && !globalStore.moreDetail">
            <text v-if="device.net_type === 1">{{ device?.type == 2 ? '4G双模水表号': '4G双模电表号' }}</text
            ><text v-if="device.net_type !== 1">{{ device?.type == 2 ? '蓝牙水表号': '蓝牙电表号' }}</text>:
            {{ device.sn }}
          </view>
        </view>
        <view style="width: auto; display: flex; align-items: center">
          <view
            class="close-box"
            v-if="(device.du || 0) < 0.6 && device.status == 2 && device?.type != 2"
          >
          欠费断电
          </view>
          <view
            class="close-box"
            v-if="(device.du || 0) < 0.01 && device.status == 2 && device?.type == 2"
          >
          欠费断水
          </view>
          <!-- marginRight:device.net_type !== 1 && globalStore.who != 'tenant' ? '12rpx' : device.net_type == 1 && globalStore.who != 'tenant' ? '-12rpx' : device.net_type == 1 && globalStore.who == 'tenant' ? '482rpx' : '559rpx' -->
          <view
            class="device-icon cus"
            :style="{ marginRight: device.net_type !== 1 ? '12rpx' : '-10rpx' }"
          >
            <MyIcon v-if="device?.type != 2" icon="icon-dianbiao" width="52rpx"></MyIcon>
           <image v-else style="width:46rpx;height: 56rpx;" :src="shuiImg" mode="aspectFill"></image>
            <text
              class="sn-text"
              :style="{ marginLeft: device.net_type !== 1 ? '-30rpx' : '5rpx' }"
            >
              {{ device.sn }}</text
            >
          </view>
          <template v-if="device.net_type === 1 && device.signal > 0">
            <text class="label" v-if="device.net_type === 1">4G</text>
            <text class="label" v-if="device.net_type !== 1"></text>
            <MyIcon
              :icon="'signal/' + device.signal_num"
              width="60rpx"
              height="49rpx"
              style="margin-left: 0rpx"
              v-if="device.net_type === 1"
            ></MyIcon>
          </template>
        <!-- 4g无信号显示异常提示 -->
        <text class="net-type-wraing" v-if="device.net_type == 1 && device.signal <= 0">
          设备离线
        </text>
        </view>
      </view>
      <view class="device-info flex flex-space-between flex-v-center">
        <view>
          <view class="device-num"
            ><text class="du-t">{{ Number(device?.total || 0).toFixed(2) }}</text>
            <text>{{ device?.type == 2 ? 'm³': '度' }}</text></view
          >
          <view class="device-lab">{{ device?.type == 2 ? '总水量': '总电量' }}</view>
        </view>
        <view :class="{red: (device?.type == 5 || device?.type == 6) && device?.pay_mode == 2}">
          <view class="device-num"
            ><text class="du-t">{{ device.du || 0 }}</text
            ><text></text></view
          >
          <view class="device-lab">{{ device?.type == 2 ? '剩余水量(m³)': itemText(device) }}</view>
        </view>
        <view v-if="device?.type == 5 || device?.type == 6" @tap.stop="priceShow = true;devIdxVal = devIdx">
          <view class="device-num">查询</view>
          <view class="device-lab">分段电价</view>
        </view>
        <view v-else>
          <view class="device-num">{{ device.price }}<text>{{ device?.type == 2 ? '元/m³': '元/度' }}</text></view>
          <view class="device-lab">单价</view>
        </view>
      </view>
      <view style="padding: 10rpx 20rpx 0 20rpx" class="btm-b">
        <view class="le">
          <!-- <MyIcon icon="icon-house-mini" width="30rpx" height="26rpx"></MyIcon> -->
          <image
            src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
            mode="scaleToFill"
            class="icon-s"
          />
          <text @tap.stop="editHoseName(device)">
            <text
              class="text-v-center font-28 _name"
              v-if="device.house || globalStore.moreDetail"
              >{{ device.house?.name || globalStore.moreDetail?.house_name }}</text
            >
            <text
              v-if="device.house?.name && !globalStore.moreDetail && globalStore.who != 'tenant'"
              class="iconfont icon-bianji"
              style="
                font-size: 25rpx;
                display: inline-block;
                margin: 0 0 0 10rpx;
                transform: translateY(-6rpx);
                font-weight: 400;
                color: #333;
              "
            ></text>
          </text>
          <text
            class="text-v-center font-28 _name"
            v-if="!device.house && !globalStore.moreDetail"
            >未绑定房源</text
          >
        </view>
        <view class="recharge-tip" v-if="device.online_status != 2 && getDaysDifference(device?.net_type == 1 ? (device?.recharge_callback_at || '') : (device?.read_at || '')) <= (device.type == 2 ? rechareWaterDay : rechareDay) ">
              {{ Math.abs(getDaysDifference(device?.net_type == 1 ? (device?.recharge_callback_at || '') : (device?.read_at || ''))) }}天未充{{ device.type == 2 ? '水' : '电' }}费！
          </view>
        <view
          class="haodian"
          :data-ignore="true"
          v-if="device.net_type === 1 && device.signal_num > 0"
          @tap.stop="toHaoDianDetailHandel(device)"
        >
          <image
            class="ico-l"
            src="https://yimits.oss-cn-beijing.aliyuncs.com/images/haodian.png"
            mode="scaleToFill"
            v-if="device.type != 2"
          />
          <text>{{ device?.type == 2 ? '耗水记录' : '耗电记录'}} </text>
          <image
            class="ico-r"
            src="https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-right-white.png"
            mode="scaleToFill"
          />
        </view>
      </view>
    </view>
    <QueryPricesPopUp :device="items[devIdxVal]" :show="priceShow" @close="priceShow = false" />
  </view>
</template>

<script setup>
import MyIcon from "@/components/MyIcon";
import Taro from "@tarojs/taro";
import request from "@/utils/request";
import { ref } from "vue";
import { getDaysDifference } from '@/utils/index.js'
import shuiImg from '@/assets/water/device.png'
import QueryPricesPopUp from '@/components/queryPricesPopUp'

import { useGlobalStore } from "@/stores";
const globalStore = useGlobalStore();

const rechareDay = ref(-10)//未充值10天
const rechareWaterDay = ref(-30)//未充值30天
const priceShow = ref(false);

const devIdxVal = ref(0);

const props = defineProps({
  items: {
    type: Array,
    required: false,
    default() {
      return [];
    },
  },
});

function calculateDaysBetweenDates (date1, date2) {
    if(!date1 || !date2) return NaN
  // 将日期转换为当天的 00:00:00
  const d1 = new Date(date1);
  d1.setHours(0, 0, 0, 0);
  
  const d2 = new Date(date2);
  d2.setHours(0, 0, 0, 0);

  const dayInMs = 24 * 60 * 60 * 1000;
  const diffTime = d2.getTime() - d1.getTime();
  const diffDays = Math.round(diffTime / dayInMs);
  
  return diffDays;
} 

const emit = defineEmits(["detail"]);

const editHoseName = (item) => {
  if(globalStore.who == "tenant") return
  emit("editHoseName", item);
};

const onDetail = (item, e) => {
  emit("detail", item.id);
};

const device = ref();

const itemText = (dev) => {
  if (dev?.type == 5 || dev?.type == 6) {
    if (dev?.pay_mode == 1) {
      // 预付费
      return '剩余金额(元)'
    } else {
      //后付费
      return '已用金额(元)'
    }
  } else {
    return '剩余电量(度)'
  }
}

// 跳转快捷入驻
const fastCheckHandel = (item) => {
  request
    .get({
      url: "device/" + item.id,
    })
    .then((res) => {
      device.value = res.data;
      const obj = {
        id: device.value.id,
        need_people: device.value.need_people,
        sn: device.value.sn,
      };
      Taro.navigateTo({
        url: "/pages/device/fastCheck/fastCheck?deveice=" + JSON.stringify(obj),
      });
    });
};

// 跳转到耗电记录
const toHaoDianDetailHandel = (item) => {
  console.log(item);
  Taro.navigateTo({
    url: "/pages/device/consumption-record/consumption-record?id=" + item.id + '&type=' + item?.type,
  });
};
</script>
<style lang="scss">
.device-list2 .device-info .red {
  .device-num .du-t {
    color: red !important;
  }
  >view {
    color: red !important;
    text{
      color: red !important;
    }
  }
}
.device-list2 {
  padding: 0 23px;

  .device-item {
    background: #ffffff;
    box-shadow: 0rpx 4rpx 32rpx 0rpx rgba(182, 190, 201, 0.38);
    border-radius: 20rpx;
    border: 1px solid #1452fd;
    padding: 15px;
    margin-top: 23px;
    .close-box {
      font-size: 24px;
      color: #f2811a;
      background-color: #fce9d1;
      padding: 5px 10px;
      margin-right: 10px;
      border-radius: 6px;
    }

    .net-type-wraing {
      font-size: 22px;
      color: red;
      display: inline-block;
      font-weight: 700;
      width: 50px;
      margin-left: 20px;
    }

    .house-box-title {
      font-size: 36rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
      line-height: 31rpx;
    }
  }
  .device-top {
    margin-bottom: 7px;

    .device-name {
      font-size: 28rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .device-sn {
      font-size: 24rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #000000;
    }
    .recharge-tip {
        font-size: 24px;
        color: red;
        font-weight: 700;
        // color: #f2811a;
        // background-color: red;
        // padding: 5px 10px;
        // border-radius: 6px;
      }
    .device-icon {
      width: 60px;
      height: 60px;
      background: #e9effa;
      border-radius: 10px;
      margin-right: 12px;
      text-align: center;
      // padding: 2px 0;
      .my-icon {
        margin-top: -10px;
      }
      .sn-text {
        font-size: 16px;
        font-weight: 700;
        margin-top: 10px;
        display: block;
        margin-left: 5px;
      }
      &.cus {
        position: relative;
        width: 88px;
        height: 72px;
        .sn-text {
          position: absolute;
          left: 50%;
          bottom: 3px;
          font-size: 12px;
          transform: translateX(-50%);
          margin-left: 0 !important;
        }
      }
    }
    .fast-box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      // align-items: center;
      // margin-left: 80px;
      image {
        width: 125px;
        height: 90px;
      }
      text {
        color: #000;
        font-size: 20px;
        font-weight: 700;
        // margin-top: -10px;
      }
    }
    .label {
      font-weight: 700;
      font-size: 15px;
      display: inline-block;
      transform: translate(22px, -15px);
    }
  }
  .device-info {
    background: #f0f4ff;
    border-radius: 20px;
    padding: 10px 25px;
    text-align: center;
    position: relative;
    .net-type-wraing {
      position: absolute;
      right: 35px;
      top: 0px;
      font-size: 20px;
      color: red;
      font-weight: 700;
    }
    > view {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 175px;
      height: 120px;
      background: #f0f4ff;
      border-radius: 20px;
    }

    .device-num {
      font-size: 40rpx;
      color: #1452fd;
      font-family: Bahnschrift;
      font-weight: 400;
      margin-bottom: 10px;
      font-weight: 700;
      display: flex;
      align-items: center;
      .du-t {
        max-width: 150px;
        overflow: hidden;
        display: inline-block;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #1452fd !important;
        font-size: 40px !important;
        margin-top: 0 !important;
      }
      text {
        font-weight: 500;
        font-size: 18px;
        color: #4460b3;
        margin-top: 5px;
      }
    }
    .device-lab {
      font-size: 26rpx;
      font-family: OPPOSans;
      font-weight: 500;
      color: #879bd6;
    }
  }

  // -------------
  .btm-b {
    display: flex;
    align-items: center;
    justify-content: space-between;
    ._name {
      font-size: 32px;
      font-weight: 700;
      margin-left: 5px;
      vertical-align: middle !important;
    }
    .icon-s {
      width: 32px;
      height: 32px;
      vertical-align: middle;
    }
    .text-v-center {
      flex-shrink: 0;
      vertical-align: bottom;
    }
    .recharge-tip {
        font-size: 24px;
        color: red;
        font-weight: 700;
        // color: #f2811a;
        // background-color: red;
        // padding: 5px 10px;
        // border-radius: 6px;
      }
    .haodian {
      flex-shrink: 0;

      width: 160px;
      height: 46px;
      background: linear-gradient(90deg, #2a69f6 0%, #519bfd 100%);
      font-size: 20px;
      color: #fff;
      border-radius: 5px;
      line-height: 46px;
      text-align: center;
      .ico-l {
        width: 16px;
        height: 23px;
        vertical-align: middle;
        margin-right: 5px;
      }
      .ico-r {
        width: 9px;
        height: 15px;
        margin-left: 15px;
      }
    }
  }
}
</style>
