<template>
    <view class="rr-amount">
      <view class="rr-a-t">待充值金额 (元)</view>
      <view class="rr-a-n">{{order.pay_amt}}</view>
    </view>
    <!-- <view class="rr-item flex flex-space-between">
        <view class="rr-item-label">到账电量</view>
        <view class="rr-item-value">{{order.du}}度</view>
    </view>
    <view class="rr-item flex flex-space-between">
        <view class="rr-item-label">电量单价</view>
        <view class="rr-item-value">{{device.price}}元/度</view>
    </view> -->
    <view class="rr-item flex flex-space-between">
        <view class="rr-item-label">实际付款</view>
        <view class="rr-item-value">0元</view>
    </view>
    <view class="rr-btm">
        <view><button class="btn-primary" @tap="onRecharge">立即充值</button></view>
        <view class="p20 text-center">
            <image @tap="onChangePrivacy(1)" v-if="!privacy" :src="iconRadio" class="icon-radio"></image>
                <image @tap="onChangePrivacy(0)" v-if="privacy" :src="iconRadioChecked" class="icon-radio"></image>
            <text @tap="onChangePrivacy(1)">我已阅读并同意</text> <text class="link" @tap="onJump('/pages/article/article?key=recharge')">《充值协议》</text>
        </view>
        <GlobDialog />
    </view>
  </template>

  <script setup>
  import {computed, ref} from 'vue'
    import Taro, { useDidShow, useDidHide,useUnload, useReady, useLoad, usePullDownRefresh, useReachBottom } from '@tarojs/taro'
    import request from '@/utils/request'
    import MyIcon from '@/components/MyIcon'

    const iconRadio = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-radio.png'
    const iconRadioChecked = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-radio-checked.png'

    import deviceConnect from '@/utils/deviceConnect'

    import GlobDialog from '@/components/globDialog/index.vue'


    import { useGlobalStore } from '@/stores'
    const globalStore = useGlobalStore()

    definePageConfig({
      navigationBarTitleText: "充值"
    });

    const pageIsAlive = ref(true)

    const timerSetOrder = ref(null)
    const timerSetOrderFour = ref(null)
    const timerSetOrderBle = ref(null)


    useDidHide(() => {
      pageIsAlive.value = false
      clearInterval(timerSetOrder.value)
      clearInterval(timerSetOrderFour.value)
      clearInterval(timerSetOrderBle.value)
      // globalStore.setGlobDialog({show:false,type:''})
      // globalStore.setDeviceTime(15)
      // globalStore.setDeviceRefTime(30)
    })

    useUnload(() => {
      pageIsAlive.value = false
      clearInterval(timerSetOrder.value)
      clearInterval(timerSetOrderFour.value)
      clearInterval(timerSetOrderBle.value)
      globalStore.setGlobDialog({show:false,type:''})
      globalStore.setDeviceTime(15)
      globalStore.setDeviceRefTime(30)
    })

    const id = ref(0)
    const order = ref({})
    const device = ref({})

    const privacy = ref(true)
    const onChangePrivacy = (val) => {
        privacy.value = val
    }

    useLoad((option) => {
      id.value = option.id
      fetch()
    })

    useDidShow(() => {
      // globalStore.setGlobDialog({show:false,type:''})
        // fetch()
        pageIsAlive.value = true
    })

  /**
   * 检查当前度数是否大于上次充值失败的度数
   * @returns {Promise<boolean>} true:大于 false:小于等于
   * @description 如果大于则认为充值成功了不在进行补充
   */
  const checkLastFailOrder = (orderNo,failOrder) => {
    return new Promise(async (resolve, reject) => {
      try {
        if (device.value?.net_type == 2) {
          console.log('读取最新度数');
          deviceConnect.getConnection()
            .then(() => {
              deviceConnect.queryStatus()
          })
          .catch(() => {
              if (pageIsAlive.value) {
                globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
                globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
              }
          })
          await listenDeviceReadBleFileOrder(true)
          console.log('读取最新度数成功');
        }else {
          // 在线表不处理直接返回false
          return resolve(false)
        }
        // 比较上次充值失败的度数
        // const lastFailInfo = Taro.getStorageSync('rechargeFailDuInfo') ? JSON.parse(Taro.getStorageSync('rechargeFailDuInfo')) : null//度数上次充值失败的信息
        if (failOrder && failOrder.device.sn == device.value.sn) {
          if (Number(device.value?.du) > (Number((Number(failOrder.du) * 0.1).toFixed(2)) + Number(failOrder.device_du)) || (Number(device.value.total) >= (Number(failOrder.device_total) + (Number(failOrder.du) * 0.1) + Number(failOrder.device_du)))) {
            // TODO 清楚失败订单
            handleRechargeSuccess(orderNo)
            resolve(true)
          } else {
            resolve(false)
          }
        } else {
          resolve(false)
        }
      } catch {
        resolve(false)
      }
    })
  }

    const fetch = () => {
        request.get({
            url: 'order/fail',
            data: {
                deviceId: id.value
            }
        }).then(res => {
            order.value = res.data.fail
            if (res.data.fail) {
                device.value = res.data.fail.device
                rechargeFailSetDuInfo({
                  du: device.value?.du,
                  net_type: device.value?.net_type,
                  sn: device.value?.sn,
                  id: device.value?.id
                })
            }
            if (!res.data.fail) {
                Taro.showModal({
                    title: '警告',
                    content: '您没有充值失败的订单',
                    showCancel: false,
                    success: function (res) {
                        if (res.confirm) {
                            Taro.navigateBack()
                        }
                    }
                })
            }
        })
    }

    const checkOrder = () => {
        return new Promise((resolve, reject) => {
            request.get({
                url: 'order/fail',
                data: {
                    deviceId: id.value
                }
            }).then(res => {
                if (res.data.fail && res.data.fail.id === order.value.id) {
                    resolve()
                }
                reject()
            })
        })
    }

    usePullDownRefresh(() => {
      // fetch()
    })

    let countdown = null;
    let coutTimes = 30

    const handleRechargeOrder = () => {
        request.post({
            url: 'order/tryRecharge',
            data: {
                orderNo: order.value.order_no
            }
        }).then(async _=> {
            // 请求充值
            // Taro.showLoading({
            //     title: '正在充值...',
            //     mask: true
          // })
          globalStore.setDeviceRefTime(30)
          globalStore.setGlobDialog({show:true,type:'TimeReFLoading'})
            countdown = setInterval(function() {
              // coutTimes--
              globalStore.setDeviceRefTime(globalStore.deviceRefTime-1)
              if (globalStore.deviceRefTime <= 0) {
                clearInterval(countdown);
                globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                // coutTimes=30
                globalStore.setDeviceRefTime(30)
                globalStore.setGlobDialog({show:true,type:'RefFailed'})
              } else {
                // Taro.showLoading({
                //   title: `充值剩余${(coutTimes).toFixed(0)}秒`,
                //   mask: true
                // });
              }
            }, 1000);
            let new_du = 0
           if (device.value.net_type != 1) {
            // 获取蓝牙设备最新度
            deviceConnect.queryStatus()
              // Taro.showLoading({
              //   title: '正在读表中...',
              //   icon: 'none',
              //   mask: true
              // })
             new_du = await listenDeviceReadBleFileOrder()
            }
          deviceConnect.recharge2((Number(new_du) + Number(order.value.du)).toFixed(2), order.value.order_no,true).then(_ => {
                // 充值超时提示用户
                rechangeTime.value = setTimeout(() => {
                    Taro.hideLoading()
                    // Taro.showToast({
                    //   title: '充值响应超时',
                    //   icon: 'error'
                    // })
                    globalStore.setGlobDialog({show:true,type:'RefFailed'})
                    clearInterval(countdown);
                    // coutTimes=30
                    globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                    globalStore.setDeviceRefTime(30)
                    handleRechargeFailOrder(order.value.order_no)
                }, 1000 * 30)

                if (device.value.net_type === 1) {
                    listenDeviceRechargeOrder().then(() => {
                    clearTimeout(rechangeTime.value)
                    clearInterval(countdown);
                    // coutTimes=30
                    globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                    globalStore.setDeviceRefTime(30)
                    }).catch(() => {
                        handleRechargeFailOrder(order.value.order_no)
                        clearTimeout(rechangeTime.value)
                        clearInterval(countdown);
                        // coutTimes=30
                        globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                        globalStore.setDeviceRefTime(30)
                    })
                } else {
                    listenDeviceRechargeBleFileOrder().then(() => {
                    clearTimeout(rechangeTime.value)
                    clearInterval(countdown);
                    // coutTimes=30
                    globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                    globalStore.setDeviceRefTime(30)
                    }).catch(() => {
                        clearTimeout(rechangeTime.value)
                        clearInterval(countdown);
                        // coutTimes=30
                        globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                        globalStore.setDeviceRefTime(30)
                        handleRechargeFailOrder(order.value.order_no)
                    })
                }

          }).catch(_ => {
              console.log('****errrrrrrr*****');
              
                Taro.hideLoading()
                clearInterval(countdown);
                // coutTimes=30
                globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                globalStore.setDeviceRefTime(30)
                  // Taro.showToast({
                  //   title: '充值失败',
                  //   icon: 'error'
                  // })
                  // globalStore.setGlobDialog({show:true,type:'RefFailed'})
                  handleRechargeFailOrder(order.value.order_no)
            })

        })
    }

    const isTap = ref(false)
    const onRecharge = async() => {
      if (isTap.value) {
          Taro.showToast({
              title: '操作过于频繁，请稍后再试',
              icon: 'none'
          })
          return
      }
      isTap.value = true
      const isTrue = await checkLastFailOrder(order.value.order_no,order.value)
      if (isTrue) {
        Taro.showModal({
          title: '检测到充值已经成功请返回查看!',
          showCancel: false,
          success: function (res) {
            Taro.navigateBack()
          }
        })
        return
      }


        if (order.value.device.net_type !== 1) {
            deviceConnect.getConnection().then(_=> {
              handleRechargeOrder()
            }).catch(() => {
              console.log('----------------------我说阿斯达克警方哈桑翻江倒海------');
              if (pageIsAlive.value) {
                globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
                globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
                  // Taro.showModal({
                  //   title: '提示',
                  //   content: '抱歉！电表连接失败啦，请确保与电表保持3米距离重启小程序进行操作！',
                  //   showCancel:true,
                  //   cancelText: '查看教程',
                  //   confirmText: '我知道了',
                  //   success: function (res) {
                  //     if (res.confirm) {
                  //       console.log('用户点击我知道了')
                  //     } else if (res.cancel) {
                  //       console.log('用户点击查看使用教程')
                  //       Taro.navigateTo({
                  //         url: '/pages/qa/index'
                  //       })
                  //     }
                  //   }
                  // })
                }
            })
        } else {
          handleRechargeOrder()
        }

        setTimeout(() => {
            isTap.value = false
        }, 5000)
    }

    const rechangeTime = ref(null)

  const listenDeviceRechargeOrder = () => {
    let s = 0
    s=0
    return new Promise((resolve, reject) => {
      timerSetOrderFour.value = setInterval(() => {
        s ++

        request.get({
          url: 'device/' + id.value,
          showLoading: false
        }).then(res => {
          if (Number(res.data.du) > Number(device.value.du)) {
            handleRechargeSuccess(order.value.order_no).then(_=> {
              device.value.du = res.data.du
              Taro.hideLoading()
              clearInterval(countdown);
              // coutTimes=30
              globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
              globalStore.setDeviceRefTime(30)
              clearInterval(timerSetOrderFour.value)
              s=0
              Taro.showToast({
                title: '充值成功',
                duration: 3000
              })
              globalStore.homeHasChange = true
              resolve()
            })
          }
          if (s >= 30) {
            s=0
            Taro.hideLoading()
            clearInterval(countdown);
            //  coutTimes=30
            globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
            globalStore.setDeviceRefTime(30)
            clearInterval(timerSetOrderFour.value)
            globalStore.setGlobDialog({show:true,type:'RefFailed'})
            // Taro.showToast({
            //   title: '充值失败',
            //   icon: 'error',
            //   duration: 2000
            // })
            reject()
          }
        })

      }, 3000)
    })
  }

  const listenDeviceRechargeBleFileOrder = () => {
    let s = 0
    s = 0
    return new Promise((resolve, reject) => {
      timerSetOrder.value = setInterval(() => {
        s ++

        if (globalStore.device.message.length > 0) {
          let msg = globalStore.device.message.shift()
          console.log('---------msg,mac---------');
          console.log(msg, device.value.mac)
          console.log('---------msg,mac---------');
          if (msg.mac === device.value.mac) {
            if (msg.event === '0A' && Number(msg.du) > Number(device.value.du)) {
              s = 0
              device.value.du = msg.du
              let postData = {
                du: msg.du,
                info: globalStore.who,
                log: 1
              }
              request.post({
                url: 'device/' + id.value + '/read',
                showLoading: false,
                data: postData
              }).then(_=> {
                // setTimeout(() => {
                //   Taro.navigateBack()
                // }, 3000)
                handleRechargeSuccess(order.value.order_no).then(_=> {
                  Taro.hideLoading()
                  clearInterval(timerSetOrder.value)
                  clearInterval(countdown);
                  //  coutTimes=30
                  globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
                  globalStore.setDeviceRefTime(30)
                  Taro.showToast({
                    title: '充值成功',
                    duration: 3000
                  })
                  globalStore.homeHasChange = true
                  setTimeout(() => {
                    // Taro.navigateBack()
                    Taro.reLaunch({
                      url: '/pages/index/index'
                    })
                  }, 3000)
                  resolve()
                })

              }).catch(() => {
                s = 0
              })
            }

            if (s >= 31) {
              s = 0
              Taro.hideLoading()
              clearInterval(countdown);
              // coutTimes=30
              globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
              globalStore.setDeviceRefTime(30)
              clearInterval(timerSetOrder.value)
              Taro.showToast({
                title: '操作超时',
                icon: "none",
                duration: 2000
              })
              reject()
            }
          }
        }

        // if (s >= 15) {
        //   Taro.hideLoading()
        //   clearInterval(timer)
        //   Taro.showToast({
        //     title: '操作超时',
        //     icon: 'error',
        //     duration: 2000
        //   })
        //   reject()
        // }

      }, 3000)
    })
  }

  const handleRechargeSuccess = (orderNo) => {
    return new Promise((resolve, reject) => {
      request.post({
        url: 'order/success',
        data: {
          orderNo,
          description: device.value?.net_type == 2 ? '蓝牙表充值成功' : undefined
        },
        showLoading: false
      }).then(_=> {
        resolve()
      }).catch(_=> {
        reject()
      })
    })
  }

  // 拿到最新蓝牙设备的du
  const listenDeviceReadBleFileOrder = (bol) => {
    let s = 0
    return new Promise((resolve, reject) => {
      timerSetOrderBle.value = setInterval(() => {
        s ++

        if (globalStore.device.message.length > 0) {
          let msg = globalStore.device.message.shift()
          console.log(msg, device.value.mac)
          if (msg.mac === device.value.mac) {
            if (msg.event === '0C') {
              if (bol) {
                device.value.du = msg.du
              }
              resolve(msg.du)
              Taro.hideLoading()
              clearInterval(timerSetOrderBle.value)
              // clearInterval(countdown);
              //  coutTimes=30
              // globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
              // globalStore.setDeviceRefTime(30)
            }

            if (s >= 15) {
              Taro.hideLoading()
              // clearInterval(countdown);
              // coutTimes=30
              // globalStore.setGlobDialog({show:false,type:'TimeReFLoading'})
              // globalStore.setDeviceRefTime(30)
              clearInterval(timerSetOrderBle.value)
              // Taro.showToast({
              //   title: '操作超时',
              //   icon: 'warning',
              //   duration: 2000
              // })
              reject()
            }
          }
        }

      }, 1000)
    })
    }

    // const handleRechargeFailOrder = (orderNo) => {
    //     request.post({
    //     url: 'order/fail',
    //     data: {
    //         orderNo
    //     },
    //     showLoading: false
    //     })
    // }

    // 获取设备信息
    const getDevInfo = (sn) => {
      return new Promise((resolve, reject) => {
        let opt = {
          url: 'device/' + id.value
        }
        if (sn) {
          opt = {
            url: 'device/search',
            data: {
              sn
            },
            showLoading: false,
            showToast:false
          }
        }
        request.get(opt).then(res => {
          // device.value = res.data
          resolve(res.data)
        })
      })
  }

    // 充值失败，存储失败的度数
    const rechargeFailSetDuInfo = (info) => {
      Taro.setStorageSync('rechargeFailDuInfo', JSON.stringify(info))
    }

    const handleRechargeFailOrder = async(orderNo) => {
      const info = await getDevInfo(device.value.sn)//获取最新的du
      if (info?.net_type == 1) {
        // 最新度数发生变化了 说明充值成功了 不上报错误
        if (Number(info?.du) > Number(device.value.du)) {
          return
        }
        // 4g
        request.post({
          url: 'order/fail',
          data: {
            orderNo,
            before: device.value.du,
            after:info?.du
          },
          showLoading: false,
          showToast:false
        })
        console.log('4G表充值失败：');
        console.log("orderNo:"+orderNo);
        console.log("before:"+device.value.du);
        console.log("after:" + info?.du);
        globalStore.setGlobDialog({show:true,type:'RefFailed'})
        rechargeFailSetDuInfo({
          du: info?.du,
          net_type: device.value?.net_type,
          sn: device.value?.sn,
          id: device.value?.id
        })
      } else {
        // 获取蓝牙设备最新度
        deviceConnect.queryStatus()
          Taro.showLoading({
            title: '正在读表中...',
            icon: 'none',
            mask: true
          })
        listenDeviceReadBleFileOrder()
          .then((du) => {
            if (Number(du) > Number(device.value.du)) {
              handleRechargeSuccess(orderNo)
              request.post({
                url: 'device/' + id.value + '/read',
                showLoading: false,
                showToast:false,
                data: {
                  du: device.value.du,
                  info: globalStore.who,
                  log: 1
                }
              })
              return
            }
            console.log('蓝牙表充值失败：');
            console.log("orderNo:"+orderNo);
            console.log("before:"+device.value.du);
            console.log("after:" + du);
            globalStore.setGlobDialog({show:true,type:'RefFailed'})
            rechargeFailSetDuInfo({
              du,
              net_type: device.value?.net_type,
              sn: device.value?.sn,
              id: device.value?.id
            })

            request.post({
              url: 'order/fail',
              data: {
                orderNo,
                before: device.value.du,
                after:du
              },
              showLoading: false,
              showToast:false
            })
            // request.post({
            //   url: 'order/success',
            //   data: {
            //     orderNo,
            //     description:`蓝牙表充值失败`,
            //   },
            //   showLoading: false
            // })
        }).catch(() => {
          rechargeFailSetDuInfo({
              du: device.value.du,
              net_type: device.value?.net_type,
              sn: device.value?.sn,
              id: device.value?.id
          })
          request.post({
              url: 'order/fail',
              data: {
                orderNo,
                before: device.value.du,
                after: device.value.du
              },
              showLoading: false,
              showToast:false
            })
            // request.post({
            //   url: 'order/success',
            //   data: {
            //     orderNo,
            //     description:`蓝牙表充值失败`,
            //   },
            //   showLoading: false
            // })
        })
      }

  }

    const onJump = (path) => {
        Taro.navigateTo({
        url: path
        })
    }

    const playAudio = () => {
        const innerAudioContext = Taro.createInnerAudioContext()
        innerAudioContext.autoplay = true
        innerAudioContext.src = 'https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/audio/cztx.wav'
        innerAudioContext.onPlay(() => {
        console.log('开始播放')
        })
    }


  </script>
  <style lang="scss">
  .rr-amount {
    margin: 26px;
    height: 300px;
    background: #1352FD;
    box-shadow: 0 4px 32px 0px rgba(19,82,253,0.38);
    border-radius: 25px;
    text-align: center;

    .rr-a-t {
        padding-top: 84px;
        font-size: 30px;
        font-weight: 500;
        color: #8EACFF;
        line-height: 31px;
    }
    .rr-a-n {
        padding-top: 40px;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 31px;
        font-size: 88rpx;
    }
  }
  .rr-item {
    margin: 26px;
    padding: 51px 42px;
    background: #FFFFFF;
    box-shadow: 0rpx 4rpx 32rpx 0rpx rgba(182,190,201,0.38);
    border-radius: 25rpx;
    .rr-item-lable {
        font-size: 30px;
        font-weight: 500;
        color: #000000;
        line-height: 31px;
    }
    .rr-item-value {
        font-size: 30px;
        font-weight: 500;
        color: #9B9B9B;
        line-height: 31px;
    }
  }
  .rr-btm {
    padding: 37px 25px 84px 25px;
    box-sizing: border-box;
    background: #FFFFFF;
    box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(54,69,193,0.24);
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
  }
  .icon-radio {
    width: 26px;
    height: 26px;
    vertical-align: middle;
    margin-right: 10px;
    }
  </style>
