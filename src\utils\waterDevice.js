import Taro from '@tarojs/taro'
import { useGlobalStore } from '@/stores'
const globalStore = useGlobalStore()


// TODO searchDevice 调用

// 校验权限 微信版
const checkScope = () => {
  return new Promise((resolve, reject) => {
    console.log('getSetting')
    Taro.getSetting({
      success: function (res) {
        console.log('getSetting success', res)
        if (!res.authSetting['scope.bluetooth']) {
          Taro.authorize({
            scope: 'scope.bluetooth',
            success: () => {
              if (!res.authSetting['scope.userLocation']) {
                Taro.authorize({
                  scope: 'scope.userLocation',
                  success: () => {
                    resolve()
                  }
                })
              } else {
                resolve()
              }
            },
            fail: (e) => {
              console.log('authorize failed', e)
            }
          })
        } else {
          if (!res.authSetting['scope.userLocation']) {
            Taro.authorize({
              scope: 'scope.userLocation',
              success: () => {
                resolve()
              },
              fail: (e) => {
                console.log('authorize failed', e)
              }
            })
          } else {
            resolve()
          }
        }
      },
      fail: (e) => {
        console.log(e, 'getSetting failed')
        reject(e)
      }
    })
  })
}

const checkScopeAlipay = () => {
  const systemInfo = my.getSystemInfoSync()
  console.log(systemInfo, 'systemInfo')
  if (!systemInfo.locationEnabled) {
    Taro.showToast({
      title: '手机定位未打开',
      icon: 'none',
      duration: 2000
    })
    return
  }
  if (typeof systemInfo.bluetoothEnabled !== 'undefined' && !systemInfo.bluetoothEnabled) {
    Taro.showToast({
      title: '手机蓝牙未打开',
      icon: 'none',
      duration: 2000
    })
    return
  }
  return _checkScopeAlipay(systemInfo.locationAuthorized)
}

const _checkScopeAlipay = (locationAuthorized) => {
  return new Promise((resolve, reject) => {
    console.log('alipay getSetting')
    Taro.getSetting({
      success: function (res) {
        console.log('alipay getSetting success', res)
        if (!res.authSetting['bluetooth']) {
          my.showAuthGuide({
            authType: 'BLUETOOTH',
            success: () => {
              if (!locationAuthorized) {
                my.showAuthGuide({
                  authType: 'LBS',
                  success: () => {
                    resolve()
                  },
                  fail: (e) => {
                    console.log('authorize failed', e)
                    reject()
                  }
                })
              } else {
                resolve()
              }
            },
            fail: (e) => {
              console.log('authorize failed', e)
              reject()
            }
          })
        } else {
          if (!locationAuthorized) {
            my.showAuthGuide({
              authType: 'LBS',
              success: () => {
                resolve()
              },
              fail: (e) => {
                console.log('authorize failed', e)
                reject()
              }
            })
          } else {
            resolve()
          }
        }
      },
      fail: (e) => {
        console.log(e, 'getSetting failed')
        reject(e)
      }
    })
  })
}


/**
 * 检查蓝牙权限
 */
const checkScopeHandel = () => {
  return new Promise((resolve, reject) => {
    if (process.env.TARO_ENV === 'weapp') {
      checkScope().then(_ => {
        console.log('checkScope')
        resolve()
      }).catch(() => {
        globalStore.setGlobDialog({ show: true, type: 'PermissionDenied' })
        reject()
      })
    } else {
      checkScopeAlipay().then(_ => {
        console.log('checkScope')
        resolve()
      }).catch(() => {
        globalStore.setGlobDialog({ show: true, type: 'PermissionDenied' })
        reject()
      })
    }
  })
}

// 搜索设备
export const searchDevice = () => {
  return new Promise(async (resolve, reject) => {
    try {
      await checkScopeHandel();
    } catch (e) {
      reject()
    }
    Taro.openBluetoothAdapter({
      success: () => {
        setTimeout(() => {
          Taro.startBluetoothDevicesDiscovery({
            allowDuplicatesKey: false,
            success: (res) => {
              console.log(res, "startBluetoothDevicesDiscovery");
              Taro.onBluetoothDeviceFound((data) => {

                data.devices.forEach(item => {
                  if (item.name.includes('B000') || item.name.includes('QJ00')) {
                    console.log(data, "onBluetoothDeviceFound找到设备了");
                    // 链接设备
                    connectDevice(item.deviceId)
                    resolve(data.devices)
                    Taro.stopBluetoothDevicesDiscovery()
                  }
                })
              })
              // resolve()
            },
            fail: (err) => {
              console.log(err);
              if (err.errno == 1500104) {
                Taro.showModal({
                  title: "连接失败，频繁操作请重试！",
                  showCancel: false,
                  success () {
                  }
                })
              }
            }
          })
        }, 1200)
      },
      fail: (err) => {
        reject(err)
        globalStore.setGlobDialog({ show: true, type: 'PermissionDenied' })
        console.log('----初始化蓝牙失败----', err);
        if (err.errCode == 10001) {
          // Taro.showModal({
          //   title: "请打开蓝牙",
          //   showCancel: false,
          //   success () {
          //   }
          // })
        }
      }
    })
  })
}

/**
 * 断开设备
 */
const disconnectDevice = (deviceId) => {
  return new Promise((resolve, reject) => {
    Taro.closeBLEConnection({
      deviceId,
      success (res) {
        console.log(res)
        console.log(deviceId + '： 断开连接成功！');
        resolve()
      },
      fail (err) {
        console.log('断开连接失败！：');
        console.log(err);
        resolve()
      }
    })
  })
}

/**
 * 连接设备
 */
const connectDevice = (deviceId) => {
  return new Promise(async (resolve, reject) => {
    // 断开之前链接的设备
    globalStore.waterDeviceBleInfo?.deviceId && await disconnectDevice(globalStore.waterDeviceBleInfo?.deviceId)
    Taro.createBLEConnection({
      deviceId,
      timeout: 10000,
      success (res) {
        console.log(res);
        console.log(deviceId + ': --链接成功--');
        getBLEDeviceServices(deviceId)
        resolve(deviceId)
      },
      fail (e) {
        console.log(e);
        if (e.errMsg.split(":")[2] == 'already connect') {
          // 已经链接
          console.log('已经链接');
          getBLEDeviceServices(deviceId)
          resolve(deviceId)
        } else if (e.errCode == 10012) {
          Taro.showModal({
            title: "连接超时，请重新连接！",
            showCancel: false,
            success () {
            }
          })
        } else {
          Taro.showModal({
            title: "连接失败，errCode" + e.errCode,
            showCancel: false,
            success () {
            }
          })
        }
      }
    })
  })
}

/**
 * 获取蓝牙设备所有特征值
 */
const getBLEDeviceServices = (deviceId) => {
  return new Promise((resolve, reject) => {
    Taro.getBLEDeviceServices({
      deviceId,
      success: (res) => {
        console.log(res, "getBLEDeviceServices");
        for (let i = 0; i < res.services.length; i++) {
          if (res.services[i].isPrimary) {
            getBLEDeviceCharacteristics(deviceId, res.services[i].uuid)
            resolve(res.services)
            return
          }
        }
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

/**
 * 16进制字符串转 ArrayBuffer
 */
const hexToArrayBuffer = (hex) => {
  let hexArr = hex
  if (typeof hex === 'string') {
    hexArr = hex.match(/[\da-f]{2}/gi)
  }
  return new Uint8Array(
    hexArr.map((byte) => {
      return parseInt(byte, 16);
    })
  ).buffer;
}

/**
 * 加密函数
 */
function encodeArray (dataArrayBuffer) {
  let k = 3;
  const macAddrArray = new Uint8Array([49, 50, 51, 53]); // '1', '2', '3', '5' as byte values
  let dataArray = new Uint8Array(dataArrayBuffer); // Convert ArrayBuffer to Uint8Array for easier manipulation

  for (let i = 3; i <= 30; i++) {
    dataArray[i] = dataArray[i] ^ macAddrArray[k]; // XOR operation
    k--;
    if (k < 0) {
      k = 3; // Reset k to 3 when it goes below 0
    }
  }

  return dataArray.buffer; // Return the modified ArrayBuffer
}



/**
 * 获取蓝牙设备某个服务中所有特征值(characteristic)
 */
const getBLEDeviceCharacteristics = (deviceId, serviceId) => {
  Taro.getBLEDeviceCharacteristics({
    deviceId,
    serviceId,
    success: (res) => {
      console.log('getBLEDeviceCharacteristics success', res.characteristics)
      if (!res.characteristics.length) {
        console.log('characteristics长度为0');
      } else {
        console.log('characteristics长度为' + res.characteristics.length);
      }
      for (let i = 0; i < res.characteristics.length; i++) {
        let item = res.characteristics[i]
        console.log(item);
        if (item.properties.read) {
          Taro.readBLECharacteristicValue({
            deviceId,
            serviceId,
            characteristicId: item.uuid,
          })
        }
        if (item.properties.write) {
          console.log('write', {
            deviceId,
            serviceId,
            characteristicId: item.uuid,
          });

          // 存储蓝牙设备信息
          globalStore.setWaterDeviceBleInfo({
            deviceId,
            serviceId,
            characteristicId: item.uuid,
          })
        }
        if (item.properties.notify || item.properties.indicate) {
          Taro.notifyBLECharacteristicValueChange({
            deviceId,
            serviceId,
            characteristicId: item.uuid,
            state: true,
          })
        }
      }
    },
    fail (res) {
      console.error('getBLEDeviceCharacteristics', res)
      console.log(res);
    }
  })
}

// ArrayBuffer转16进度字符串示例
function ab2hex (buffer) {
  const hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    function (bit) {
      return ('00' + bit.toString(16)).slice(-2)
    }
  )
  return hexArr.join('')
}

/**
 * 监听设备回复数据
 */
export function onBLECharacteristicValueChange () {
  return new Promise((resolve, reject) => {
    Taro.offBLECharacteristicValueChange()//先关闭监听
    Taro.onBLECharacteristicValueChange((characteristic) => {
      console.log("onBLECharacteristicValueChange开启监听");

      const hexStr = ab2hex(characteristic.value)//回复的16进制数据
      const byteArray = new Uint8Array(characteristic.value);//回复的字节数组

      console.log("characteristic: " + characteristic);
      console.log(hexStr, "hexStr")
      console.log(byteArray, "byteArray");
      resolve({
        hexStr,
        byteArray
      })
    })
  })
}