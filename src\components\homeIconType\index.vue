<script setup>
import MyIcon from "@/components/MyIcon";
import request from "@/utils/request";
import Taro, { useDidShow } from "@tarojs/taro";
import { computed, onMounted, ref, watch } from "vue";
const luckImg = 'https://yimits.oss-cn-beijing.aliyuncs.com/picss/key-d.png'
// const shuiImg = 'https://yimits.oss-cn-beijing.aliyuncs.com/picss/shuibiao.png'
import shuiImg from '@/assets/water/device.png'

import { AES_Encrypt, AES_Decrypt, MD5_Encrypt } from "@/utils/crypto";
import { useGlobalStore } from "@/stores";
import { clientId, clientSecret } from "@/config";

const globalStore = useGlobalStore();

const props = defineProps({
  type: {
    type: String,
    required: true,
    default: "1", //1：快速入住 2：门锁 3：水表 4：电表
  },
  fast_contract: {
    type: Object,
    default: () => ({}),
  },
  device: {
    type: Object,
    default: () => ({}),
  },
  // waterDevice: {
  //   type: Object,
  //   default: () => ({}),
  // },
  curentType: {
    type: String,
    default: "4",
  },
  detail: {
    type: Object,
    default: () => ({
      waterDevice:[{}]
    }),
  },
  tentDianHouseDevices: {
    type: Array,
    required: false,
    default() {
      return [];
    },
  },
  tentWaterHouseDevices: {
    type: Array,
    required: false,
    default () {
      return [];
    },
  }
});

const emit = defineEmits(["switchType"]);

const ttlockAccount = ref(); //锁账户

const waterDevice = computed(() => {
  return globalStore.who == 'tenant' ? (globalStore.waterTempDeviceList.length && globalStore.waterTempDeviceList[0] || {}) : ( props.detail.waterDevice.length && props.detail.waterDevice[0] || {})
})

/**
 * 获得token
 */
const getToken = ({ username, password }) => {
  return new Promise((resolve, reject) => {
    request
      .post({
        url: "/oauth2/token",
        LOCKYAPI: true,
        data: {
          username,
          password,
          client_id: clientId,
          client_secret: clientSecret,
          grant_type: "password",
          redirect_uri: "http://www.sciener.cn",
        },
      })
      .then((res) => {
        console.log(res);
        resolve();
        globalStore.setTtlaccessToken(res.access_token);
      });
  });
};

// 获取账户
const getAccount = () => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "ttlock/userDetail",
      })
      .then((res) => {
        console.log(res);
        if (!res.data.length || !res.data) {
          // regsion();
        } else {
          ttlockAccount.value = res.data.filter((item) => item.type == "1")[0];
          getToken({
            username: ttlockAccount.value.username,
            password: ttlockAccount.value.password_hash,
          })
            .then(() => {
              resolve();
            })
            .catch((err) => {
              reject(err);
            });
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

function calculateDaysBetweenDates (date1, date2) {
  if(!date1 || !date2) return NaN
  // 将日期转换为当天的 00:00:00
  const d1 = new Date(date1);
  d1.setHours(0, 0, 0, 0);
  
  const d2 = new Date(date2);
  d2.setHours(0, 0, 0, 0);

  const dayInMs = 24 * 60 * 60 * 1000;
  const diffTime = d2.getTime() - d1.getTime();
  const diffDays = Math.round(diffTime / dayInMs);
  
  return diffDays;
} 

const toMoreDevice = (type, who ='tenant') => {
  if (type == 3) {
    // 水表
    if (who != 'tenant') {
      console.log(props.detail.waterDevice,"props.detail.waterDevice");
      
      globalStore.setMore({device: props.detail.waterDevice});
      Taro.navigateTo({
        url: "/pages/device/moreList/moreList",
      });
      return
    } 
    globalStore.setMore({device:props.tentWaterHouseDevices});
    Taro.navigateTo({
      url: "/pages/device/moreList/moreList",
    });
  } else if (type == 4) {
    // 电表
    globalStore.setMore({device:props.tentDianHouseDevices});
    Taro.navigateTo({
      url: "/pages/device/moreList/moreList",
    });
  }
};

const isAp = process.env.TARO_ENV === 'alipay'

// 跳转快捷入驻
const fastCheckHandel = async (item) => {
  // if(!item.house) return Taro.showToast({ title: '暂未绑定房屋', icon: 'none' })
  if (!isAp) {
    getAccount()
  }
  request
      .get({
        url: "device/" + item.id,
      })
      .then((res) => {
        const obj = {
          id: res.data.id,
          need_people: res.data.need_people,
          sn: res.data.sn,
          lockId:
            props.detail?.ttlock && props.detail?.ttlock.length
              ? props.detail?.ttlock[props.detail?.ttlock.length - 1]?.lock_id
              : undefined,
          house_id: props.detail?.id,
        };
        Taro.navigateTo({
          url:
            "/pages/device/fastCheck/fastCheck?deveice=" + JSON.stringify(obj),
        });
      });
};

const dianDeviceClickHandel = (item) => {
  emit("switchType", 4);
};
const lockClickHandel = (item) => {
  emit("switchType", 2);
};

const waterClickHandel = () => {
  console.log('水表');
  emit("switchType", 3);

}

const toBindDevice = () => {
  Taro.navigateTo({
    url: '/pages/house/addDevice/addDevice?house_id=' + props.detail?.id
  })
}
</script>

<template>
  <view class="home-icon-type-container">
    <!-- 快速入住 -->
    <view
      class="fast-box"
      @tap.stop="fastCheckHandel(device)"
      v-if="device?.id && fast_contract && type == 1"
    >
      <image src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fast-coimg.png" mode="aspectFill" />
      <text
        class="text"
        v-if="
          calculateDaysBetweenDates(new Date(), fast_contract?.expired_at) >=
            0 &&
          !isNaN(
            calculateDaysBetweenDates(new Date(), fast_contract?.expired_at)
          )
        "
        >租期剩余{{
          calculateDaysBetweenDates(new Date(), fast_contract?.expired_at)
        }}天</text
      >
      <text
        class="text"
        style="color: red"
        v-if="
          calculateDaysBetweenDates(new Date(), fast_contract?.expired_at) <
            0 &&
          !isNaN(
            calculateDaysBetweenDates(new Date(), fast_contract?.expired_at)
          )
        "
        >租期逾期{{
          Math.abs(
            calculateDaysBetweenDates(new Date(), fast_contract?.expired_at)
          )
        }}天</text
      >
    </view>
    <!-- 门锁 -->
    <view
      class="lock-door"
      :style="{marginLeft: device?.id && globalStore.who != 'tenant' ? '38rpx' : device?.id && globalStore.who == 'tenant' ? '18rpx' : '0'}"
      v-if="type == 2"
      @tap.stop="lockClickHandel(device)"
    >
      <image :src="luckImg" mode="aspectFill"></image>
      <text>门锁</text>
      <view class="active" v-if="curentType == 2"></view>
    </view>
    <!-- 水表 -->
    <view :class="waterDevice?.sn ? 'dian-device water-device' : 'water-device'" v-if="type == 3"   @tap.stop="waterClickHandel(waterDevice)" :style="{marginLeft: globalStore.who != 'tenant' ? '38rpx' : '18rpx',background: curentType == 3 ? '#f0fcfc' : '#f0f5fe'}">
      <template  v-if="!waterDevice?.sn">
        <text
          @tap.stop="toMoreDevice(3)"
          v-if="globalStore.who == 'tenant'  && tentWaterHouseDevices.length > 1"
          class="badge"
        >{{ tentWaterHouseDevices.length }}</text>
        <image :src="shuiImg" mode="aspectFill"></image>
        <text>水表</text>
      </template>
      <template v-if="waterDevice?.sn">
        <view class="tic">
          <!-- 用户端 -->
          <text
            @tap.stop="toMoreDevice(3)"
            v-if="globalStore.who == 'tenant'  && tentWaterHouseDevices.length > 1"
            class="badge"
          >{{ tentWaterHouseDevices.length }}</text>
          <!-- 房东端 -->
          <text
            @tap.stop="toMoreDevice(3, 'business')"
            v-if="globalStore.who != 'tenant' && detail.waterDevice.length > 1"
            class="badge"
          >{{ detail.waterDevice.length }}</text>
          <image :src="shuiImg"  style="margin-right: 5rpx;"  mode="aspectFill"></image>
          <view style="position: relative">
            <text class="label" v-if="waterDevice?.net_type === 1">4G</text>
            <text class="label" v-if="waterDevice?.net_type !== 1"></text>
            <MyIcon
              :icon="'signal/' + waterDevice?.signal_num "
              width="60rpx"
              height="42rpx"
              v-if="waterDevice?.net_type === 1"
            />
            <!-- 4g无信号显示异常提示 -->
            <text class="net-type-wraing" v-if="(waterDevice?.net_type == 1 && waterDevice?.signal <= 0)">
              设备离线
            </text>
          </view>
        </view>
      </template>
      <text class="sn-text" v-if="waterDevice?.sn"> {{ waterDevice.sn }}</text>
      <view class="active" style="background: #f0fcfc;" v-if="curentType == 3"></view>
    </view>
    <!-- 电表 -->
    <view
      class="dian-device"
      v-if="type == 4"
      @tap.stop="dianDeviceClickHandel(device)"
    >
      <view class="tic" v-if="device">
        <text
          @tap.stop="toMoreDevice(4)"
          v-if="globalStore.who == 'tenant'  && tentDianHouseDevices.length > 1"
          class="badge"
        >{{ tentDianHouseDevices.length }}</text>
        <MyIcon
          icon="icon-dianbiao"
          width="52rpx"
          height="56rpx"
          style="margin-right: 5rpx"
        />
        <view style="position: relative" v-if="device.net_type === 1 && device.signal > 0">
          <text class="label" v-if="device.net_type === 1">4G</text>
          <text class="label" v-if="device.net_type !== 1"></text>
          <MyIcon
            :icon="'signal/' + device.signal_num"
            width="60rpx"
            height="42rpx"
            v-if="device.net_type === 1"
          />
        </view>
        <!-- 4g无信号显示异常提示 -->
        <text class="net-type-wraing" v-if="device.net_type == 1 && device.signal <= 0">
          设备离线
        </text>
      </view>
      <view v-if="!device?.sn" class="dian-device" style="font-size: 24rpx;"  @tap.stop="dianDeviceClickHandel(device)">
        <view class="tic">
          <MyIcon
            icon="icon-dianbiao"
            width="52rpx"
            height="56rpx"
            style="margin-right: 5rpx"
          />
          <text>电表</text>
        </view>
      </view>
      <text class="sn-text" v-if="device.sn "> {{ device.sn }}</text>
      <view class="active" v-if="curentType == 4"></view>
    </view>
  </view>
</template>

<style lang="less">
.device-top {
  position: relative;
}
.home-icon-type-container {
  .badge {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 40px;
    height: 40px;
    font-size: 32px;
    text-align: center;
    line-height: 40px;
    background-color: #ff0000;
    color: #ffffff;
    border-radius: 100px;
    z-index:1;
  }
  .net-type-wraing {
    font-size: 22px;
    color: red;
    display: inline-block;
    font-weight: 700;
    width: 50px;
    transform: translateX(6px);
  }
  .active {
    position: absolute;
    bottom: -16px;
    left: 0;
    width: 100%;
    height: 21px;
    background: #f0f5fe;
  }
  .fast-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 142px;
    height: 97px;
    // background: #ccc;
    background: #f0f5fe;
    border-radius: 10px;

    // align-items: center;
    // margin-left: 80px;
    image {
      width: 105px !important;
      height: 61px !important;
      flex-shrink: 0;
    }

    text {
      color: #000;
      font-size: 20px;
      font-weight: 700;
      // margin-top: -10px;
    }
  }
  .lock-door {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 142px;
    height: 97px;
    background: #f0f4ff;
    border-radius: 10px;
    font-size: 26px;
    color: #000;
    margin-left: 27px;
    box-sizing: border-box;
    padding: 0 14px;
    image {
      width: 52px;
      height: 58px;
      vertical-align: middle;
    }
  }
  .dian-device {
    position: absolute;
    right: 0;
    top: 0;
    width: 142px;
    height: 97px;
    background: #f0f5fe;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-left: 29px;
    .icon-youjiantou1 {
      position: absolute;
      right: 15px;
      top: 8px;
      width: 9px;
      height: 15px;
      margin-left: 15px;
      color: #1f5bfd;
      font-size: 20px;
      z-index: 90;
    }
    .tic {
      display: flex;
      align-items: center;
      justify-content: center;

      .label {
        position: absolute;
        top: 14rpx;
        left: -17rpx;
        font-weight: 700;
        font-size: 15px;
        display: inline-block;
        transform: translate(22px, -15px);
      }
    }
    .sn-text {
      color: #000;
      font-size: 14px;
      font-weight: 700;
      width: 127px;
      height: 24px;
      background: #c9d6ee;
      border-radius: 6px;
      display: flex;
      justify-content: center;
      align-items: center;
      transform: translateY(-5px);
    }
  }
  .water-device {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 142px;
    height: 97px;
    background: #f0f4ff;
    border-radius: 10px;
    font-size: 26px;
    color: #000;
    margin-left: 27px;
    box-sizing: border-box;
    padding: 0 14px;
    image {
      width: 43px;
      height: 76px;
      vertical-align: middle;
    }
    .icon-youjiantou1 {
      position: absolute;
      right: 15px;
      top: 8px;
      width: 9px;
      height: 15px;
      margin-left: 15px;
      color: #1f5bfd;
      font-size: 20px;
      z-index: 90;
    }
    .tic {
      display: flex;
      align-items: center;
      justify-content: center;

      .label {
        position: absolute;
        top: 14rpx;
        left: -17rpx;
        font-weight: 700;
        font-size: 15px;
        display: inline-block;
        transform: translate(22px, -15px);
      }
    }
    .sn-text {
      color: #000;
      font-size: 14px;
      font-weight: 700;
      width: 127px;
      height: 24px;
      background: #c9d6ee;
      border-radius: 6px;
      display: flex;
      justify-content: center;
      align-items: center;
      transform: translateY(-8px);
    }
  }
}
</style>
