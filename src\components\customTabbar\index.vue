<!-- 此文件是自定义的底部TabBar -->
<template>
  <view class="tabbar-list">
    <view
      class="tabbar-item"
      :class="{ active: globalStore.activeIndex === index }"
      v-for="(item, index) in globalStore.tablist"
      :key="index"
      @tap="changeActive(item, index)"
    >
      <image
        class="img"
        :src="
          globalStore.activeIndex === index
            ? '/' + item.selectedIconPath
            : '/' + item.iconPath
        "
      ></image>
      <view :style="globalStore.activeIndex === index ? 'color:#1e5cfd' : ''">{{
        item.text
      }}</view>
    </view>
  </view>
  <!-- 防止闪烁 -->
  <view class="emty-p-c">
    <image
      v-for="(item, index) in globalStore.tablist"
      :src="'/' + item.selectedIconPath"
      mode="scaleToFill"
    />
  </view>
</template>
<script setup>
import { computed, onMounted } from "vue";

import Taro, { useDidShow, useDidHide, useReady, useLoad } from "@tarojs/taro";

import { useGlobalStore } from "@/stores";

const globalStore = useGlobalStore();

const changeActive = (item, index) => {
  // store.commit("tabBarModule/changeIndex", index);
  globalStore.setActive(index);
  setTimeout(() => {
    Taro.switchTab({
      url: "/" + item.pagePath,
    });
  }, 0);
};

onMounted(() => {
  console.log("ok");
  // 用来切换身份
  // 每次刷新重置到第一个tabbar
  // store.commit('tabBarModule/setRole', 'pubilc')
  // if (role == "tenant") {
  //   globalStore.setRole("tenant");
  // } else {
  //   globalStore.setRole("tenant");
  // }
  // console.log(globalStore.who);
  // globalStore.setRole(globalStore.who);
  // console.log(globalStore.tablist);
});
</script>

<style lang="scss">
.emty-p-c {
  display: none;
}
.tabbar-list {
  display: flex;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 120rpx;
  background-color: #fff;
  // border: 1px solid #fff;
  overflow: hidden;
  // border-top-left-radius: 50rpx;
  // border-top-right-radius: 50rpx;
  box-shadow: 10px 10px 10px 10px #dadada;
  // 设置ios刘海屏底部横线安全区域
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  .tabbar-item {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 20rpx;
    color: #999;

    &.active {
      // &::before {
      // 	position: absolute;
      // 	left: 50%;
      // 	top: 0%;
      // 	transform: translateX(-50%);
      // 	content: '';
      // 	display: inline-block;
      // 	width: 34rpx;
      // 	height: 6rpx;
      // 	border-radius: 6rpx;
      // 	background-color: #3c82f6;
      // 	animation: opan .3s forwards;

      // 	@keyframes opan {
      // 		0% {
      // 			opacity: 0;
      // 		}

      // 		100% {
      // 			opacity: 1;
      // 		}
      // 	}
      // }
    }

    .img {
      width: 41rpx;
      height: 41rpx;
      margin-bottom: 4rpx;
    }
  }
}
</style>
