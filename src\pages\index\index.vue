<template>
  <view>
    <view class="home-business" v-if="globalStore.who === 'business'">
      <view class="header" :style="isAp ? 'padding-top:90rpx' : ''">
        <view class="logo">
          <!-- <view
            class="message-ico iconfont icon-lingdang"
            :class="{ active: isNewMsg && globalStore.isNewMsgShow }"
            @tap="clickMsgHandel"
          >
          </view> -->
          <image class="img" :src="logo" mode="aspectFit"></image
        ></view>
        <view class="uls">
          <view @tap="toDevLisHandel('0')">
            <view class="num">{{ countData?.all || "0" }}</view>
            <view class="label">全部</view>
          </view>
          <view class="border-b"></view>
          <view @tap="toDevLisHandel('3')">
            <view class="num">{{ countData?.off || "0" }}</view>
            <view class="label">离线</view>
          </view>
          <view class="border-b"></view>
          <view @tap="toDevLisHandel('5')">
            <view class="num">{{ countData?.low || "0" }}</view>
            <view class="label">低电</view>
          </view>
          <view class="border-b"></view>
          <view @tap="toDevLisHandel('7')">
            <view class="num">{{ countData?.unrecharge || "0" }}</view>
            <view class="label">30天未充值</view>
          </view>
          <view class="border-b"></view>
          <view @tap="toDevLisHandel('6')">
            <view class="num">{{ countData?.warning || "0" }}</view>
            <view class="label">7天未用</view>
          </view>
        </view>
      </view>
      <view class="header-bg"></view>
      <view class="main">
        <view class="box2 flex">
          <!-- <view
            class="enter-chart flex flex-v-center"
            @tap="onJump('/pages/money/stat/stat')"
          >
            <view
              ><image
                class="icon-chart"
                :src="iconChart"
                mode="aspectFit"
              ></image
            ></view>
            <view>数据统计</view>
          </view> -->
          <view
            class="enter-help flex flex-v-center flex-space-between"
            @tap="onJumpPublic('/pages/webview/article?key=fangdongzhinan')"
          >
            <view
              ><image class="icon-help" :src="iconHelp" mode="aspectFit"></image
            ></view>
            <view style="padding-right: 20px; color: red;"
              >使用教程 房东必看</view
            >
            <view
              ><image
                class="icon-arrow1"
                :src="iconArrow1"
                mode="aspectFit"
              ></image
            ></view>
          </view>
        </view>
        <view class="deveice-box">
          <MySearchHome
            placeholder="房间名称搜索"
            @search="onSearch"
            isShowSwitchTag
            @onSwitchTag="onSwitchTagHandel"
            :showDialog="isShowSwitchTag"
            :switchText="switchText"
          ></MySearchHome>
          <scroll-view class="scrollview" scroll-x="true" :show-scrollbar="false" enhanced 	:scroll-left="scrollLeft"  style="height: 70rpx;padding: 0 20rpx;box-sizing: border-box;">
            <view class="status-filter">
              <text
                @tap="onChangeStatus(item.value)"
                v-for="(item, index) in statusLs"
                :key="index"
                class="tag"
                :class="params.status === item.value ? 'tag-selected' : 'tag-default'"
                >{{ item.name }}</text
              >
            </view>
          </scroll-view>

          <DeviceRenderIndex
            v-if="items.length"
            :items="items"
            @detail="onDetail"
            @editHoseName="editHoseNameHandel"
          ></DeviceRenderIndex>
          <view class="emty" v-else> 您还没有电表，请马上添加吧~ </view>
        </view>
        <view class="btn-device" :style="isAp ? `bottom:${safeAreaBottom}rpx` : '' " @tap="onBindDevice">
          <image class="icon-scan" :src="iconScan" mode="widthFix"></image>
          添加设备
        </view>
      </view>
    </view>
    <view class="home-tenant" v-if="globalStore.who === 'tenant'">
      <view class="btn-device" style="z-index: 2;" v-if="tempList.length && globalStore?.isLogin" :style="isAp ? `bottom:${safeAreaBottom}rpx` : '' " @tap="lookOrder">
          {{ tempList.some(item => item.status == 5) ? '结束订单' : '查看订单' }}
      </view>
      <view class="header">
        <view class="header-title">
          <view class="tt1">我的房源</view>
          <view class="tt2">智慧租房，智慧生活</view>
        </view>
        <view class="more-house" v-if="tenant.contract" @tap="onMoreHouse"
          >更多房源</view
        >
      </view>
      <view class="main" :style="tenant.contract && (globalStore.tempDeviceList.length || globalStore.waterTempDeviceList.length) ? 'height:390rpx' : ''">
        <view class="my-house">
          <view class="my-house-box" v-if="globalStore.tempDeviceList.length || globalStore.waterTempDeviceList.length" style="margin-bottom: 30rpx;">
            <DeviceRender
              :device="globalStore.tempDeviceList[0]"
              @tap.stop="onDeviceDetail(globalStore.tempDeviceList[0]?.id || tempDevice.id)"
              :class="globalStore.who == 'tenant' ? 'tenant-item' : ''"
              :tentDianHouseDevices="tentDianHouseDevices"
              :tentWaterHouseDevices="tentWaterHouseDevices"
            ></DeviceRender>
            <!-- <DeviceRenderAlipay
              :device="tempDevice"
              v-if="isAp"
              @tap="onDeviceDetail(tempDevice.id)"
            ></DeviceRenderAlipay> -->
            <!-- <view class="text-center"
              ><text class="tag tag-link" @tap="onMoreTempDevice"
                >更多设备</text
              ></view
            > -->
          </view>
          <view class="my-house-box" v-if="tenant.contract || (!globalStore.tempDeviceList.length && !globalStore.waterTempDeviceList.length)">
            <view class="my-house-empty" v-if="!tenant.contract">
              <view class="font-36">您还没有租约信息</view>
              <view class="font-26">基于物联网的房屋租赁交易与管理平台</view>
            </view>
            <view class="my-house-full" v-if="tenant.contract">
              <view class="my-house-name" style="
                  width: 85vw;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
              ">{{ tenant.contract.name }}</view>
              <view class="my-house-days"
                >租期还剩
                <text class="color-primary">{{
                  tenant.contract.expire_days
                }}</text>
                天</view
              >
              <view class="my-house-stat flex flex-space-between">
                <view class="flex-half text-center" @tap="onJumpFailBill">
                  <view class="stat-num">{{
                    tenant.contract.fail_bill_count
                  }}</view>
                  <view class="stat-txt">逾期费用/笔</view>
                </view>
                <view class="flex-half text-center">
                  <view class="stat-num" @tap="onDeviceRecharge">{{
                    (tenant.contract.dianbiao && tenant.contract.dianbiao.du) ||
                    0
                  }}</view>
                  <view class="stat-txt">可用电量/度</view>
                  <view class="more-device"
                    >
                    <!-- <text
                      class="tag tag-link"
                      @tap="onMoreDevice(tenant.contract.house_id)"
                      >更多设备</text
                    > -->
                    </view
                  >
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="menu flex flex-row flex-space-between" v-if="tempList.some(item => item.status == 5) ? false : true">
        <view
          class="menu-item"
          @tap="onJump('/pages/tenant/contract/contract')"
        >
          <view class="menu-icon menu3"
            ><image
              class="menu-img"
              :src="iconContract"
              mode="widthFix"
            ></image
          ></view>
          <view>我的合同</view>
          <view class="badge" v-if="tenant.sign_count > 0">{{
            tenant.sign_count
          }}</view>
        </view>
        <view class="menu-item" @tap="onJumpBill">
          <view class="menu-icon menu4"
            ><image class="menu-img" :src="iconBill" mode="widthFix"></image
          ></view>
          <view>待缴账单</view>
          <view class="badge" v-if="tenant.pay_bill_count > 0">{{
            tenant.pay_bill_count
          }}</view>
        </view>
        <!--          <view class="menu-item ">-->
        <!--            <view class="menu-icon menu5"><image class="menu-img" :src="iconMoney" mode="widthFix"></image></view>-->
        <!--            <view>我的钱包</view>-->
        <!--          </view>-->
        <view class="menu-item" @tap="onJump('/pages/tenant/repair/repair')">
          <view class="menu-icon menu6"
            ><image class="menu-img" :src="iconBaoxiu" mode="widthFix"></image
          ></view>
          <view>在线报修</view>
        </view>
      </view>
      <view class="my-bill" v-if="tenant.bills.length > 0">
        <view class="bill-title">近期账单</view>
        <view class="bill-item" v-for="item in tenant.bills">
          <view class="bill-house">{{
            item.house.estate_name + item.house.name
          }}</view>
          <view class="bill-con bill-border-btm flex flex-space-between">
            <view class="bill-name"
              >{{ item.title }}
              <text class="tag tag-warning-small" v-if="item.is_fail"
                >已逾期</text
              ></view
            >
            <view class="bill-amount"
              ><text class="cny">￥</text>{{ item.amount }}</view
            >
          </view>
          <view class="bill-btm flex flex-space-between flex-v-center">
            <view class="bill-pay-date">应付日期：{{ item.pay_date }}</view>
            <view
              ><button class="btn-primary-small" @tap="onShowPayOne(item)">
                立即支付
              </button></view
            >
          </view>
        </view>
      </view>
    </view>
    <MyPopup :show="showPay" title="在线支付" @close="changeShowPay">
      <template #content>
        <view class="b-pay-amount p20 text-center"
          ><text class="cny">￥</text> {{ payData.amount }}</view
        >
        <view v-if="payData.fee > 0" class="b-pay-fee p20">
          包含服务费 {{ payData.fee }}元
        </view>
        <view class="b-pay-tips"
          >您支付的费用直达房东账户，如需退款请直接联系房东。</view
        >
        <view style="padding-bottom: 145rpx;" class="p20"
          ><button class="btn-primary" @tap="onPay">立即支付</button></view
        >
      </template>
    </MyPopup>
    <LoginDialog :show="showLogin" @close="onLoginClose" @login="onLogin">
    </LoginDialog>
    <!-- <TabBarIndex /> -->
    <emtyMessage
      title="提示"
      confirmText="知道了"
      :show="emtyShow"
      @close="emtyShow = false"
      @confirm="confirmTipHandel"
    >
      <template #content>
        <view class="emty-message-container">
          <view class="qrcode">
            <image
              src="https://yimits.oss-cn-beijing.aliyuncs.com/images/qrcode.png"
              mode="aspectFill"
            />
          </view>
          <view class="text-b">
            <view>首次开通电表</view>
            <view>请扫描电表上的二维码</view>
          </view>
        </view>
      </template>
    </emtyMessage>
    <YToast :show="showToast" width="310" height="100" padding="35rpx 35rpx" :text="yToastText" @close="showToast = false"/>

    <MyPopup :show="isEditShow" title="房源名修改" @close="isEditShow = false">
      <template #content>
        <view style="color: #000000; padding: 35rpx;" :style="{paddingBottom: isAp ? '160rpx' : '100rpx'}">
          <view class="font-24">
           房源名称
          </view>
          <view class="p20"><MyInput2>
            <template #content>
              <input class="input-custom" :cursor="posCur" @input="bindReplaceInput"  cursor-spacing="120" v-model="formState.name" placeholder="请输入房源名称" />
            </template>
          </MyInput2></view>

          <view class="text-center" style="padding: 30rpx;">
            <button class="btn-primary btn-inline" style="width: 220rpx;" @tap="onSubmitHoseName">确定</button>
          </view>
        </view>
      </template>
    </MyPopup>

    <SwitchTagDialog  :showDialog="isShowSwitchTag" @close="isShowSwitchTag = false" @confirm="onConfirmSwitchTag" />

    <YModal
      title="设备已绑定"
      confirmText="好的"
      :show="showBingding"
      @close="showBingding = false"
      @confirm="showBingding = false"
      @cancel="openPhone(globalStore.userInfo.service_te || globalStore?.userInfo?.agent?.mobile)"
      :maskClose="false"
      cancelText="联系客服"
      :showCancel="qrcodeSnDetail?.business?.user?.mobile != globalStore.userInfo?.mobile"
      :bodyStyle="{
        backgroundColor: '#fff',
        paddingBottom: '30rpx',
        width: '650rpx',
        borderRadius: '40rpx',
        zIndex: 9999999999,
      }"
      :cancelStyle="{fontSize: '32rpx',marginTop:'15rpx'}"
    >
      <template #content>
        <view class="bind-container" v-if="qrcodeSnDetail?.business?.user?.mobile == globalStore.userInfo?.mobile">
          <!-- 房东: {{ qrcodeSnDetail?.business?.user?.nickname }}, 手机号：{{ qrcodeSnDetail?.business?.user?.mobile }}, -->
          {{ qrcodeSnDetail?.sn }}{{qrcodeSnDetail?.type == 2 ? '水表' : '电表'}}，已经绑定到 {{ qrcodeSnDetail?.house?.name }} 房间
        </view>
        <view class="bind-container" v-else>
          <!-- 房东: {{ qrcodeSnDetail?.business?.user?.nickname }}, 手机号：{{ qrcodeSnDetail?.business?.user?.mobile }}, -->
          {{ qrcodeSnDetail?.sn }}{{qrcodeSnDetail?.type == 2 ? '水表' : '电表'}}，该表已经绑定到其他房东，请联系客服：（{{ globalStore.userInfo.service_te || globalStore?.userInfo?.agent?.mobile || '************'}}）
        </view>
      </template>
    </YModal>

    <YModal
      title="请先选择水表后扫码添加"
      confirmText="知道了"
      :show="showWaterScan"
      @close="showWaterScan = false"
      @confirm="showWaterScan = false"
      @cancel="openPhone(globalStore.userInfo.service_te || globalStore?.userInfo?.agent?.mobile)"
      :maskClose="false"
      :showCancel="false"
      :bodyStyle="{
        backgroundColor: '#fff',
        paddingBottom: '30rpx',
        width: '650rpx',
        borderRadius: '40rpx',
        zIndex: 9999999999,
      }"
      :cancelStyle="{fontSize: '32rpx',marginTop:'15rpx'}"
    >
      <template #content>
        <view class="water-bind-container">
          <view class="water-box">
            <image :src="shuiImg" mode="aspectFill" />
            <text>水表</text>
          </view>
        </view>
      </template>
    </YModal>

  </view>
</template>

<script setup>
import { ref,unref } from "vue";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useShareAppMessage,
  useReachBottom,
  usePageScroll
} from "@tarojs/taro";
import request from "@/utils/request";
import { useGlobalStore } from "@/stores";
import { toFixed } from "@/utils";
import YModal from "@/components/YModal";

import "./index.scss";
const logo = "https://yimits.oss-cn-beijing.aliyuncs.com/images/logo.png";
const iconChart = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-chart.png";
const iconHelp = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-help.png";
const iconArrow1 = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-arrow1.png";
const iconHouse = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-house.png";
const iconDevice = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-device.png";
const iconContract = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-contract.png";
const iconBill = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-bill.png";
const iconMoney = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-money.png";
const iconBaoxiu = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-baoxiu.png";
const iconScan = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-scan.png";

import MyIcon from "@/components/MyIcon";
import MyPopup from "@/components/MyPopup";
import DeviceRender from "@/components/DeviceRender/item";
import DeviceRenderAlipay from "@/components/DeviceRender/itemAlipay";

import MyInput2 from '@/components/MyInput2'

import SwitchTagDialog from '@/components/SwitchTag/index.vue'

import TabBarIndex from "@/components/customTabbar";

import LoginDialog from "@/components/LoginDialog";

import MySearchHome from "@/components/MySearchHome";

import DeviceRenderIndex from "@/components/DeviceRender";

import emtyMessage from './components/emtyMessage.vue'

import shuiImg from '@/assets/water/device.png'

const globalStore = useGlobalStore();

import YToast from '@/components/YToast/index.vue'

const scrollLeft = ref(0)

const scrollTop = ref(0)

const showBingding = ref(false)//设备是否已被绑定

const showWaterScan = ref(false)//电表扫码的水表

const qrcodeSnDetail = ref('')//设备二维码信息

const isShowSwitchTag = ref(false)

import { tenant as tenantTab, landlord, curent } from "@/utils/tabbar";

import { role, active, isLogin } from "@/utils/tabActive";
import { computed } from 'vue';
import { watch } from 'vue';


const showToast = ref(false)

const yToastText = ref('')

// Taro.hideTabBar();

// globalStore.getUserInfo();

const isNewMsg = ref(false);

const emtyShow = ref(false)

const countData = ref();

const hoseName = ref('')

const tentDianHouseDevices = ref([])

const tentWaterHouseDevices = ref([])

const isEditShow = ref(false)

const home = ref({
  contract: {
    willExpire: 0,
    expired: 0,
  },
  lastMonth: {
    bill: 0,
    paid: 0,
  },
});

const safeAreaBottom = ref(120)

const isAp = process.env.TARO_ENV === 'alipay'

if (process.env.TARO_ENV === 'alipay') {
  const info =  my.getSystemInfoSync()
  my.setNavigationBar({
    frontColor: '#ffffff',
    backgroundColor: '#1452fd',
  })
  if ( info.platform === "iOS") {
    // safeAreaBottom.value = info.screenHeight - info.safeArea.height + 120
    safeAreaBottom.value = ((info.screenHeight - info.safeArea.bottom) * 2) + 120
  }
}

const hasTempDevice = ref(false);
const tempDevice = ref({});

const openPhone = (phone) => {
  Taro.makePhoneCall({
    phoneNumber: phone || '************', 
  });
};

const tenant = ref({
  contract: [],
  myContractIndex: globalStore.myContractIndex,
  bills: [],
});

const statusLs = ref([
  {
    name: '全部',
    value:'all'
  },
  {
    name: '未出租',
    value: 'vacant'
  },
  {
    name: '已出租',
    value: 'hired'
  },
  {
    name: '签订中',
    value: 'signing'
  },
  {
    name: '房租已逾期',
    value: 'fail'
  },
  {
    name: '合同快到期',
    value: 'expire'
  }
])

const formState = ref({
  estate_name: "",
  name: "",
  province: "",
  city: "",
  district: "",
  fang: 1,
  ting: 1,
  wei: 1,
  chu: 1,
  size: 0,
  rent: 0,
});

const posCur = ref(-1)

const bindReplaceInput = (e) => {
  var pos = e.detail.cursor
  posCur.value = pos
}

const onSearch = (keyword) => {
  params.value.keyword = keyword;
  fetchDevics();
};

const onSwitchTagHandel = () => {
  if (!globalStore.isLogin) {
    showLogin.value = true;
    return;
  } else {
    showLogin.value = false;
  }
  isShowSwitchTag.value = !isShowSwitchTag.value
}

const switchText = ref('选择楼幢')

const onConfirmSwitchTag = (item) => {
  console.log("onConfirmSwitchTag", item);
  switchText.value = item?.labels && item?.labels.join('、')
  params.value.class_id = item?.ids
  isLastPage.value = false
  fetchDevics()
}

const editHoseNameHandel = (item) => {
  console.log(item);
  isEditShow.value = !isEditShow.value
    // 获取
    request
      .get({
        url: "house/" + item.id,
      })
      .then((res) => {
        formState.value = {
          id: res.data.house.id,
          estate_name: res.data.house.estate_name,
          name: res.data.house.name,
          province: res.data.house.province,
          city: res.data.house.city,
          district: res.data.house.district,
          fang: res.data.house.fang,
          ting: res.data.house.ting,
          wei: res.data.house.wei,
          chu: res.data.house.chu,
          size: res.data.house.size,
          rent: res.data.house.rent,
        };
      });
}

const toDevLisHandel = (type) => {
 Taro.navigateTo({
   url: "/pages/device/device?type=" + type,
 })
}

const onSubmitHoseName = () => {
  if(!formState.value.name) return Taro.showToast({
        title: "请输入房源名称！",
        icon: "none",
      });
  // 更新
  request
    .post({
      url: "house/update/" + formState.value.id,
      data: {
        house: formState.value,
      },
    })
    .then(async(data) => {
      await fetchDevics();
      isEditShow.value = false
      Taro.showToast({
        title: "修改成功！",
        icon: "success",
        success: () => {
        }
      });
    })
}

const onDetail = (id) => {
  globalStore.setIndexPageScrollTop(scrollTop.value.toFixed(2))
  globalStore.setIndexRecharegeId(id)
  // globalStore.setIndexCatcheList(items.value)
  Taro.navigateTo({
    url: "/pages/device/detail/detail?id=" + id,
  });
};

// 获取统计信息
const getCount = async () => {
  const res = await request.get({
    url: "user/deviceCount",
  });
  if (res.code == 200) {
    countData.value = res.data;
  }
};

const items = ref([]);
const total = ref(0);

const isLastPage = ref(false);

const params = ref({
  page: 1,
  status: 0,
  keyword: "",
  sort: "created_at",
  order: "desc",
  class_id: '',
  limit:5
});

const onChangeStatus = (val) => {
  if (!globalStore.isLogin) {
    showLogin.value = true;
    return;
  }
  params.value.status = val;
  if (val == 4) {
    scrollLeft.value = 120
  } else {
    scrollLeft.value = 0
  }
  fetchDevics();
};

const fetchDevics = () => {
  return new Promise(async(reslove) => {
    if(globalStore.who == "tenant")  return reslove()
    items.value = [];
    isLastPage.value = false
    params.value.page = 1;
    globalStore.setIndexRecharegeId(null)
    globalStore.setIndexPageScrollTop(0)
    await getList();
    reslove()
  })
};

 usePageScroll((e) => {
   scrollTop.value = e.scrollTop
 })

const confirmTipHandel = () => {
  globalStore.setTip(1)
  emtyShow.value = false
}

const getList = () => {
  return new Promise((reslove) => {
    request
      .get({
        url: "house",
        data: {
          ...params.value,
          is_warning:params.value.status == 6 ? 1 : "",
          has_device:1
        },
      })
      .then((res) => {
        if (items.value.length > 0) {
          items.value = [...items.value, ...res.data.items.map(itema => {
            return {
              ...itema,
              curentType: itema.device.length ? 4 : 4
            }
          })];
        } else {
          items.value = res.data.items.map(itema => {
            return {
              ...itema,
              curentType: itema.device.length ? 4 : 4
            }
          });
        }
        if (res.data.total === 0 && globalStore.who == "business" && !globalStore.isTipShow) {
          if (!qrcodeSn.value) {
            emtyShow.value = true
          }
        } else {
          emtyShow.value = false
        }
        if (res.data.total > 0) {
          globalStore.setTip(1)
        }
        total.value = res.data.total;
        if (res.data.currentPage >= res.data.lastPage) {
          isLastPage.value = true;
        }
        reslove()
      });
  })
};

const fetch = () => {
  if (globalStore.who === "business") {
    request
      .get({
        url: "index/business",
      })
      .then((res) => {
        home.value = res.data;
        console.log(home.value);
      });
  }
  if (globalStore.who === "tenant") {
    getTempOrder()
    request
      .get({
        url: "index/tenant",
        data: {
          contract_id: globalStore.myContractIndex,
        },
      })
      .then((res) => {
        tenant.value = {
          contract: res.data.contract.length > 0 ? res.data.contract[0] : null,
          bills: res.data.bills,
          pay_bill_count: res.data.pay_bill_count,
          sign_count: res.data.sign_count,
        };
        if (!conf.value.id && tenant.value.contract) {
          request
            .get({
              url: "business/conf",
              data: {
                businessId: tenant.value.contract.business_id,
              },
            })
            .then((res) => {
              conf.value = res.data;
              if (res.data.fee_payer === 2) {
                payData.value.rate = 0.006;
              }
            });
        }
        if (!tenant.value.contract && globalStore.tempDeviceList.length > 0) {
          request
            .get({
              url:
                "device/" +
                globalStore.tempDeviceList[
                  globalStore.tempDeviceList.length - 1
                ].id,
            })
            .then((res) => {
              if (res.data) {
                tempDevice.value = res.data;
                hasTempDevice.value = true;
              }
            });
        }
      });
  }
};

const onJumpFailBill = () => {
  if (!globalStore.isLogin) {
    showLogin.value = true;
    return;
  }
  Taro.navigateTo({
    url:
      "/pages/tenant/bill/bill?status=3&contractId=" + tenant.value.contract.id,
  });
};

const onJumpBill = () => {
  if (!globalStore.isLogin) {
    showLogin.value = true;
    return;
  }
  if (!tenant.value.contract) {
    Taro.showToast({
      title: "暂无房源信息",
      icon: "none",
    });
    return;
  }
  Taro.navigateTo({
    url: "/pages/tenant/bill/bill?contractId=" + tenant.value.contract.id,
  });
};

const clickMsgHandel = () => {
  onJump("/pages/messagePage/messagePage");
};

const isLoadAuto = ref(false)//是否第一次加载过自动跳转

const isNeedShow = ref(false)//是否搜集身份


/**
 * 如果有sn并且是租客端 说明分享绑定过设备 跳转到电表充值页面
 */
const autoJumpRecharge = () => {
  return new Promise(async(reslove) => {
    const userInfo = await globalStore.getUserInfo();
    console.log(userInfo);
    if (userInfo?.sn && Boolean(userInfo?.sn) && userInfo?.type == 'tenant') {
      console.log('autoJumpRecharge');
      request.get({
          url: 'device/search',
          data: {
            sn: userInfo?.sn
          },
          showToast:false
        }).then(response => {
          if (response.data.type == 2) {
            // 水表
            globalStore.setWaterTempDevice(response.data)
          } else {
            // 电表
            globalStore.setTempDevice(response.data)
            tempDevice.value = response.data;
            hasTempDevice.value = true;
          }
         console.log(response.data,"response.data");
          reslove()
          if(unref(qrcodeSn)) return
          if(unref(isNeedShow)) return
          Taro.navigateTo({
            url: `/pages/tenant/deviceDetail/deviceDetail?sn=${userInfo.sn}&share=1'`,
              success: function (res) {
              isLoadAuto.value = true
            }
          })
        })
    } else {
      globalStore.setTempDevice()
      tempDevice.value = {};
      hasTempDevice.value = false;
      reslove()
    }
  })
}

watch(() => isLogin.value, (val) => {
  if (val) {
    showLogin.value = true;
  } else {
    showLogin.value = false;
  }
})

/**
 * 切换身份
 */
const handleChangeWho = (who) => {
  globalStore.setWho(who);
  active.value = 0;

  if (who == "tenant") {
    Taro.setStorageSync("tablist", JSON.stringify(tenantTab));
    curent.value = tenantTab;
  } else {
    Taro.setStorageSync("tablist", JSON.stringify(landlord));
    curent.value = landlord;
  }
  globalStore.homeHasChange = true;
  request
    .post({
      url: "user/update",
      data: {
        type: who,
      },
    })
    .then((res) => {
    });
};

const qrcodeSn = ref(null)


/**
 * 微信扫码 判断电表 自动跳转
 */
 const DeviceScanCodeHandel = async(sn) => {
  console.log('sn999:'+sn);
  if(!sn) return
    const res = await request.get({
      url: 'device/search',
      data: {
        sn
      },
      showLoading:false,
      showToast:false
    })
    // TODO 先判断是否是退费电表 如果是退费电表并且有正在进行的订单 则跳转到退费页面 
   if (res.data?.show_order == 1 && tempList.value.some(item => item.status == 5)) {
      Taro.navigateTo({
        url: "/pages/tenant/tempOrder/tempOrder",
      });
      return
    }
    console.log(res.data.business?.user?.mobile);
    const userInfo = await globalStore.getUserInfo();

    if (res.data.business && (res.data.business.user.mobile != userInfo.mobile)) {
      // 先清空本地设备
      globalStore.setTempDevice()
      globalStore.setWaterTempDevice()
      if (res.data.type == 2) {
        // 水表
        globalStore.setWaterTempDevice(res.data)
        // 水表设备查询
        if (globalStore.waterTempDeviceList.length) {
        const houseId = globalStore.waterTempDeviceList[globalStore.waterTempDeviceList.length - 1].house_id
        request
          .get({
            url: "house/" + houseId,
          })
          .then((res) => {
            if (res.data.device.length) {
              const dianDevice = res.data.device.filter(item => item.type != 2)
              const waterDevice = res.data.device.filter(item => item.type == 2)
              tentDianHouseDevices.value = dianDevice.map(item => {
                return {
                  ...item,
                  house: res.data.house,
                }
              });
              tentWaterHouseDevices.value = waterDevice.map(item => {
                return {
                  ...item,
                  house: res.data.house,
                }
              });
              // 如果有电表直接覆盖
              if (tentDianHouseDevices.value.length) {
                globalStore.setTempDevice(tentDianHouseDevices.value[0])
              }
              console.log(tentDianHouseDevices.value, "tentDianHouseDevices");
              console.log(tentWaterHouseDevices.value, "tentWaterHouseDevices");
            }
          });
        }
      } else {
        // 电表
        globalStore.setTempDevice(res.data)
        tempDevice.value = res.data;
        // 电表设备查询
        if (globalStore.tempDeviceList.length) {
            const houseId = globalStore.tempDeviceList[globalStore.tempDeviceList.length - 1].house_id
            request
              .get({
                url: "house/" + houseId,
              })
              .then((res) => {
                if (res.data.device.length) {
                  const dianDevice = res.data.device.filter(item => item.type != 2)
                  const waterDevice = res.data.device.filter(item => item.type == 2)
                  tentDianHouseDevices.value = dianDevice.map(item => {
                    return {
                      ...item,
                      house: res.data.house,
                    }
                  });
                  tentWaterHouseDevices.value = waterDevice.map(item => {
                    return {
                      ...item,
                      house: res.data.house,
                    }
                  });
                  // 如果有水表直接覆盖
                  if (tentWaterHouseDevices.value.length) {
                    globalStore.setWaterTempDevice(tentWaterHouseDevices.value[0])
                  }
                  console.log(tentDianHouseDevices.value, "tentDianHouseDevices");
                  console.log(tentWaterHouseDevices.value, "tentWaterHouseDevices");
                }
              });
        }
      }
    }
    console.log('okokokoko',res);
    if (res.data.business && (res.data.business.user.mobile != userInfo.mobile)) {
      console.log('不是本人扫码');
      // 不是本人扫码 跳转充值页面
      Taro.navigateTo({
        url: `/pages/tenant/deviceDetail/deviceDetail?sn=${sn}&share=1`,
        success: function (res) {
          isLoadAuto.value = true
          handleChangeWho('tenant')
        }
      })
    }
}

// 查看是否搜集身份信息
const checkIsCollectInfo = () => {
  return new Promise((reslove, reject) => {
  if(globalStore.who != "tenant") return reslove(false)
    request.get({
      url: 'user/checkFast',
    }).then(res => {
      console.log(res);
      let fastSn = res.data?.device?.sn
      // check_status 0 未审核 1 审核通过 2 审核不通过 （|| res.data.check_status == 0）
      const isNeed = res.data.is_need || res.data.check_status == 2
      isNeedShow.value = res.data.is_need || res.data.check_status == 2
      // 有设备sn 去获取
      if(res.data?.device?.sn){
        reject()
        request.get({
          url: 'device/search',
          data: {
            sn: res.data?.device?.sn
          },
          showToast:false
        }).then(response => {
         globalStore.setTempDevice(response.data)
         reslove()
         request.get({
            url:
              "device/" +
              globalStore.tempDeviceList[
                globalStore.tempDeviceList.length - 1
              ].id,
          }).then((res) => {
            reslove()
            if (res.data) {
              tempDevice.value = res.data;
              hasTempDevice.value = true;
            }
            if(unref(qrcodeSn)) return
            if(unref(isNeedShow)) return
            Taro.navigateTo({
              url: `/pages/tenant/deviceDetail/deviceDetail?sn=${fastSn}&share=1'`,
                success: function (res) {
                isLoadAuto.value = true
              }
            })
          });
        })
      }
      // isNeedShow.value = true
      reslove(isNeed)
      if (isNeedShow.value) {
        // 拒绝审核 重新提交
        if (res.data?.need_type == 2 && res.data.check_status == 2) {
          return Taro.showModal({
            title: '温馨提示',
            showCancel: false,
            content: '您的入住信息审核不通过,请检查信息后重新提交!',
            success: function (res) {
              isLoadAuto.value = true
              // if (res.confirm) {
                console.log('用户点击确定')
                Taro.navigateTo({
                  url:"/pages/tenant/tenantGongAnInfo/tenantGongAnInfo"
                })
              // } else if (res.cancel) {
              //   console.log('用户点击取消')
              // }
            }
          })
        }
        if (res.data?.need_type && res.data?.need_type == 2) {
          // 公安采集
          return Taro.showModal({
            title: '温馨提示',
            showCancel: false,
            content: '应公安监管部门的要求，请完成入住人员信息登记，感谢您的配合。',
            success: function (res) {
              isLoadAuto.value = true
              // if (res.confirm) {
                console.log('用户点击确定')
                Taro.navigateTo({
                  url:"/pages/tenant/tenantGongAnInfo/tenantGongAnInfo"
                })
              // } else if (res.cancel) {
              //   console.log('用户点击取消')
              // }
            }
          })
        } else {
          // 普通采集
        return Taro.showModal({
          title: '温馨提示',
          showCancel: false,
          content: '应监管部门的要求，请完成入住人员身份信息登记，感谢您的配合。',
          success: function (res) {
            isLoadAuto.value = true
            // if (res.confirm) {
              console.log('用户点击确定')
              Taro.navigateTo({
                url:"/pages/tenant/tenantInfo/tenantInfo"
              })
            // } else if (res.cancel) {
            //   console.log('用户点击取消')
            // }
          }
        })
        }
      }
      if (res.data.now_count > res.data.need_count) {
        isNeedShow.value = true
        // 如果认证的人数大于需要认证的数量提示用户去删除房客
        return Taro.showModal({
          title: '温馨提示',
          showCancel: false,
          content: '您当前入住人数比已认证的人数有所减少,请删除部分房客',
          success: function (res) {
            isLoadAuto.value = true
            // if (res.confirm) {
              console.log('用户点击确定')
              Taro.navigateTo({
                url:"/pages/tenant/tenantInfo/tenantInfo"
              })
            // } else if (res.cancel) {
            //   console.log('用户点击取消')
            // }
          }
        })
      }
    }).catch(() => {
      reslove(false)
    })
  })

}


useLoad((queryS) => {
  console.log(queryS,"queryS");
  // const scancode_time = parseInt(query.scancode_time) // 获取用户扫码时间 UNIX 时间戳
  let query = decodeURIComponent(queryS.q) // 获取到二维码原始链接内容
  const queryString = query;
  const params = {};

  queryString.substr(queryString.indexOf('?') + 1)
    .split('&')
    .forEach(param => {
      const [key, value] = param.split('=');
      params[key] = decodeURIComponent(value);
    });
    console.log(params.sn);
    qrcodeSn.value = params.sn
  if (globalStore.who == "tenant" && isAp) {
      qrcodeSn.value = Taro.getStorageSync('qrcodeSn') || params.sn
      Taro.getStorageSync('qrcodeSn') && Taro.removeStorageSync('qrcodeSn')
    } else if(globalStore.who != "tenant" && isAp) {
      qrcodeSn.value = Taro.getStorageSync('qrcodeSn') || ''
      Taro.getStorageSync('qrcodeSn') && Taro.removeStorageSync('qrcodeSn')
    }
    if (params.sn) {
    emtyShow.value = false
    if (!globalStore.isLogin) {
        showLogin.value = true;
      }
    }
  if (!globalStore.isLogin) {
    // Taro.navigateTo({
      //   url: '/pages/login/login'
      // })
      // showLogin.value = true;
      return;
    }
  // DeviceScanCodeHandel()
});



useReachBottom(() => {
  console.log("bottom");
  if (!isLastPage.value) {
    params.value.page += 1;
    getList();
  }
});

let setTime = ref(null)

useDidHide(() => {
  clearTimeout(setTime.value)
  // isShowSwitchTag.value = false
})

const isOneShow = ref(false)

const tempList = ref([])

// 查询临时用电订单
const getTempOrder = () => {
  request.get({
    url: 'order/tempList',
    data: {
      page: 1,
      pageSize:10
    }
  }).then(res => {
    console.log(res,"tempList");
    tempList.value = res.data.data
  })
}


useDidShow(async () => {
  console.log(globalStore.homeHasChange, "globalStore.homeHasChange");
  clearInterval(globalStore.countdown)
  isLastPage.value = false
  if (!globalStore.isLogin) {
    tenant.value = {
      contract: false,
      myContractIndex: globalStore.myContractIndex,
      bills: [],
    };
    hasTempDevice.value = false;
    items.value = [];
    if (isOneShow.value) {
      showLogin.value = true;
    }
    isOneShow.value = true
    isLoadAuto.value = false
    return;
  }
  showLogin.value = false;
  fetch();
  // fetchDevics();
  console.log(globalStore.indexPageScrollTop, "globalStore.indexPageScrollTop");

  // 局部更新
  if (globalStore.indexPageScrollTop && globalStore.indexRecharegeId) { 
    console.log(globalStore.indexRecharegeId,"globalStore.indexRecharegeId");
    console.log(globalStore.indexPageScrollTop, "globalStore.indexPageScrollTop");
    console.log("局部刷新");
    
    
    request.get({
        url: 'device/' + globalStore.indexRecharegeId
    }).then(res => {
      console.log(res.data);
      // 根据device数组平铺数据
      const transItmes = computed(() => {
        const data = items.value;
        const result = [];

        data.forEach(item => {
          if (item.device.length > 0) {  // 处理device为空数组的情况
            item.device.forEach(device => {
              result.push({ ...item, device: [device] });
            });
          } else {
            result.push({ ...item, device: [] }); // 保持device为空数组
          }
        });
        // console.log(result,"result");
        return result;
      })
      // console.log(items.value,"items.value");
      // console.log(transItmes.value,"transItmes.value");

      // const curIdx = transItmes.value.findIndex(item => item.device[0].id == res.data.id)
      const curIdx = transItmes.value.findIndex(item => 
        item.device.some(device => device.id == res.data.id)
      );
      // console.log(transItmes.value[curIdx].id,"transItmes[curIdx]");
      const curHouseIdx = items.value.findIndex(item => item.id == transItmes.value[curIdx].id)
      const curDeviceIdx = items.value[curHouseIdx].device.findIndex(item => item.id == res.data.id)
      // console.log(items.value[curHouseIdx],"items[curHouseIdx]");
      // console.log(items.value[curHouseIdx].device[curDeviceIdx],"items[curHouseIdx].device[curDeviceIdx]");
      items.value[curHouseIdx].device[curDeviceIdx] = res.data
    })
      // items.value = globalStore.indexCatcheList;
      // Taro.pageScrollTo({
      //   scrollTop: globalStore.indexPageScrollTop,
      //   duration: 0,
      // });
  } else {
   fetchDevics();
  }
  if (globalStore.who !== "tenant") {
    getCount();
  } else {
    // 电表设备查询
    if (globalStore.tempDeviceList.length) {
      const houseId = globalStore.tempDeviceList[globalStore.tempDeviceList.length - 1].house_id
      request
        .get({
          url: "house/" + houseId,
        })
        .then((res) => {
          if (res.data.device.length) {
            const dianDevice = res.data.device.filter(item => item.type != 2)
            const waterDevice = res.data.device.filter(item => item.type == 2)
            tentDianHouseDevices.value = dianDevice.map(item => {
              return {
                ...item,
                house: res.data.house,
              }
            });
            tentWaterHouseDevices.value = waterDevice.map(item => {
              return {
                ...item,
                house: res.data.house,
              }
            });
            // 如果有水表直接覆盖
            if (tentWaterHouseDevices.value.length) {
              globalStore.setWaterTempDevice(tentWaterHouseDevices.value[0])
            }
            console.log(tentDianHouseDevices.value, "tentDianHouseDevices");
            console.log(tentWaterHouseDevices.value, "tentWaterHouseDevices");
          }
        });
    } else
    // 水表设备查询
    if (globalStore.waterTempDeviceList.length) {
      const houseId = globalStore.waterTempDeviceList[globalStore.waterTempDeviceList.length - 1].house_id
      request
        .get({
          url: "house/" + houseId,
        })
        .then((res) => {
          if (res.data.device.length) {
            const dianDevice = res.data.device.filter(item => item.type != 2)
            const waterDevice = res.data.device.filter(item => item.type == 2)
            tentDianHouseDevices.value = dianDevice.map(item => {
              return {
                ...item,
                house: res.data.house,
              }
            });
            tentWaterHouseDevices.value = waterDevice.map(item => {
              return {
                ...item,
                house: res.data.house,
              }
            });
            // 如果有电表直接覆盖
            if (tentDianHouseDevices.value.length) {
              globalStore.setTempDevice(tentDianHouseDevices.value[0])
            }
            console.log(tentDianHouseDevices.value, "tentDianHouseDevices");
            console.log(tentWaterHouseDevices.value, "tentWaterHouseDevices");
          }
        });
    }
  }

  if (globalStore.homeHasChange) {
    fetch();
    globalStore.homeHasChange = false;
  }
  showPay.value = false;

  const { new_message_count } = await globalStore.getUserInfo();
  if (new_message_count > 0) {
    isNewMsg.value = true;
    // globalStore.setNewMsgShow(true);
  } else {
    isNewMsg.value = false;
    globalStore.setNewMsgShow(true);
  }
  // qrcodeSn.value = Taro.getStorageSync('qrcodeSn') || qrcodeSn.value
  // Taro.getStorageSync('qrcodeSn') && Taro.removeStorageSync('qrcodeSn')
  if (globalStore.who == "tenant" && isAp) {
      qrcodeSn.value = Taro.getStorageSync('qrcodeSn') || qrcodeSn.value
      Taro.getStorageSync('qrcodeSn') && Taro.removeStorageSync('qrcodeSn')
    } else if(globalStore.who != "tenant" && isAp) {
      // qrcodeSn.value = ''
      qrcodeSn.value = Taro.getStorageSync('qrcodeSn') || ''
      Taro.getStorageSync('qrcodeSn') && Taro.removeStorageSync('qrcodeSn')
    }
  console.log(isLoadAuto.value, "isLoadAuto");
  console.log(qrcodeSn.value, "qrcodeSn");
  if (!unref(isLoadAuto) ) {
   await checkIsCollectInfo()
   await autoJumpRecharge()
  }
  if (!unref(isLoadAuto) && unref(qrcodeSn)) {
    console.log('进来');
    setTime.value = setTimeout(() => {
      console.log(qrcodeSn.value);
      DeviceScanCodeHandel(qrcodeSn.value)
    },500)
  }
});

usePullDownRefresh(async () => {
  console.log("onPullDownRefresh");
  isLastPage.value = false
  Taro.stopPullDownRefresh();
  fetch();
  if (!globalStore.isLogin) return;
  fetchDevics();
  getCount();

  const { new_message_count } = await globalStore.getUserInfo();
  if (new_message_count > 0) {
    isNewMsg.value = true;
    // globalStore.setNewMsgShow(true);
  } else {
    isNewMsg.value = false;
    globalStore.setNewMsgShow(true);
  }
});

useShareAppMessage(() => {
  console.log("onShare");
  return {
    title: "闪租婆 - 让租赁更智能化",
  };
});

const showLogin = ref(false);
const onLoginClose = () => {
  showLogin.value = false;
};
const onLogin = async () => {
  showLogin.value = false;
  fetch();
  fetchDevics();
  getCount();
  if (!unref(isLoadAuto)) {
   await checkIsCollectInfo()
   await autoJumpRecharge()
  }
  if (!unref(isLoadAuto) && unref(qrcodeSn)) {
    DeviceScanCodeHandel(qrcodeSn.value)
  }
};

const onJump = (path) => {
  if (!globalStore.isLogin) {
    // Taro.navigateTo({
    //   url: '/pages/login/login'
    // })
    showLogin.value = true;
    return;
  }
  Taro.navigateTo({
    url: path,
  });
};

const onJumpPublic = (path) => {
  Taro.navigateTo({
    url: path,
  });
};

const onMoreDevice = (houseId) => {
  Taro.navigateTo({
    url: "/pages/tenant/device/device?houseId=" + houseId,
  });
};
const onMoreTempDevice = () => {
  Taro.navigateTo({
    url: "/pages/tenant/tempDevice/tempDevice",
  });
};

const onMoreHouse = () => {
  Taro.navigateTo({
    url: "/pages/tenant/house/house",
  });
};

// 查看正在进行的订单
const lookOrder = () => {
  console.log("查看订单");
  Taro.navigateTo({
    url: "/pages/tenant/tempOrder/tempOrder",
  });
}

const onBindDevice = async() => {
  if (!globalStore.isLogin) {
    showLogin.value = true;
    return;
  }
  try {
    Taro.showLoading({
      mask: true
    })
    await globalStore.getUserInfo();
  } catch (_) {}
   finally {
    Taro.hideLoading()
  }
  globalStore.setIndexPageScrollTop(0)
  globalStore.setIndexRecharegeId(null)
  // globalStore.setIndexCatcheList(null)
  Taro.scanCode({
    scanType: "qrcode",
    success: (res) => {
      console.log(res, "scan result");
      if (!res.result) {
        Taro.showToast({
          title: "扫码失败",
          icon: "error",
        });
        return;
      }
      let sn = res.result.trim();
      sn.replace(/[\r\n]/g, "");
      if (sn.substring(0, 5) === "https") {
        let _arr = res.result.split("sn=");
        console.log(_arr);
        if (_arr.length > 0) {
          sn = _arr[1];
        }
      }
      // TODO先查询是否是绑定过的设备
      request.get({
        url: 'device/search',
        data: {
          sn
        },
        showLoading:true,
        showToast:true
      }).then(res => {
        // console.log(res, "res111111");
        if (res.data && res.data.business) {
          qrcodeSnDetail.value = res.data
          return showBingding.value  = true;
        } else {
          if (res.data.type == 2) {
            // showWaterScan.value = true;
            Taro.navigateTo({
              url:
                `/pages/house/addWaterDevice/addWaterDevice?sn=${sn}`,
            });
            return
            // Taro.showToast({
            //   title: '请添加电表类型设备！',
            //   icon: 'none',
            //   duration:5000
            // })
          }
          Taro.navigateTo({
            url: "/pages/bindDevice/bindDevice?sn=" + sn,
          });
        }
      })
      
    },
  });
};

const onDeviceRecharge = () => {
  if (tenant.value.contract.dianbiao) {
    Taro.navigateTo({
      url:
        "/pages/tenant/deviceDetail/deviceDetail?id=" +
        tenant.value.contract.dianbiao.id,
    });
  }
};
const onDeviceDetail = (id) => {
  // Taro.navigateTo({
  //   url: "/pages/tenant/deviceDetail/deviceDetail?id=" + id,
  // });
};

const conf = ref({});

const payData = ref({
  billId: [],
  amount: 0,
  rate: 0,
  fee: 0,
});
const showPay = ref(false);
const onShowPayOne = (item) => {
  console.log(item, payData.value);
  payData.value.billId = [item.id];
  payData.value.amount = item.amount;
  payData.value.fee = toFixed(
    payData.value.rate * parseFloat(payData.value.amount),
    2
  );
  payData.value.amount =
    parseFloat(item.amount) + parseFloat(payData.value.fee);
  changeShowPay();
};
const changeShowPay = () => {
  showPay.value = !showPay.value;
};
const onPay = () => {
  request
    .post({
      url: "payment/billPay",
      data: {
        billId: payData.value.billId,
        amount: payData.value.amount,
        fee: payData.value.fee,
        pay_channel:
          process.env.TARO_ENV === "alipay" ? "alipay_lite" : "wx_lite",
      },
      showToast:false
    })
    .then((res) => {
      console.log(res, "payment params");
      let payConfig = JSON.parse(res.data.expend.pay_info);
      if (process.env.TARO_ENV === "weapp") {
        wx.requestPayment({
          timeStamp: payConfig.timeStamp,
          nonceStr: payConfig.nonceStr,
          package: payConfig.package,
          signType: payConfig.signType,
          paySign: payConfig.paySign,
          success() {
            console.log("success");
            Taro.showToast({
              title: "支付成功！",
            });
            // 刷新列表
            fetch();
          },
          fail() {
            console.log("fail");
            Taro.showToast({
              title: "支付失败",
              icon: "error",
            });
          },
        });
      }
      if (process.env.TARO_ENV === "alipay") {
        my.tradePay({
          // 调用统一收单交易创建接口（alipay.trade.create），获得返回字段支付宝交易号 trade_no
          tradeNO: payConfig.tradeNO,
          success: (res) => {
            console.log("success", res);
            if (res.resultCode === 9000) {
              Taro.showToast({
                title: "支付成功！",
              });
              // 刷新列表
              fetch();
            } else {
              Taro.showToast({
                title: "支付失败",
                icon: "error",
              });
            }
          },
          fail: (res) => {
            console.log("fail", res);
            Taro.showToast({
              title: "支付失败",
              icon: "error",
            });
          },
        });
      }
    }).catch(err => {
      console.log(err);
      if (err.data.code != 200) {
        yToastText.value = err.data.message
        showToast.value = true
      }
    })
};
</script>
