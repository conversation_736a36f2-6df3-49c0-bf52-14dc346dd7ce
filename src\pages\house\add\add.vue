<template>
  <view>
    <view v-show="!success">
      <view class="house-form">
        <!-- <MyCell prefix="所在地" required arrow suffix="请选择"></MyCell> -->
        <MyCell
          prefix="小区名"
          required
          arrow
          :suffix="formState.estate_name ? '' : '请选择'"
          @tap="onChooseLocation"
          align="right"
        >
          <template #content>{{ formState.estate_name }}</template>
        </MyCell>
        <MyCell
          prefix="分组"
          required
          v-if="globalStore.isAdmin"
          suffixWidth="200"
          arrow
          :suffix="selectClassNames.join(',') ? '' : '请选择分组'"
          @tap="classShow = true"
          align="right"
        >
          <template #content>{{ selectClassNames.join(',') }}</template>
        </MyCell>
        <MyCell prefix="房源名" required align="right">
          <template #content>
            <input
              name="name"
              v-model="formState.name"
              placeholder="几栋几单元几室"
              :cursor="posCur"
              @input="bindReplaceInput"
            />
          </template>
        </MyCell>
        <MyCell prefix="户型" align="right">
          <template #content>
            <view class="flex flex-v-center flex-right">
              <input
                type="number"
                name="fang"
                class="mini-input"
                v-model="formState.fang"
                placeholder="1"
               :cursor="posCur"
               @input="bindReplaceInput"
              />
              <view class="mini-txt">房 /</view>
              <input
                type="number"
                name="ting"
                class="mini-input"
                placeholder="1"
                v-model="formState.ting"
                :cursor="posCur"
               @input="bindReplaceInput"
              />
              <view class="mini-txt">厅 /</view>
              <input
                type="number"
                name="wei"
                class="mini-input"
                placeholder="1"
                v-model="formState.wei"
               :cursor="posCur"
              @input="bindReplaceInput"
              /><view class="mini-txt">卫 /</view>
              <input
                type="number"
                name="wei"
                class="mini-input"
                placeholder="0"
                v-model="formState.chu"
                :cursor="posCur"
               @input="bindReplaceInput"
              /><view class="mini-txt">厨</view>
            </view>
          </template>
        </MyCell>
        <MyCell prefix="参考租金" align="right" suffix="元/月">
          <template #content>
            <input
              type="digit"
              name="rent"
              v-model="formState.rent"
              placeholder="5000.00"
              :cursor="posCur"
              @input="bindReplaceInput"
            />
          </template>
        </MyCell>
        <MyCell prefix="总面积" align="right" suffix="㎡">
          <template #content>
            <input
              type="digit"
              name="rent"
              v-model="formState.size"
              placeholder="100"
              :cursor="posCur"
              @input="bindReplaceInput"
            />
          </template>
        </MyCell>
      </view>
      <view class="footer-fixed">
        <button class="btn-add m33" @tap="handleSubmit">创建房源</button>
      </view>
    </view>
    <view v-show="success" class="result">
      <view><MyIcon icon="img-house-s" width="188rpx" height="193rpx"></MyIcon></view>
      <view class="result-t">房源录入成功！</view>
      <view class="result-st">在房源列表中可以查看房源详情</view>
      <view class="result-btn">
        <view><button class="btn-primary" @tap="onContinueAdd">继续录入</button></view>
        <view><button class="btn-second" @tap="onHouseDetail">查看房源</button></view>
      </view>
    </view>
    <ClassPopUp 
      :classShow="classShow" 
      @close="classShow = false"
      @confirm="onConfirm"
    />
  </view>
</template>
<script setup>
import { ref } from "vue";
import "./add.scss";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  usePullDownRefresh,
  useReachBottom,
  useLoad
} from "@tarojs/taro";
import request from "@/utils/request";
import { debounce } from "lodash";

import MyInput from "@/components/MyInput";
import MyCell from "@/components/MyCell";
import MyIcon from "@/components/MyIcon";
import MyPopup from "@/components/MyPopup";
import ClassPopUp from "@/components/ClassPopUp";


import { useGlobalStore } from "@/stores";
import { validation } from "@/utils/validation";
const globalStore = useGlobalStore();

const posCur = ref(-1)

const bindReplaceInput = (e) => {
  let pos = e.detail.cursor
  posCur.value = pos
}

const classShow = ref(false)

const checkedList = ref([]);

const selectClassNames = ref([])

const formState = ref({
  estate_name: "",
  name: "",
  province: "",
  city: "",
  district: "",
  fang: 1,
  ting: 1,
  wei: 1,
  chu: 1,
  size: 0,
  rent: 0,
});


const onConfirm = (val,val2) => {
  selectClassNames.value = val;
  checkedList.value = val2;
}

const handleSubmit = debounce(function () {

  validation(formState.value, {
    estate_name: {
      type: 'required',
      message: '请输入小区名'
    },
    name: {
      type: 'required',
      message: '请输入房源名'
    }
  }).then(() => {
    if (globalStore.isAdmin) {
      if (selectClassNames.value.length == 0) {
        return Taro.showToast({
          title: "请选择分类",
          icon: "error",
        })
      }
    }
    request
    .post({
      url: "house/create",
      data: {
        house: formState.value,
        classId: selectClassNames.value.length ? JSON.stringify(checkedList.value) : '',
      },
    })
    .then((data) => {
      result.value = data.data
      success.value = true
    });
  }).catch((err) => {
    Taro.showToast({
      title: err,
      icon: "error",
    });
  })

}, 500);

const onHouseDetail = () => {
  Taro.redirectTo({
    url: "/pages/house/detail/detail?id=" + result.value.id,
  });
}
const onContinueAdd = () => {
  success.value = false
  formState.value = {
    estate_name: formState.value.estate_name,
    name: "",
    province: formState.value.province,
    city: formState.value.city,
    district: formState.value.district,
    fang: 1,
    ting: 1,
    wei: 1,
    chu: 1,
    size: 0,
    rent: 0,
  }
}

  // 防止精度丢失
  function addDecimals(a, b) {
		let multiplier = Math.pow(10, 10); // 选择一个适当的倍数
		let intA = Math.round(a * multiplier);
		let intB = Math.round(b * multiplier);
		let result = (intA + intB) / multiplier;
		return result;
	}

const onChooseLocation = () => {
  Taro.chooseLocation({
        success: (res) => {
          console.log(res)
          formState.value.estate_name = res.name

          // let reg = /.+?(省|市|自治区|自治州|县|区)/g
          let reg = /.+?(省|市|自治区|自治州|县|区|镇|乡|街道)/g;
          const area = res.address.match(reg)
          formState.value.province = area[0]
          formState.value.city = area[1]
          formState.value.district = area[2]
          formState.value.country = area[3] || ''
          formState.value.lat = res.latitude
          formState.value.lng = res.longitude
        }
        })
        return
  Taro.showLoading({
    title: "正在获取当前位置",
  });
  Taro.getLocation({
    isHighAccuracy: true,
    success: (local) => {
      console.log(local);
      Taro.hideLoading();
      if (!formState.value.estate_name) {
        // getAddress(local.latitude, local.longitude);
          // 微信有偏差需要处理
          if (process.env.TARO_ENV === 'weapp') {
            getAddress(addDecimals(local.latitude, 0.000312), addDecimals(local.longitude, 0.006611))
          } else {
            getAddress(local.latitude, local.longitude)
          }
        return;
      }
      Taro.chooseLocation({
        // latitude: local.latitude,
        // longitude: local.longitude,
        success: (res) => {
          console.log(res);
          formState.value.estate_name = res.name;

          let reg = /.+?(省|市|自治区|自治州|县|区)/g;
          const area = res.address.match(reg);
          formState.value.province = area[0];
          formState.value.city = area[1];
          formState.value.district = area[2];
        },
      });
    },
    fail: () => {
      Taro.hideLoading();
    },
  });
};

const getAddress = (latitude, longitude) => {
  request
    .get({
      url: "getAddress",
      data: {
        location: latitude + "," + longitude,
      },
    })
    .then((res) => {
      console.log(res.data);
      if (res.data.status === 0) {
        formState.value.estate_name =
          res.data.result.formatted_addresses.recommend;
        formState.value.province = res.data.result.address_component.province;
        formState.value.city = res.data.result.address_component.city;
        formState.value.district = res.data.result.address_component.district;
        console.log(formState);
      }
    });
};

const result = ref({})
const success = ref(false)

</script>
