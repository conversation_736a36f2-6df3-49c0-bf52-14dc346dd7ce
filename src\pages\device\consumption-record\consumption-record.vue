<script setup>
import { ref, unref, watch } from "vue";

import Line from "../components/line/index.vue";

import request from "@/utils/request";

import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";

const active = ref(0);

const dayjs = require("dayjs");

const arr = ref(["日", "月", "年"]);

const data = ref();

const isUpdate = ref(false);

const dateSel = ref(dayjs().format("YYYY-MM-DD")); //.slice(0, 7)

const deveId = ref();

const list = ref();

const day = ref(1);

const month = ref(1);

const year = ref(1);

const dayr = ref(1);

const monthr = ref(1);

const yearr = ref(1);

const flag = ref(false);

const device_type = ref(1)

const dataObj = ref({
  dataKey: [],
  dataValue: [],
}); //图表数据

useLoad((query) => {
  console.log(query.id);
  deveId.value = query.id;
  device_type.value = query.type;
  if(query.type == 2){
    Taro.setNavigationBarTitle({
      title: "用水记录",
    })
  }
  getData(query.id, "day");
});

usePullDownRefresh(async () => {
  const unitMap = ["day", "month", "year"];

  await getData(unref(deveId), unitMap[unref(active)]);
  Taro.stopPullDownRefresh();
});

watch(active, async (newVal) => {
  day.value = 1;
  month.value = 1;
  year.value = 1;

  dayr.value = 1;
  monthr.value = 1;
  yearr.value = 1;

  if (newVal == 0) {
    // 日
    dateSel.value = dayjs().format("YYYY-MM-DD");
  } else if (newVal == 1) {
    // 月
    dateSel.value = dayjs().format("YYYY-MM");
  } else {
    // 年
    dateSel.value = dayjs().format("YYYY");
  }

  await getData(
    unref(deveId),
    newVal === 0 ? "day" : newVal === 1 ? "month" : "year"
  );
});

const changeDateAndGetData = async (offset, unit) => {
  if (unref(flag))
    return Taro.showToast({
      title: "不要点太快哦~",
      icon: "none",
      duration: 1000,
    });
  flag.value = true;
  const newDate = dayjs(unref(dateSel)).add(offset, unit);

  const formatMap = {
    day: "YYYY-MM-DD",
    month: "YYYY-MM",
    year: "YYYY",
  };

  const format = formatMap[unit];

  console.log(newDate.format(format));
  dateSel.value = newDate.format(format);

  const period = unit; //('day', 'month', 'year')
  await getData(unref(deveId), period);
  // ------**防止切换太快图表渲染闪烁**------
  setTimeout(() => {
    flag.value = false;
  }, 600);
};

const handleDateChange = (direction) => {
  const offset = direction === "left" ? -1 : 1;

  const unitMap = ["day", "month", "year"];
  const activeValue = unref(active);
  const unit = unitMap[activeValue] || "day";

  changeDateAndGetData(offset, unit);
};

// 左侧按钮处理
const leftHandel = () => handleDateChange("left");

// 右侧按钮处理
const rightHandel = () => handleDateChange("right");

// 获取数据
const getData = async (device_id, type) => {
  isUpdate.value = false;
  dataObj.value.dataKey = [];
  dataObj.value.dataValue = [];
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "user/getPowers",
        data: {
          device_id: device_id || unref(deveId),
          type: type || "day",
          time: unref(dateSel),
          device_type: device_type.value == 2 ? 4 : 3,
        },
      })
      .then((res) => {
        data.value = res.data;
        list.value = res.data.table;
        dataObj.value.dataValue = res.data.data;

        switch (active.value) {
          case 0:
            // 日
            dataObj.value.dataKey = res.data.keys.map((item) =>
              item.split(" ")[1].slice(0, 5)
            );
            break;
          case 1:
          case 2:
            // 月 和 年 使用相同的处理逻辑
            dataObj.value.dataKey = res.data.keys.map((item) => item.slice(5));
            break;
        }

        isUpdate.value = true;
        resolve();
      })
      .catch(() => {
        reject();
      });
  });
};
</script>
<template>
  <view class="haodian-container">
    <view class="utils-box">
      <view
        v-for="(item, idx) in arr"
        :key="idx"
        class="li"
        :class="{ active: active === idx }"
        @tap="active = idx"
      >
        {{ item }}
      </view>
    </view>
    <!-- 图表 -->
    <view class="chart-box">
      <view class="charts">
        <Line :dataObj="dataObj" :isUpdate="isUpdate" :deviceType="device_type"/>
      </view>
    </view>
    <view class="content">
      <view class="utils">
        <view class="iconfont icon-zuojiantou" @tap="leftHandel"></view>
        <view class="date">{{ dateSel.replaceAll("-", ".") }}</view>
        <view class="iconfont icon-youjiantou" @tap="rightHandel"></view>
      </view>
      <view class="list">
        <view class="li" v-for="(item, idx) in list" :key="idx">
          <view class="tit" v-if="device_type != 2"> {{ Number(item.power).toFixed(3) }} (度) </view>
          <view class="tit" v-else> {{ Number(item.power).toFixed(3) }} (吨) </view>
          <view>
            {{
              active == 0
                ? item.start.split(" ")[1]?.slice(0, 5)
                : active == 1
                ? item.start.replaceAll("-", "/")
                : item.start.replaceAll("-", "/").split("/")[0] +
                  "/" +
                  item.start.replaceAll("-", "/").split("/")[1]
            }}
          </view>
          <view class="fenge" style="width: 0"> ~ </view>
          <view>
            {{
              active == 0
                ? item.end.split(" ")[1]?.slice(0, 5)
                : active == 1
                ? item.end.replaceAll("-", "/")
                : item.end.replaceAll("-", "/").split("/")[0] +
                  "/" +
                  item.end.replaceAll("-", "/").split("/")[1]
            }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.haodian-container {
  .utils-box {
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 100px;
    background-color: #1352fd;
    .li {
      position: relative;
      color: #ffffff6d;
      font-size: 40px;
      &.active {
        color: #fff;

        &::after {
          display: block;
          content: "";
          width: 30px;
          height: 4px;
          background-color: #fff;
          position: absolute;
          bottom: -10px;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }
  .chart-box {
    background: linear-gradient(180deg, #1352fd 51%, #ffffff 100%);
  }
  .charts {
    margin: 0 auto;
    width: 715px;
    height: 530px;
    background: #ffffff;
    box-shadow: 0px 7px 29px 0px rgba(112, 145, 178, 0.2);
    border-radius: 20px;
    // margin-left: -20px;
    overflow: hidden;
    canvas {
      margin: 0;
      margin-left: -16px;
    }
  }
  .content {
    margin-top: 40px;
    .utils {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 690px;
      height: 88px;
      background: #f2f5f7;
      border-radius: 15px;
      margin: 0 auto;
      .date {
        margin: 0 10px;
      }
      .iconfont {
        font-size: 30px;
        color: #6c82c0;
        &:active {
          color: #ccc;
        }
      }
    }
    .list {
      .li {
        display: flex;
        justify-content: space-around;
        border-bottom: 1px solid #ececec;
        padding: 20px 0;
        font-size: 30px;
        > view {
          color: #b6bec5;
          width: 150px;
          text-align: center;
        }
        .tit {
          color: #000;
        }
      }
    }
  }
}
</style>
