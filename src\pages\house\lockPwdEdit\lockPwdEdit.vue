<script setup>
import MyIcon from "@/components/MyIcon";
import request from "@/utils/request";
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import { onMounted, ref, unref } from "vue";
import { modifyPwdHandel } from "@/utils/lock";
import { useGlobalStore } from "@/stores";
const globalStore = useGlobalStore();

const query = ref();
const keyboardPwd = ref("");
const keyboardPwdCofirm = ref("");

const eysclose = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/eys-close.png";
const eysopen = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/eys-open.png";
const tip = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/tip.png";
const tip2 = "https://yimits.oss-cn-beijing.aliyuncs.com/picss/tip-b.png";


useLoad((q) => {
  console.log("load");
  query.value = JSON.parse(q.item);
  console.log(query.value);
});

const isOpen = ref(false);
const isOpen2 = ref(false);

const clickEysHandel = () => {
  isOpen.value = !isOpen.value;
};

const clickEysHandel2 = () => {
  isOpen2.value = !isOpen2.value;
};

const clickEditHandel = () => {
  if (
    unref(keyboardPwd).trim() == "" ||
    unref(keyboardPwdCofirm).trim() == ""
  ) {
    Taro.showToast({
      title: "请输入密码",
      icon: "none",
    });
    return;
  }
  if (
    unref(keyboardPwd).trim().length != 6 ||
    unref(keyboardPwdCofirm).trim().length != 6
  ) {
    Taro.showToast({
      title: "请输入6位密码",
      icon: "none",
    });
    return;
  }
  if (unref(keyboardPwd).trim() != unref(keyboardPwdCofirm).trim()) {
    Taro.showToast({
      title: "两次输入的密码不一致",
      icon: "none",
    });
    return;
  }
  modifyPwdHandel({
    oldKeyboardPwd: query.value.keyboardPwd,
    keyboardPwd: unref(keyboardPwd),
    keyInfo: globalStore.KEYINFO,
    keyboardPwdId: query.value.keyboardPwdId,
    lockId: query.value.lockId,
  }).then((res) => {
    console.log(res);
    Taro.showToast({
      title: "修改成功",
      icon: "none",
    });
    setTimeout(() => {
      Taro.switchTab({
        url: "/pages/index/index",
      });
    }, 1200);
  });
};
</script>

<template>
  <view class="pwd-edit-lock">
    <view class="cell">
      <view class="tit">设置新密码</view>
      <view class="ipt">
        <input
          maxlength="6"
          placeholder="请输入新密码"
          :type="isOpen ? 'text' : 'password'"
          v-model="keyboardPwd"
          :key="isOpen"
        />
        <image
          :src="isOpen ? eysclose : eysopen"
          mode="aspectFit"
          @tap="clickEysHandel"
        ></image>
      </view>
    </view>
    <view class="cell">
      <view class="tit">确认新密码</view>
      <view class="ipt">
        <input
          maxlength="6"
          placeholder="再次输入新密码"
          :type="isOpen2 ? 'text' : 'password'"
          v-model="keyboardPwdCofirm"
          :key="isOpen2"
        />
        <image
          :src="isOpen2 ? eysclose : eysopen"
          mode="aspectFit"
          @tap="clickEysHandel2"
        ></image>
      </view>
    </view>
    <view class="tip-box">
      <view class="one">
        <image :src="tip" mode="aspectFit"></image>
        <text>密码限制为6为数字</text>
      </view>
      <view class="two">
        <image :src="tip2" mode="aspectFit"></image>
        <text>请确认您已靠近门锁</text>
      </view>
    </view>

    <view class="btn">
      <button @tap="clickEditHandel">确认修改</button>
    </view>
  </view>
</template>

<style lang="less">
page {
  background-color: #f1f3f7;
}
.pwd-edit-lock {
  .cell {
    height: 200px;
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    padding: 30px 40px;
    margin-bottom: 5px;
    .tit {
      font-size: 30px;
      color: #939393;
      margin-bottom: 50px;
    }
    .ipt {
      display: flex;
      justify-content: space-between;
      align-items: center;
      input {
        font-size: 30px;
      }
    }
    image {
      width: 41px;
      height: 28px;
    }
  }
  .tip-box {
    margin-top: 42px;
    font-size: 24px;
    margin-left: 40px;
    > view {
      image {
        width: 20px;
        height: 20px;
      }
    }
    .one {
      color: #939393;
      margin-bottom: 30px;
      text {
        color: #939393;
        margin-left: 10px;
      }
    }
    .two {
      color: #1352fd;
      text {
        color: #1352fd;
        margin-left: 10px;
      }
    }
  }
  .btn {
    width: 100%;
    min-height: 150px;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    // 设置ios刘海屏底部横线安全区域
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    button {
      width: 700px;
      height: 88px;
      background: #1352fd;
      border-radius: 20px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0px -4px 32px 0px rgba(54, 69, 193, 0.24);
    }
  }
}
</style>
