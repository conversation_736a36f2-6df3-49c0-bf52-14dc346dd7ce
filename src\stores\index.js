import { defineStore } from 'pinia'
import Taro from "@tarojs/taro"
import request from '../utils/request'

import { tenant, landlord, curent, landlordAdmin } from '../utils/tabbar'

export const useGlobalStore = defineStore('global', {
  state: () => {
    const token = Taro.getStorageSync('token')
    const tempDeviceList = Taro.getStorageSync('tempDeviceList') || []
    const waterTempDeviceList = Taro.getStorageSync('waterTempDeviceList') || []
    const who = Taro.getStorageSync('who') || 'business'
    let windowInfo = null
    if (process.env.TARO_ENV === 'alipay') {
      windowInfo = my.getSystemInfoSync()
    } else {
      windowInfo = Taro.getWindowInfo()
    }
    console.log(windowInfo)
    console.log(Taro.getSystemInfoSync());
    return {
      who: who,
      isAdmin:false, // 是否是管理员
      userInfo: {},
      isLogin: !!token,
      token: token,
      tempDeviceList: tempDeviceList.filter(item => item?.type != 2),
      waterTempDeviceList: waterTempDeviceList,
      windowInfo: windowInfo,
      systemInfoSync: Taro.getSystemInfoSync(),
      myContractIndex: 0,
      homeHasChange: false, // 首页是否变动
      yjData: null, // 押金设置
      userInfoHasChange: false, // 用户信息是否变动
      device: {
        isConnected: false,
        mac: '',
        deviceId: '',
        serviceId: '',
        charId: '',
        notifyId: '',
        message: []
      },
      listenClient: 0, // 设备消息订阅标记
      tablist: [],//忽略
      activeIndex: 0,//忽略
      isNewMsgShow: true,//消息是否展示
      isTipShow: Boolean(Taro.getStorageSync('isTipShow')) ? true : false,//
      isWxShare: false,//是否微信分享
      countdown: null,//倒计时loading定时器
      cacheLocation: Boolean(Taro.getStorageSync('cacheLocation')) ? JSON.parse(Taro.getStorageSync('cacheLocation')) : '', //缓存的地址
      DevicePrice: Taro.getStorageSync('DevicePrice') ? Taro.getStorageSync('DevicePrice') : null,//缓存的电价
      ServicePrice: Taro.getStorageSync('ServicePrice') ? Taro.getStorageSync('ServicePrice') : null,//缓存的服务费
      waterDevicePrice: Taro.getStorageSync('waterDevicePrice') ? Taro.getStorageSync('waterDevicePrice') : null,//缓存的水表电价
      isGlobVisable: false,//全局弹窗是否显示
      globDialogType: 'TimeLoading',//全局弹窗的类型 PermissionDenied(获取权限失败)  | ConnectionFailed（连接失败） |  TimeLoading（连接倒计时） | BleConnectionTip(蓝牙表链接前提示) | NetWorkErrorTip(4G表无信号时提示) | TimeReFLoading （充值倒计时）| RefFailed 充值失败
      deviceConTime: 30,//s 设备连接超时时间
      deviceRefTime: 30,//s 设备充值超时时间
      KEYINFO: Taro.getStorageSync('KEYINFO') ? JSON.parse(Taro.getStorageSync('KEYINFO')) : null,//锁数据
      ttlaccessToken: Taro.getStorageSync('ttlaccessToken') ? Taro.getStorageSync('ttlaccessToken') : null,//tokenVal
      moreDetail: null,//更多详情
      waterDeviceBleInfo: null,//水表蓝牙信息
      indexPageScrollTop: 0,//首页滚动位置
      indexCatcheList: null,//首页列表缓存
      indexRecharegeId: null,//首页充值id
      isErrorShow:false,//是否显示过错误弹窗
      rechargeTimeSecens: 10, // 充值时间的限流时间s
      rechargeTime: Taro.getStorageSync('rechargeTime') ? Taro.getStorageSync('rechargeTime') : null,// 充值时间-时间戳
      deviceRssi: null,//当前设备的rssi
    }
  },
  actions: {
    setDeviceRssi (rssi) {
      this.deviceRssi = rssi
    },
    setRechargeTime (time) {
      Taro.setStorageSync('rechargeTime', time)
      this.rechargeTime = time
    },
    setErroShow (flag) {
      this.isErrorShow = flag
    },
    setIndexRecharegeId (id) {
      this.indexRecharegeId = id
    },
    setIndexCatcheList (list) {
      this.indexCatcheList = list
    },
    setIndexPageScrollTop (top) {
      this.indexPageScrollTop = top
    },
    setWaterDeviceBleInfo (info) {
      this.waterDeviceBleInfo = info
    },
    setMore (detail) {
      this.moreDetail = detail
    },
    removeTTlToken () {
      this.ttlaccessToken = null
      Taro.removeStorageSync('ttlaccessToken')
    },
    setTtlaccessToken (token) {
      this.ttlaccessToken = token
      Taro.setStorageSync('ttlaccessToken', token)
    },
    setkeyInfo (info) {
      this.KEYINFO = info
      Taro.setStorageSync('KEYINFO', JSON.stringify(info))
    },
    setDeviceTime (time) {
      this.deviceConTime = time
    },
    setDeviceRefTime (time) {
      this.deviceRefTime = time
    },
    setGlobDialog ({show: show, type: dialogType}) {
      this.isGlobVisable = show
      this.globDialogType = dialogType
    },
    setcacheDevicePrice (price) {
      this.DevicePrice = price
      Taro.setStorageSync('DevicePrice', price)
    },
    setcacheServicePrice (price) {
      this.ServicePrice = price
      Taro.setStorageSync('ServicePrice', price)
    },
    setcacheWaterDevicePrice (price) {
      this.waterDevicePrice = price
      Taro.setStorageSync('waterDevicePrice', price)
    },
    setcacheLocation (adressObj) {
      this.cacheLocation = adressObj
      Taro.setStorageSync('cacheLocation', JSON.stringify(adressObj))
    },
    setShare (share) {
      this.isWxShare = share
    },
    setLoadingTime (time) {
      this.countdown = time
    },
    setWho (who) {
      if (who == 'admin') {
        this.who = 'business'
        Taro.setStorageSync('who', 'business')
        this.isAdmin = true
        return
      }
      this.who = who
      this.isAdmin = false
      Taro.setStorageSync('who', who)
    },
    setIsLogin (flag) {
      this.isLogin = flag
      if (!flag) {
        this.token = null
      }
    },
    setTip (show) {
      this.isTipShow = show
      Taro.setStorageSync('isTipShow', show)
    },
    setActive (idx) {
      this.activeIndex = idx
    },
    setNewMsgShow (flag) {
      this.isNewMsgShow = flag
    },
    setRole (who) {
      if (who === 'tenant') {
        if (this.tablist.length) return
        this.tablist = tenant
      } else {
        if (this.tablist.length) return
        this.tablist = landlord
      }
    },
    getUserInfo () {
      return new Promise((resolve, reject) => {
        // if (this.isLogin && this.userInfo.mobile && !this.userInfoHasChange) {
        //   resolve({...this.userInfo,sn:this.userInfo?.sn || null})
        // }
        if (this.isLogin) {
          request.get({
            url: 'user/info',
            showLoading:false
          }).then(res => {
            console.log(res, 'getUserInfo')
            this.setUserInfo(res.data)
            this.userInfoHasChange = false

            if (!this.tablist.length) {
              this.setWho(res.data.type)
              if (res.data.type == 'admin') {
                Taro.setStorageSync("tablist", JSON.stringify(landlordAdmin));
                curent.value = landlordAdmin;
              }else if (res.data.type == 'tenant') {
                Taro.setStorageSync('tablist', JSON.stringify(tenant))
                curent.value = tenant
              } else {
                Taro.setStorageSync('tablist', JSON.stringify(landlord))
                curent.value = landlord
                this.setMore(null)
              }
            }

            if (res.data.new_message_count > 0) {
              if (res.data.type == 'tenant') {
                Taro.showTabBarRedDot({
                  index: 1
                })
              }
            }

            resolve({...res.data,sn:res.data?.sn || null})
          }).catch(e => {
            Taro.clearStorageSync('token')
            this.isLogin = false
            reject(e)
          })
        }
        // resolve(this.userInfo)
      })
    },
    setUserInfo (info) {
      this.userInfo = info
      this.who = info.type
      this.isLogin = true
    },
    setYjData (yjData) {
      this.yjData = yjData
    },
    logout () {
      this.userInfo = {}
      this.isLogin = false
      Taro.clearStorageSync('token')
      this.userInfoHasChange = true
      this.tempDeviceList = []
      Taro.removeStorageSync('tempDeviceList')
      this.ttlaccessToken = null
      Taro.removeStorageSync('ttlaccessToken')
      this.indexPageScrollTop = 0
      this.indexCatcheList = null
      this.indexRecharegeId = null
      this.isAdmin = false
    },
    onConnectedDevice (mac, deviceId, serviceId, charId, notifyId) {
      this.device.mac = mac
      this.device.deviceId = deviceId
      this.device.serviceId = serviceId
      this.device.charId = charId
      this.device.notifyId = notifyId
      this.device.message = []
      this.device.isConnected = true
    },
    onCloseDevice () {
      this.device.isConnected = false
      this.device.deviceId = ''
      this.device.serviceId = ''
      this.device.charId = ''
      this.device.notifyId = ''
      this.device.mac = ''
      this.device.message = []
    },
    onDeviceMessage (msg) {
      console.log(msg, 'onDeviceMessage');
      // this.device.message = [msg]
      this.device.message.push(msg)
    },
    putTempDevice (device) {
      let isExist = false
      this.tempDeviceList.map(d => {
        if (d.id === device.id) {
          isExist = true
        }
      })
      if (!isExist) {
        this.tempDeviceList.push(device)
        Taro.setStorageSync('tempDeviceList', this.tempDeviceList)
      }
    },
    // ***只保存最新的一个 每次更新都替换***
    setTempDevice (device) {
      if(device?.type == 2) return this.setWaterTempDevice(device) //水表 trun
      if (!device) {
        this.tempDeviceList = []
        Taro.setStorageSync('tempDeviceList', this.tempDeviceList)
      } else {
        this.tempDeviceList = [device]
        Taro.setStorageSync('tempDeviceList', this.tempDeviceList)
      }
    },
     // ***只保存最新的一个 每次更新都替换***
     setWaterTempDevice (device) {
      if (!device) {
        this.waterTempDeviceList = []
        Taro.setStorageSync('waterTempDeviceList', this.waterTempDeviceList)
      } else {
        this.waterTempDeviceList = [device]
        Taro.setStorageSync('waterTempDeviceList', this.waterTempDeviceList)
      }
    },
    removeTempDevice (id) {
      this.tempDeviceList = this.tempDeviceList.filter(d => {
        return d.id !== id
      })
      Taro.setStorageSync('tempDeviceList', this.tempDeviceList)
    },
    removeWaterTempDevice (id) {
      this.waterTempDeviceList = this.waterTempDeviceList.filter(d => {
        return d.id !== id
      })
      Taro.setStorageSync('waterTempDeviceList', this.waterTempDeviceList)
    }
  },
})
