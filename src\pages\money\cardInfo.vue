<template>
  <view class="card">
    <view class="card-amount">
      <view><text class="cny">￥</text><text class="amount">{{ data.user.amount }}</text></view>
      <view class="amount-s">
        总金额 (含未到账 <text class="pending-amount">¥{{data.user.pending_amount}}</text> )
      </view>
    </view>
    <view class="card-bank flex flex-space-between" v-if="!data.account || !data.account.account_info || !data.account.account_info.card_id">
      <view>暂无银行卡</view>
      <view class="btn-add-bank" @tap="onJump('/pages/money/bindCard')"><text class="text-v-center">去添加</text> <MyIcon icon="icon-arrow-right-mini" width="9rpx" height="15rpx"></MyIcon></view>
    </view>
    <view class="card-bank flex flex-space-between" style="align-items: center;" v-else>
      <view v-if="accountInfo && accountInfo?.status == 2">{{ data.account.account_info?.card_name }}(企业)</view>
      <view>{{data.account.account_info.bank_name}}</view>
      <view class="btn-add-bank" @tap="onJump('/pages/money/cardInfo')">{{data.account.account_info.card_id}}</view>
    </view>
  </view>
  <view>
    <view class="ci-card flex flex-space-between">
      <view class="ci-l">账户类型</view>
      <view class="ci-r">{{ accountInfo && accountInfo.status == 2 ? '企业账户' : '个人账户' }}</view>
    </view>
    <view class="ci-card flex flex-space-between">
      <view class="ci-l">户名</view>
      <view class="ci-r">{{ data.account.account_info && data.account.account_info.card_name }}</view>
    </view>
    <view v-if="accountInfo && accountInfo?.status == 2" class="ci-card flex flex-space-between">
      <view class="ci-l">法人</view>
      <view class="ci-r">{{ accountInfo?.legal_person }}</view>
    </view>
    <view class="ci-card flex flex-space-between">
      <view class="ci-l">账号</view>
      <view class="ci-r">{{ data.account.account_info && data.account.account_info.card_id }}</view>
    </view>
    <view class="ci-card flex flex-space-between">
      <view class="ci-l">开户行</view>
      <view class="ci-r">{{ accountInfo && accountInfo.status == 2 ? accountInfo?.bank_name : data.account.account_info && data.account.account_info.bank_name }}</view>
    </view>
    <view v-if="accountInfo && accountInfo?.status == 2" class="ci-card flex flex-space-between">
      <view class="ci-l">统一社会信用码</view>
      <view class="ci-r">{{ accountInfo?.social_credit_code }}</view>
    </view>
    <view v-if="accountInfo && accountInfo?.status == 2" class="ci-card flex flex-space-between">
      <view class="ci-l" style="width: 150rpx;">地址</view>
      <view class="ci-r">{{ accountInfo?.address }}</view>
    </view>
  </view>

  <view class="footer-fixed">
    <view class="p20">
      <view class="btn-text" @tap="onChangeShowUnBind">解绑</view>
    </view>
  </view>

  <MyPopup :show="showUnBind" title="解绑提示" @close="changeShowPay">
    <template #content>
      <view class="p40 text-center">确认是否解绑该银行卡？ </view>
      <view class="p20 flex flex-space-between flex-v-center" style="border-top: 1px solid #DFDFDF">
        <view style="width: 28%" class="text-center" @tap="onUnBindCard">确认</view>
        <view style="width: 70%"><button class="btn-primary" @tap="onChangeShowUnBind">取消</button></view>
      </view>
    </template>
  </MyPopup>

</template>

<script setup>
import { ref } from "vue";
import request from "@/utils/request";
import myBottomBtn from "@/components/MyBottomBtn/MyBottomBtn";
import MyIcon from "@/components/MyIcon"
import MyPopup from "@/components/MyPopup"

import { getApiRoot } from "@/config";
import Taro, {
  useDidShow,
  useDidHide,
  useReady,
  useLoad,
  usePullDownRefresh,
  useReachBottom,
} from "@tarojs/taro";
definePageConfig({
  navigationBarTitleText: "银行卡账户详情",
  navigationBarBackgroundColor: "#F7F9FF",
});

const data = ref({
  user: {},
  account: {}
})

usePullDownRefresh(() => {
  fetch()
  Taro.stopPullDownRefresh()
})

useDidShow(() => {
  fetch()
  getAcountHandel()
})

const fetch = () => {
  request.get({
    url: 'account'
  }).then(res => {
    data.value = res.data
  })
}

const onJump = (url) => {
  Taro.navigateTo({
    url: url
  })
}

const onUnBindCard = () => {
  request.post({
    url: 'account/unbindBank'
  }).then(res => {
    Taro.navigateBack()
  })
}

const accountInfo = ref(null)

  // 获取对公信息
  const getAcountHandel = async() => {
 const {data} =await request
    .get({
      url: "account/bankDetail",
    })
    if (data && data?.address) {
      accountInfo.value = data
    }
  }

const showUnBind = ref(false)
const onChangeShowUnBind = () => {
  console.log('onclick')
  showUnBind.value = !showUnBind.value
}

</script>

<style lang="scss">
page {
  background-color: #f7f9ff;
}
.card {
  position: relative;
  margin: 26px;
  border-radius: 25px;
  background: url("https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/bg-card.png") top right no-repeat;
  background-size: 455px;
  background-color: #1352FD;
  padding: 15px;

  .card-bank {
    border-top: 1px solid #3A6FFF;
    color: #8EACFF;
    font-size: 24px;
    padding: 28px 19px;
    .btn-add-bank {
      font-size: 20px;
      color: #FFFFFF;
    }
  }
  .card-amount {
    padding: 64px 0 40px 44px;
    color: #FFFFFF;
    .amount {
      font-size: 60px;
    }
    .cny {
      font-size: 24px;
    }
    .amount-s {
      color: #8EACFF;
      font-size: 20px;
    }
  }

  .btn-cash-log {
    width: 137px;
    height: 67px;
    background: url("https://yimidb.oss-cn-hangzhou.aliyuncs.com/mp/images/bg-cash.png") center no-repeat;
    background-size: contain;
    font-size: 24px;
    color: #1352FD;
    line-height: 60px;
    text-align: center;
    position: absolute;
    top: 28px;
    right: -10px;
  }
  .btn-cash {
    font-size: 24px;
    color: #8EACFF;
    border: 1px solid #8EACFF;
    padding: 6px 19px;
    position: absolute;
    top: 121px;
    right: 35px;
  }
}
.ci-card {
  padding: 34px 32px 17px;
  background: #FFFFFF;
}
.ci-l {
  font-size: 30px;
  font-weight: 500;
  color: #B6BEC5;
}
.ci-r {
  font-size: 30px;
  font-weight: 500;
  color: #000000;
}

</style>
