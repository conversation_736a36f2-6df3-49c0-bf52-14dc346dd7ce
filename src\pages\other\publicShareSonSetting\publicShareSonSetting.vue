<script setup>
import { AtCheckbox } from "taro-ui-vue3";
import { ref } from "vue";
import request from "@/utils/request";
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import { AtSwitch } from "taro-ui-vue3/lib";

const checkedList = ref([]);

const shareDetail = ref();

const curItem = ref();

const statusBol = ref(false);

const allSonDevices = ref([]);

const curentDevice = ref();
const rate = ref();

const remark = ref("");

useLoad((opt) => {
  console.log(opt.item);
  if (!opt.item) return;
  curItem.value = JSON.parse(opt.item);
  rate.value = curItem.value?.rate;
  remark.value = curItem.value?.remark;
  getDetail(curItem.value.public_device_id);
  getCurList();
  if (curItem.value?.status == 1) {
    statusBol.value = true;
  } else {
    statusBol.value = false;
  }

  if (curItem.value?.clear == 1) {
    checkedList.value.push(0);
  }
  if (curItem.value?.recharge == 1) {
    checkedList.value.push(1);
  }
});

const getDetail = (device_id) => {
  request
    .get({
      url: "device/busDetail",
      data: {
        device_id,
      },
    })
    .then((res) => {
      if (res.code != 200) return;
      shareDetail.value = res.data;
    });
};

// 获取当前分类
const getCurList = () => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "houseClass/list",
        data: {
          type: "edit",
        },
      })
      .then((res) => {
        if (res.code != 200) return;
        console.log(res);
        allSonDevices.value.splice(0);
        res.data.forEach((item) => {
          if (item.id == curItem.value.tag_id) {
            curentDevice.value = item;
            console.log(curentDevice.value, "curentDevice.value");
            item.houses.forEach((item2) => {
              item2.device.forEach((item3) => {
                if (
                  item3?.bus_device &&
                  item3.bus_device?.type == 2 &&
                  item3?.bus_device?.status
                ) {
                  allSonDevices.value.push(item3);
                }
              });
            });
          }

        });
        console.log(allSonDevices.value, "allSonDevices.value");

        resolve();
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const handleChange = (val) => {
  console.log(val);

  const idx = checkedList.value.findIndex((item) => item == val);
  if (idx != -1) {
    checkedList.value.splice(idx, 1);
    console.log(checkedList.value, "arrset");
    if (val == 0) {
      console.log("清零后自动解绑公摊取消");
    } else if (val == 1) {
      console.log("充值后自动绑定公摊取消");
    }
    return;
  }

  checkedList.value.push(val);

  const arr = new Set(checkedList.value);
  checkedList.value = Array.from(arr);

  console.log(checkedList.value, "arrset");

  if (checkedList.value.some((item) => item == val)) {
    if (val == 0) {
      console.log("清零后自动解绑公摊");
    } else if (val == 1) {
      console.log("充值后自动绑定公摊");
    }
  } else {
  }
};

const saveHandel = () => {
  const totalRate = allSonDevices.value.reduce((pre, cur) => {
    return pre + Number(cur.bus_device.rate);
  }, 0);

  console.log(totalRate, "totalRate");
  console.log(rate.value, "rate.value");
  console.log(curItem.value?.rate, " curItem.value?.rate");

  console.log(totalRate - Number(curItem.value?.rate) + Number(rate.value));

  if (totalRate - Number(curItem.value?.rate) + Number(rate.value) > 100) {
    Taro.showToast({
      title: "分表总比例不能超过100%",
      icon: "none",
      duration: 2000,
    });
    return;
  }
  console.log(checkedList.value, "checkedList.value");
  request
    .post({
      url: "device/joinBus",
      data: {
        device_id: curItem.value?.device_id,
        pid: shareDetail.value?.id,
        rate: rate.value,
        clear: checkedList.value.some((chec) => chec == 0) ? 1 : 0,
        recharge: checkedList.value.some((chec) => chec == 1) ? 1 : 0,
        status: statusBol.value ? 1 : 0,
        remark: remark.value,
      },
      showLoading: true,
    })
    .then(() => {
      Taro.showToast({
        title: "修改成功！",
        icon: "none",
        duration: 2000,
      });
      setTimeout(() => {
        Taro.navigateBack();
      }, 1000);
    });
};

const changeSwitch = (e) => {
  console.log(e);
  statusBol.value = e;
  request.post({
    url: "device/joinBus",
    data: {
      device_id: curItem.value?.device_id,
      pid: shareDetail.value?.id,
      rate: curItem.value?.rate,
      clear: curItem.value?.clear,
      recharge: curItem.value?.recharge,
      status: e ? 1 : 0,
      remark: curItem.value?.remark,
    },
    showLoading: true,
  }).then(() => {
    Taro.showToast({
      title: '操作成功！',
      icon: 'none',
      duration: 2000,
    })
  })
};
</script>

<template>
  <view class="publicShareSonSetting-container">
    <view class="cell">
      <view class="txt">表号:</view>
      <view>{{ curItem?.sn }}</view>
    </view>
    <!-- <view class="cell">
      <view class="txt">价格选择:</view>
      <view>合租电价：0.800元/kwh</view>
    </view> -->
    <view class="cell">
      <view class="txt">对应公摊主表:</view>
      <view>{{ curItem?.public_sn }}</view>
    </view>
    <view class="cell">
      <view class="txt">选择分组:</view>
      <view>{{ curItem?.tag_name }}</view>
    </view>
    <view class="cell">
      <view class="txt">权重:</view>
      <view class="ipt">
        <input type="text" v-model="rate" placeholder="请输入权重" />
        <text>%</text>
      </view>
    </view>
    <view class="cell">
      <view class="txt">状态:</view>
      <view>
        <at-switch
          :title="statusBol ? '分摊中' : '未启用'"
          :checked="curItem?.status == 1 ? true : false"
          @change="changeSwitch"
        />
      </view>
    </view>
    <view class="cell">
      <view class="txt">备注:</view>
      <input type="text" v-model="remark" placeholder="请输入备注" />
      <!-- <view>{{ curItem?.remark || "--" }}</view> -->
    </view>
    <view class="bot">
      <view>
        <at-checkbox
          :options="[{ label: '清零后停止公摊', value: 0 }]"
          :selectedList="checkedList"
          @change="handleChange(0)"
        />
      </view>
      <view>
        <at-checkbox
          :options="[{ label: '充值后启动公摊', value: 1 }]"
          :selectedList="checkedList"
          @change="handleChange(1)"
        />
      </view>
    </view>
    <!-- utils -->
    <view class="footer-fixed">
      <button class="btn-add m33" @tap="saveHandel">保存</button>
    </view>
  </view>
</template>

<style lang="scss">
@import "taro-ui-vue3/dist/style/components/checkbox.scss";
@import "taro-ui-vue3/dist/style/components/switch.scss";
page {
  background-color: #f1f2f3;
}
.publicShareSonSetting-container {
  padding-bottom: 150px;
  .at-switch__title {
    margin-right: 20px;
  }
  .at-switch {
    padding: 0;
  }
  .bot {
    margin-top: 30px;
    padding: 0 33px;
    > view {
      margin-bottom: 20px;
    }
    .at-checkbox {
      &::after {
        content: none;
      }
      &::before {
        content: none;
      }
    }
    .at-checkbox__title {
      font-size: 28px;
      color: #000;
      margin-left: 10px;
      font-size: 32px;
      font-weight: 400;
    }
    .at-checkbox__option-wrap {
      padding: 0;
    }
    .at-checkbox__option {
      padding-left: 0;
    }
    .at-checkbox__option-cnt {
      background-color: #f1f2f3 !important;
      align-items: center;

      &:active {
        background-color: #fff;
      }
    }

    .at-checkbox__option--selected {
      .at-checkbox__icon-cnt {
        background-color: #5bc650;
      }
    }

    .at-checkbox__icon-cnt {
      // width: 25px;
      // height: 25px;
      // min-width: 10px;
      margin-right: 0;
    }
  }
  .cell {
    display: flex;
    align-items: center;
    font-size: 28px;
    background-color: #fff;
    border-bottom: 1px solid #f1f2f3;
    padding: 36px 33px;
    box-sizing: border-box;
    .txt {
      color: #8c8c8c;
      margin-right: 15px;
    }
    .ipt {
      display: flex;
      align-items: center;
      input {
        width: 80px;
      }
      text {
        margin-left: 5px;
      }
    }
  }
}
</style>
