<template>
  <view>
    <view class="house-bg">1</view>
    <view class="house">
      <view class="house-top house-box">
        <view class="house-name">{{ data.house.name }}</view>
        <view class="house-cell mt45"><text class="label">所在小区</text><text class="value">{{ data.house.estate_name }}</text></view>
        <view class="house-cell"><text class="label">房源状态</text><text :class="'house-status-' + data.house.status_key">
          {{ data.house.status }}
        </text></view>
      </view>
      <view class="house-box" v-if="data.house.contract && data.house.contract.status === 20">
        <view class="house-box-title">租客信息</view>
        <view class="mt45">
          <view class="house-cell"><text class="label">租客姓名</text><text class="value"> {{ data.house.master_tenant && data.house.master_tenant.name }}</text></view>
          <view class="house-cell"><text class="label">手机号码</text><text class="value"> {{ data.house.master_tenant && data.house.master_tenant.mobile }}</text> <image :src="IconPhone" @tap="onCall" style="width: 29rpx; height: 29rpx; vertical-align: middle;"></image>
          </view>
          <view class="house-cell"><text class="label">租房周期</text><text class="value"> {{ data.house.contract && data.house.contract.start_at }} ~ {{ data.house.contract && data.house.contract.expired_at }}</text></view>
          <view class="house-cell"><text class="label">租房押金</text><text class="value"> {{ data.house.contract && data.house.contract.contract_rent.deposit }}元</text></view>
          <view class="house-cell"><text class="label">押付方式</text><text class="value"> {{ data.house.contract && data.house.contract.contract_rent.rent_num_name }}</text></view>
          <view class="house-cell"><text class="label">房屋租金</text><text class="value"> {{ data.house.contract && data.house.contract.contract_rent.amount }}</text></view>
        </view>
      </view>
      <view class="house-box flex flex-space-between flex-v-center">
        <view class="house-box-title">设备信息</view>
        <view><text class="btn-text-small" @tap="onAddDevice">新增</text></view>
      </view>
      <view class="house-box2" v-for="(device,idx) in data.device">
          <view class="flex device-top">
              <view v-if="device.type != 2" class="device-icon"><image :src="IconDianbiao" style="width: 44rpx; height: 57rpx;"></image></view>
              <view v-if="device.type == 2" class="device-icon"><image :src="shuiImg" style="width: 44rpx; height: 57rpx;"></image></view>
              <view style="flex: 1"><view class="device-name">{{ device.type == 2 ? '水表' : '电表' }}</view><view class="device-sn"> {{ switchType(device.net_type,device.type)}} {{device.sn}} </view></view>
              <view style="width: 64rpx;">
                <text class="btn-text-small" @tap="onUnbindDevice(device.id)">解绑</text>
              </view>
          </view>
          <view class="device-info flex flex-space-between flex-v-center">
            <view>
              <view class="device-num">{{Number(device?.total || 0).toFixed(2)}}</view>
              <view class="device-lab">{{ device.type == 2 ? '总水量' : '总电量' }}</view>
            </view>
            <view :class="{red: (device?.type == 5 || device?.type == 6) && device?.pay_mode == 2}">
              <view class="device-num">{{device.du || 0}}</view>
              <view class="device-lab">{{  device.type == 2 ? '剩余水量(m³)' : itemText(device) }}</view>
            </view>
            <view v-if="device.type == 5 || device.type == 6" @tap.stop="priceShow = true;devIdx = idx">
              <view class="device-num">查询</view>
              <view class="device-lab">分段电价</view>
            </view>
            <view v-else>
              <view class="device-num">{{device.price}}</view>
              <view class="device-lab">单价(元/{{ device.type == 2 ? 'm³' : '度'}})</view>
            </view>
          </view>
      </view>
      <view class="house-box">
        <view class="flex flex-space-between flex-v-center">
          <view class="house-box-title">房源信息</view>
          <view><text class="btn-text-small" @tap="onEdit">编辑</text></view>
        </view>
        <view class="mt45">
          <view class="house-cell"><text class="label">所在地区</text><text class="value">{{(data.house.province || '--') + ' / ' + (data.house.city || '--') + ' / ' + (data.house.district || '--')}}</text></view>
          <view class="house-cell"><text class="label">房屋面积</text><text class="value"> {{ data.house.size }} ㎡</text></view>
          <view class="house-cell"><text class="label">参考租金</text><text class="value"> {{ data.house.rent }} 元/月</text></view>
          <view class="house-cell"><text class="label">付款方式</text><text class="value"> {{ data.house.rent_num_name }} </text></view>
          <view class="house-cell"><text class="label">房屋户型</text><text class="value"> {{ data.house.fang }}房{{ data.house.ting }}厅{{ data.house.wei }}卫 {{ data.house.chu }}厨</text></view>
        </view>
      </view>
    </view>
    <view class="footer-fixed" v-if="data.house.status_key === 'vacant'" >
      <view class="flex flex-v-center flex-space-between">
        <view style="width: 220rpx; text-align: center;" @tap="onAddDevice">设备管理</view>
        <view style="width: 510rpx;"><button class="btn-add m33" @tap="onAddContract">创建出租</button></view>
      </view>
    </view>
    <view class="footer-fixed" v-if="data.house.contract && data.house.contract.status === 20" >
      <view class="flex flex-v-center flex-space-between">
          <view style="width: 370rpx; text-align: center;"><text @tap="onAddDevice">设备管理</text> | <text @tap="onBill">查看账单</text></view>
          <view style="width: 360rpx;"><button class="btn-add m33" @tap="onShowContract">查看合同</button></view>
      </view>
    </view>
    <view class="footer-fixed" v-if="data.house.contract && (data.house.contract.status === 1 || data.house.contract.status === 10)" >
      <view class="flex flex-v-center flex-space-between">
        <view style="width: 200rpx; text-align: center;"><text @tap="onAddDevice">设备管理</text></view>
        <view style="width: 530rpx;"><button class="btn-add m33" @tap="onShowContract">查看合同</button></view>
      </view>
    </view>
  <QueryPricesPopUp :device="data.device && data.device[devIdx]" :show="priceShow" @close="priceShow = false" />
  </view>
</template>
<script setup>
  import { ref } from 'vue'
  import './detail.scss'
  import '@/assets/css/house-status.scss'
  import Taro, { useDidShow, useDidHide, useReady, useLoad, usePullDownRefresh, useReachBottom} from '@tarojs/taro'
  import request from '@/utils/request'
  import QueryPricesPopUp from '@/components/queryPricesPopUp'

  import MyInput from '@/components/MyInput'
  import MyCell from '@/components/MyCell'

  const IconPhone = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-phone.png'
  const IconDianbiao = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-dianbiao.png'
  import shuiImg from '@/assets/water/device.png'

  import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

  const id = ref(0)

  const priceShow = ref(false)

  const devIdx = ref(0)

  const data = ref({
    house: {},
    device: []
  })

  const itemText = (dev) => {
  if (dev?.type == 5 || dev?.type == 6) {
    if (dev?.pay_mode == 1) {
      // 预付费
      return '剩余金额(元)'
    } else {
      //后付费
      return '已用金额(元)'
    }
  } else {
    return '剩余电量(度)'
  }
}

  useLoad((options) => {
    id.value = options.id
  })

  useDidShow(() => {
    getDetail()
  })

  const getDetail = () => {
    request.get({
      url: 'house/' + id.value
    }).then(res => {
      data.value = res.data
    })
  }

  const onAddContract = () => {
    Taro.navigateTo({
      url: '/pages/contract/chooseType/chooseType?id=' + id.value
    })
  }

  const onCall = () => {
    Taro.makePhoneCall({
      phoneNumber: data.value.house.master_tenant.mobile
    })
  }

  const switchType = (net_type,type) => {
    if (net_type == 1 && type == 2 ) {
      return '4G双模水表号:'
    }
    if (net_type == 1 && type != 2 ) {
      return '4G双模电表号:'
    }
    if (net_type != 1 && type != 2 ) {
      return '蓝牙电表号:'
    }
    if (net_type != 1 && type == 2 ) {
      return '蓝牙水表号:'
    }
  }

  const onShowContract = () => {
    if (data.value.house.contract.status === 1) {
      Taro.navigateTo({
        url: "/pages/contract/createZcontract/createZcontract?id=" + data.value.house.contract.id,
      });
    } else {
      Taro.navigateTo({
        url: '/pages/contract/detail/detail?id=' + data.value.house.contract.id
      })
    }
  }

  const onEdit = () => {
    Taro.navigateTo({
      url: '/pages/house/edit/edit?id=' + id.value
    })
  }

  const onAddDevice = () => {
   Taro.navigateTo({
    url: '/pages/house/addDevice/addDevice?house_id=' + id.value
   })
  }

  const onBill = () => {
    Taro.navigateTo({
      url: '/pages/contract/bill/bill?contract_id=' + data.value.house.contract.id
    })
  }

  const onUnbindDevice = (id) => {
    Taro.showModal({
      title: '解绑确认',
      content: '确定要解绑吗？',
      success: (res) => {
        if (res.confirm) {
          request.post({
            url: 'device/' + id + '/unbind'
          }).then(res => {
            Taro.showToast({
              title: '解绑成功！'
            })
            data.value.device = data.value.device.filter(item => {
              return item.id !== id
            })
          })
        }
      }
    })
  }

</script>
