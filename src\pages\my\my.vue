<template>
  <view class="container">
    <view
      class="user-info flex flex-v-center flex-space-between"
      :style="
        'padding-top: ' + (40 + globalStore.windowInfo.statusBarHeight) + 'px'
      "
    >
      <view class="flex flex-v-center">
        <view class="avatar" v-if="!globalStore.isLogin" @tap="handleLogin"
          ><image :src="Avatar" class="avatar-img"></image
        ></view>
        <view class="avatar" v-if="globalStore.isLogin" @tap="onUserInfo"
          ><image
            :src="globalStore.userInfo.avatar || Avatar"
            class="avatar-img"
          ></image
        ></view>
        <view class="info" v-if="!globalStore.isLogin" @tap="handleLogin">
          <view class="nickname">尚未登录</view>
          <view class="mobile">点击登录</view>
        </view>
        <view class="info" v-if="globalStore.isLogin" @tap="onUserInfo">
          <view class="nickname">{{
            globalStore.userInfo.nickname || "匿名"
          }}</view>
          <view class="mobile">{{ globalStore.userInfo.mobile }}</view>
        </view>
      </view>
      <view class="fdd" v-if="globalStore.isLogin">
        <text
          class="fdd-yes"
          v-if="
            globalStore.userInfo.fdd && globalStore.userInfo.fdd.status === 2
          "
          @tap="onJump('/pages/my/fdd/fdd')"
          >已认证</text
        >
        <text
          class="fdd-no"
          v-if="
            !globalStore.userInfo.fdd || globalStore.userInfo.fdd.status !== 2
          "
          @tap="onJump('/pages/my/fdd/fdd')"
          >未认证</text
        >
      </view>
    </view>
    <view class="block flex flex-space-between">
      <view class="who-left flex flex-v-center">
        <view><image :src="iconWho" class="who-img"></image></view>
        <view
          >当前为<text class="who-txt">{{
          globalStore.isAdmin ? '管理员' : globalStore.who === "tenant" ? "租客" : globalStore.who === "business" ?  "房东" : '管理员'
          }}</text
          >身份</view
        >
      </view>
      <view class="who-change flex flex-v-center" v-if="!globalStore.isAdmin">
        <view @tap="onChangeWho"
          ><image :src="iconChange" class="who-change-img"></image
        ></view>
        <view @tap="onChangeWho">切换</view>
      </view>
    </view>
    <view class="my-title">我的功能</view>
    <view>
      <view class="grid-1 font-24">
        <view
          class="grid-item"
          v-if="globalStore.who === 'tenant'"
          @tap="onJump('/pages/tenant/order/order')"
        >
          <view><image :src="iconBill" class="grid-item-img"></image></view>
          <view>充值记录</view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view>
        <view
          class="grid-item"
          v-if="globalStore.who === 'tenant'"
          @tap="gaojingSettingHandel"
        >
          <view><image :src="iconSetting" class="grid-item-img"></image></view>
          <view>
            <text>告警设置</text>
          </view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view>
        <view
          class="grid-item"
          v-if="globalStore.who === 'business'"
          @tap="onJump('/pages/other/publicShare/publicShare')"
        >
          <view><image :src="iconGongTaN" style="width: 55rpx;height: 55rpx;" class="grid-item-img"></image></view>
          <view>公摊设置</view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view>
        <view
          class="grid-item"
          v-if="globalStore.who === 'business' && !globalStore.isAdmin"
          @tap="onJump('/pages/other/houseTagAdmin/houseTagAdmin')"
        >
          <view><image :src="iconGroup" style="width: 55rpx;height: 55rpx;" class="grid-item-img"></image></view>
          <view>楼幢管理</view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view>
        <view
          class="grid-item"
          v-if="globalStore.who === 'business' && !globalStore.isAdmin"
          @tap="onJump('/pages/other/setAdmin/setAdmin')"
        >
          <view><image src="https://yimits.oss-cn-beijing.aliyuncs.com/lins/set-admin-2.png" style="width: 55rpx;height: 55rpx;" class="grid-item-img"></image></view>
          <view>设置管理员</view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view>
        <view
          class="grid-item"
          v-if="globalStore.who === 'business'"
          @tap="onJump('/pages/money/stat/stat')"
        >
          <view><image :src="iconChart" style="width: 55rpx;height: 55rpx;border-radius: 50%" class="grid-item-img"></image></view>
          <view>数据统计</view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view>
        <view
          class="grid-item"
          v-if="globalStore.who === 'business' && !globalStore.isAdmin"
          @tap="onJump('/pages/house/conf/conf')"
        >
          <view><image :src="iconSetting" class="grid-item-img"></image></view>
          <view>房东设置</view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view>
        <!-- <view
          class="grid-item"
          v-if="globalStore.who === 'business'"
        >
          <view><image :src="iconSetting" class="grid-item-img"></image></view>
          <view>
            <text>充值提醒</text>
            <switch
              :checked="isChargeRemind"
              @change="changeSwitchRechare($event)"
              style="transform: scale(0.7);"
              color="#1652F8"
            />
          </view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view> -->
        <view class="grid-item" @tap="onJumpPublic('/pages/qa/index')">
          <view><image :src="iconQa" class="grid-item-img"></image></view>
          <view>常见问题</view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view>
        <view
          class="grid-item"
          v-if="globalStore.who === 'business'"
          @tap="onJumpPublic('/pages/webview/article?key=fangdongzhinan')"
        >
          <view><image :src="iconHelp2" class="grid-item-img"></image></view>
          <view>操作指南</view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view>
        <view
          class="grid-item"
          v-if="globalStore.who === 'tenant'"
          @tap="onJumpPublic('/pages/webview/article?key=fangkezhinan')"
        >
          <view><image :src="iconHelp2" class="grid-item-img"></image></view>
          <view>操作指南</view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view>
        <view
          class="grid-item"
          v-if="globalStore.who === 'business'"
          @tap="clickMyContant"
        >
          <view><image :src="iconContact" class="grid-item-img"></image></view>
          <view>联系我们</view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view>
        <!-- <view
          class="grid-item"
          v-if="globalStore.who === 'business'"
          @tap="onJumpPublic('/pages/article/article?key=about')"
        >
          <view><image :src="iconAbout" class="grid-item-img"></image></view>
          <view>关于我们</view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view> -->
        <!-- <view class="grid-item serv" v-if="globalStore.who === 'tenant'">
          <view><image :src="iconServ" class="grid-item-img"></image></view>
          <view>在线客服</view>
          <button
            open-type="contact"
            bindcontact="handleContact"
            class="simple-btn"
          >
            点击咨询
          </button>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view> -->
        <view
          class="grid-item"
          v-if="globalStore.who === 'tenant'"
          @tap="onJump('/pages/my/changePwd/changePwd')"
        >
          <view><image :src="iconAbout" class="grid-item-img"></image></view>
          <view>修改密码</view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view>
        <view
          class="grid-item"
          v-if="globalStore.who === 'business'"
          @tap="onJump('/pages/my/changeBusinessPwd/changeBusinessPwd')"
        >
          <view><image :src="iconSetting" class="grid-item-img"></image></view>
          <view>修改密码</view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view>
        <view
          class="grid-item"
          v-if="globalStore.who === 'business'"
          @tap="onJump('/pages/my/changePhone/changePhone')"
        >
          <view><image :src="iconPhone" class="grid-item-img" style="width:44rpx;height:56rpx" ></image></view>
          <view>修改手机号</view>
          <view class="grid-item-arrow"
            ><image :src="iconArrowRight" class="grid-item-arrow-img"></image
          ></view>
        </view>
        <!--        <view class="grid-item" v-if="globalStore.who === 'tenant'" @tap="onJump('/pages/my/changePwd/changePwd')">
          <view><image :src="iconContact" class="grid-item-img"></image></view>
          <view>意见反馈</view>
          <view class="grid-item-arrow"><image :src="iconArrowRight" class="grid-item-arrow-img"></image></view>
        </view>-->
      </view>
    </view>
    <view v-if="globalStore.isLogin" class="logout"
      ><MyIcon icon="icon-logout" width="45rpx"></MyIcon>
      <text class="text-v-center" @tap="handleLogout">退出账号</text></view
    >

    <!-- <e-chart ref="barChat" canvas-id="bar-canvas" /> -->

    <LoginDialog :show="showLogin" @close="onLoginClose" @login="onLogin">
    </LoginDialog>
    <contantModal :show="isContact" @close="isContact = false" :phone="globalStore.userInfo.service_te"/>
    <contantModal :show="isContact2" @close="isContact2 = false" :isInlineServe="false" :phone="globalStore.userInfo.service_te" title="专属客服"/>
    <image v-if="globalStore.isLogin" class="serve-img" src="https://yimits.oss-cn-beijing.aliyuncs.com/186661723089222_.pic.jpg" @tap="clickMyContant('2')"></image>
  </view>
  <!-- <TabBarIndex /> -->
  <YModal
      title="设置告警电量"
      confirmText="确认"
      :show="showGaoJing"
      @close="showGaoJing = false"
      @confirm="confirmGaoJing"
      maskClose
      :bodyStyle="{
        backgroundColor: '#fff',
        paddingBottom: '30rpx',
        width: '650rpx',
        borderRadius: '40rpx',
        zIndex: 9999999999,
      }"
    >
      <template #content>
        <view class="gao-container">
        <view class="ipt-box">
          <input type="text" class="input" v-model="gaoJingDianLiang" placeholder="请输入告警电量" />
          <text>度</text>
        </view>
        </view>
      </template>
    </YModal>
</template>

<script setup>
import { ref } from "vue";
import Taro from "@tarojs/taro";
import { useDidShow, useDidHide, useReady, useLoad } from "@tarojs/taro";
import request from "@/utils/request";
import YModal from "@/components/YModal";


// import EChart from "@/components/echarts/e-chart.vue";

import { useGlobalStore } from "@/stores";

import "./my.scss";
const Avatar = "https://yimits.oss-cn-beijing.aliyuncs.com/images/logo2.png";
const iconWho = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-who.png";
const iconChange = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-change.png";
const iconChart = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-chart.png";

const iconSetting = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-my-setting.png";
const iconQa = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-qa.png";
const iconHelp2 = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-help2.png";
const iconContact = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-contact.png";
const iconAbout = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-about.png";
const iconPhone = "https://yimits.oss-cn-beijing.aliyuncs.com/images/phone.png";
const iconServ = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-serv.png";
const iconBill = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-bill.png";
const iconArrowRight = "https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-arrow-right.png";
import iconGroup from "@/assets/pic/group.png";
import iconGongTaN from "@/assets/pic/gongtan.png";

import TabBarIndex from "@/components/customTabbar";

import contantModal from './components/contantModal/index.vue'

import { active, role } from "@/utils/tabActive";


import MyIcon from "@/components/MyIcon";

import LoginDialog from "@/components/LoginDialog";

const barChat = ref();

// Taro.hideTabBar();

const globalStore = useGlobalStore();

useReady(() => {
  // initMultiBarChart();
});

const showGaoJing = ref(false);

const gaoJingDianLiang = ref(1);

const gaojingSettingHandel = () => {
  showGaoJing.value = true;
}

// 设置告警电量
const confirmGaoJing = () => {
  if (!gaoJingDianLiang.value) {
    Taro.showToast({
      title: '请输入告警电量',
      icon: 'none'
    })
    return
  }
  request.get({
    url: 'user/subWarning',
    data: {
      sub_warning: gaoJingDianLiang.value
    }
  }).then(res => {
    showGaoJing.value = false
    Taro.showToast({
      title: '设置成功！',
      icon: 'success'
    })
  })
}

const isChargeRemind = ref(false)//是否开启充值提醒

// 同步授权
const authHandel = ({sub_1,sub_2}) => {
  request.get({
    url: 'user/updateSub',
    data: {
      sub_1,
      sub_2
    }
  }).then(_ => {
  })
}

const changeSwitchRechare = (e) => {
  console.log(e.detail.value);
  const val = e.detail.value
  // isChargeRemind.value = val;
  if (!globalStore.isLogin) {
    showLogin.value = true;
    setTimeout(() => {
      isChargeRemind.value = false;
    },20)
  } else {
    showLogin.value = false;
    if (val) {
      if (process.env.TARO_ENV != 'weapp' || globalStore.who == 'tenant') return
      // 先检查用户是否已经授权
      // 获取订阅权限
      wx.login({
        success(res) {
          if (res.code) {
            request.post({
              url: 'user/updateOpenId',
              data: {
                code: res.code
              },
              Headers: {
                'Token': globalStore.token
              }
            }).then(_ => {
            })
          }
        },
        fail() {
          Taro.showToast({
            title: '获取openid出错',
            icon: 'error'
          })
        }
      })
      wx.getSetting({
        withSubscriptions: true,
        success (res) {
          console.log(res.subscriptionsSetting,"subscriptionsSetting")
          if (res.subscriptionsSetting.mainSwitch) { 
            Taro.requestSubscribeMessage({
              tmplIds: ['6KU5FXZLFqoV3XFZdu0HAF85ZfVYIneU8Nq4N9zMeTM'],
              success (res) {
                console.log(res, "requestSubscribeMessageSucess");
                if (res['6KU5FXZLFqoV3XFZdu0HAF85ZfVYIneU8Nq4N9zMeTM'] == 'reject') {
                  Taro.showModal({
                  title: '提示',
                  content: '请开启订阅消息，以便及时收到用电信息',
                  showCancel: false,
                    success (res) {
                      if (res.confirm) {
                        Taro.openSetting()
                      }
                    }
                  })
                return
                }
                authHandel({sub_1: 1,sub_2: globalStore.userInfo?.sub_2})
                // TODO 开启
                request.get({
                  url: 'user/subStatus',
                  data: {
                    sub_status: 1
                  }
                }).then(_ => {
                  isChargeRemind.value = true
                }).catch(err => {
                  isChargeRemind.value = false
                })

              },
              fail (erro) {
                console.log(erro,"requestSubscribeMessageErro");
              }
            })
          } else {
            Taro.showModal({
              title: '提示',
              content: '请开启订阅消息，以便及时收到用电信息',
              showCancel: false,
              success (res) {
                if (res.confirm) {
                  Taro.openSetting()
                }
              }
            })
          }
        }
      })
    }
    else {
      // TODO 关闭 user/subStatus
      request.get({
        url: 'user/subStatus',
        data: {
          sub_status: 0
        }
      }).then(_ => {
        isChargeRemind.value = false
      }).catch(err => {
        isChargeRemind.value = true
      })
    }
  }
}

const initMultiBarChart = () => {
  let yData = [
    {
      Name: "收入",
      Value: [5454, 545, 4, 54, 5, 45, 4, 54, , 4, 5, 45, 4, 5, 4, 54, 5, 4, 5],
    },
    {
      Name: "支出",
      Value: [5454, 545, 4, 54, 5, 45, 4, 54, , 4, 5, 45, 4, 5, 4, 54, 5, 4, 5],
    },
  ];

  const options = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        crossStyle: {
          color: "#999",
        },
      },
    },
    grid: {
      y: 80,
      y2: 20,
      borderWidth: 1,
      // top: '90px',//内边距
      // left: '100px',
      // right: '0px',
      // bottom: '0px'
    },
    color: "red",
    legend: {
      data: ["收入", "支出"],
      left: "center",
      top: "1%",
    },
    xAxis: [
      {
        type: "category",
        data: [
          "1月",
          "2月",
          "3月",
          "4月",
          "5月",
          "6月",
          "7月",
          "8月",
          "9月",
          "10月",
          "11月",
          "12月",
        ],
        axisPointer: {
          type: "shadow",
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "元",
        axisLabel: {
          formatter: "{value}",
        },
      },
    ],
    series: yData.map((item) => {
      return {
        name: item.Name,
        type: "bar",
        barMaxWidth: 30,
        data: item.Value,
      };
    }),
  };
  Taro.nextTick(() => {
    setTimeout(() => {
      console.log("refresh");
      barChat.value.refresh(options);
    }, 0);
  });
};

useDidShow(() => {
  globalStore.getUserInfo();
  if (!globalStore.isLogin) {
    showLogin.value = true;
  } else {
    showLogin.value = false;
    gaoJingDianLiang.value = globalStore.userInfo.sub_warning
    isChargeRemind.value = Boolean(globalStore.userInfo.sub_status)
  }
});

const isContact = ref(false);
const isContact2 = ref(false);
const clickMyContant = (type) => {
  // onJumpPublic('/pages/article/article?key=contact')
  if (type && type == 2) {
    isContact2.value = true
  } else {
    isContact.value = true
  }
}

const handleLogin = () => {
  // Taro.navigateTo({
  //   url: '/pages/login/login'
  // })
  showLogin.value = true;
};

const onUserInfo = () => {
  Taro.navigateTo({
    url: "/pages/userInfo/userInfo",
  });
};

const onChangeWho = () => {
  if (!globalStore.isLogin) {
    handleLogin();
    return;
  }
  Taro.navigateTo({
    url: "/pages/who/who",
  });
};

const onJump = (path) => {
  if (!globalStore.isLogin) {
    handleLogin();
    return;
  }
  Taro.navigateTo({
    url: path,
  });
};

const onJumpPublic = (path) => {
  Taro.navigateTo({
    url: path,
  });
};

const handleLogout = () => {
  Taro.showModal({
    title: "退出提示",
    content: "确认是否退出登录？",
    success: (res) => {
      if (res.confirm) {
        globalStore.logout();
        active.value = 0
        Taro.switchTab({
          url: "/pages/index/index",
        });
      }
    },
  });
};

const showLogin = ref(false);
const onLoginClose = () => {
  showLogin.value = false;
};
const onLogin = () => {
  showLogin.value = false;
  Taro.switchTab({
    url: "/pages/index/index",
  });
};
</script>
