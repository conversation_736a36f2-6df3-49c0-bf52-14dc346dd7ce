<script setup>
import MyPopup from "@/components/MyPopup";
import MyInput2 from "@/components/MyInput2";
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  device: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(["close", "submitPrice"]);
const onClosePrice = () => {
  emit("close");
};
</script>

<template>
  <view>
    <MyPopup
      :show="show"
      :title="device?.type == 2 ? '水量定价' : '电量定价'"
      @close="onClosePrice"
      @tap.stop=""
    >
      <template #content>
        <view style="color: #000000; padding: 35rpx">
          <!-- 峰谷表 -->
          <view class="p20" v-if="device?.type == 5 || device?.type == 6">
            <MyInput2 suffix="元" prefix="尖峰电价">
              <template #content
                ><input
                  class="input-custom"
                  type="digit"
                  cursor-spacing="120"
                  :disabled="true"
                  :value="device?.coef4"
                  placeholder="请输入尖峰电价"
              /></template>
            </MyInput2>
            <MyInput2 suffix="元" prefix="高峰电价">
              <template #content
                ><input
                  class="input-custom"
                  type="digit"
                  cursor-spacing="120"
                  :disabled="true"
                  :value="device?.coef3"
                  placeholder="请输入高峰电价"
              /></template>
            </MyInput2>
            <MyInput2 suffix="元" prefix="平段电价">
              <template #content
                ><input
                  class="input-custom"
                  type="digit"
                  cursor-spacing="120"
                  :disabled="true"
                  :value="device?.coef2"
                  placeholder="请输入平段电价"
              /></template>
            </MyInput2>
            <MyInput2 suffix="元" prefix="低谷电价">
              <template #content
                ><input
                  class="input-custom"
                  type="digit"
                  cursor-spacing="120"
                  :value="device?.coef1"
                  :disabled="true"
                  placeholder="请输入低谷电价"
              /></template>
            </MyInput2>
          </view>
          <!-- <view class="text-center" style="padding: 30rpx">
            <button
              class="btn-primary btn-inline"
              style="width: 220rpx"
              @tap="onSubmitPrice"
            >
              确定
            </button>
          </view> -->
        </view>
      </template>
    </MyPopup>
  </view>
</template>

<style lang="scss" scoped></style>
