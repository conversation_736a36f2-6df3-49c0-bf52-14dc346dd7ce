<script setup>
import MyPopup from "@/components/MyPopup";
import { AtCheckbox } from "taro-ui-vue3";
import { ref,watch } from "vue";
import Taro, {
} from "@tarojs/taro";
import request from "@/utils/request";

const props = defineProps({
  classShow: {
    type: Boolean,
    default: false,
  }
})

const emit = defineEmits(["close", "confirm"]);

const classList = ref([]);

const curentIdx = ref(0);

const isMore = ref(false);

const curentItemObj = ref(null);

const checkedList = ref([]);

const checkedObjList = ref([]);

const selectClassNames = ref([]);


watch(() => props.classShow, (val) => {
  if (val) {
    console.log("打开弹窗");
    
    getList();
  }
})

const closeHandel = () => {
  emit("close");
};

const confirmCreate = () => {
  emit("confirm", checkedObjList.value);
};

const itemClickHandel = (index) => {
  isMore.value = !isMore.value;
  curentIdx.value = index;
};

const handleChange = (val, item) => {
  curentItemObj.value = item;
  const idx = checkedList.value.findIndex((item) => item == val);
  if (idx != -1) {
    checkedList.value.splice(idx, 1);
    console.log(checkedList.value, "checkedList");
    // 根据id获取对象数组
    checkedObjList.value = classList.value.filter((item) => {
      return checkedList.value.includes(item.id);
    });
    console.log(checkedObjList.value, "checkedObjList");
    if (!checkedList.value.length) curentItemObj.value = null;
    return;
  }

  console.log(val);

  checkedList.value.push(val);
  // checkedList.value = [val];

  const arr = new Set(checkedList.value);
  checkedList.value = Array.from(arr);
  // 根据id获取对象数组
  checkedObjList.value = classList.value.filter((item) => {
    return checkedList.value.includes(item.id);
  });
  console.log(checkedObjList.value, "checkedObjList");

  console.log(checkedList.value, "checkedList");
};

// 获取分类列表
const getList = () => {
  return new Promise((resolve, reject) => {
    request
      .get({
        url: "houseClass/list",
        data: {
          type: "edit",
        },
      })
      .then((res) => {
        if (res.code != 200) return;
        classList.value = res.data;
        console.log(classList.value, "classList");
        resolve();
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const confirmCreateHandel = () => {
  console.log(checkedObjList.value);
  if (checkedObjList.value.length == 0) {
    selectClassNames.value = [];
    emit('close')
    emit('confirm', selectClassNames.value,checkedList.value)
    return 
  }
  selectClassNames.value = checkedObjList.value.map((item) => item.label);
  emit('close')
  emit('confirm', selectClassNames.value,checkedList.value)
};
</script>

<template>
  <view>
    <MyPopup :show="props.classShow" @close="emit('close')">
      <template #content>
        <view class="pop-content-class">
          <view class="pop-title">选择分组</view>
          <view class="class-box">
            <!-- 列表 -->
            <view class="list" v-if="classList.length > 0">
              <view
                class="item"
                @tap="itemClickHandel(index)"
                v-for="(item, index) in classList"
                :key="index"
              >
                <view class="top">
                  <view class="left">
                    <at-checkbox
                      :options="[{ label: item.label, value: item.id }]"
                      :selectedList="checkedList"
                      @change="handleChange(item.id, item)"
                    />
                    <view
                      class="icon-xiajiantou iconfont"
                      v-if="item?.houses?.length > 0"
                      :style="{
                        transform: `rotate(${
                          isMore && curentIdx == index ? '180' : '0'
                        }deg) translateY(3rpx)`,
                        transition: 'all .3s',
                      }"
                    ></view>
                    <!-- <view class="title">{{ item?.label }}</view> -->
                  </view>
                </view>
                <template
                  v-if="
                    isMore && curentIdx == index && item?.houses?.length > 0
                  "
                >
                  <view
                    class="_bottom"
                    v-for="oitem in item.houses"
                    :key="item.id"
                  >
                    <view class="tit">
                      <image
                        src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fangzi.png"
                        mode="scaleToFill"
                        class="icon-s"
                      />
                      <view>{{ oitem?.name }}</view>
                    </view>
                    <!-- 电表 -->
                    <view
                      class="li"
                      v-for="(sitem, idx) in oitem.device"
                      :key="idx"
                    >
                      <view class="le">
                        <text class="iconfont icon-dianbiao_shiti"></text>
                        <text
                          :style="
                            sitem?.net_type == 1 && sitem?.signal_num <= 0
                              ? 'color: #98A6C3;'
                              : ''
                          "
                          >{{ sitem?.net_type == 1 ? "电表" : "电表" }}-{{
                            sitem?.sn
                          }}</text
                        >
                      </view>
                      <view
                        class="ri"
                        v-if="sitem?.signal_num > 0 && sitem?.net_type == 1"
                      >
                        在线
                      </view>
                      <view
                        class="ri"
                        style="color: #98a6c3"
                        v-if="sitem?.net_type == 1 && sitem?.signal_num <= 0"
                      >
                        离线
                      </view>
                    </view>
                    <!-- 门锁 -->
                    <view
                      class="li"
                      v-for="(sitem, idx) in oitem.lock.filter(
                        (o, i) =>
                          oitem.lock.findIndex((f) => o.lock_id == f.lock_id) ==
                          i
                      )"
                      :key="idx"
                    >
                      <view class="le">
                        <text
                          class="iconfont icon-a-jigouguanliduantubiao_huaban1fuben13"
                          style="font-size: 35rpx; margin-left: -5rpx"
                        ></text>
                        <text>门锁-{{ sitem?.lock_id }}</text>
                      </view>
                    </view>
                  </view>
                </template>
              </view>
            </view>
            <!-- emty -->
            <view class="emty-box" v-if="classList.length == 0">
              <image :src="emtyImg" class="emty-img" mode="aspectFill"></image>
              <view>暂无分组</view>
            </view>
          </view>
          <view class="utils">
            <view @tap="emit('close')">取消</view>
            <button @tap="confirmCreateHandel">确认添加</button>
          </view>
        </view>
      </template>
    </MyPopup>
  </view>
</template>

<style lang="scss" >
.pop-content-class {
  padding: 30px;
  .content {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 40px;
    padding-top: 40px;
    padding-bottom: 40px;
  }
  .ipt {
    margin-bottom: 35px;
    padding-left: 40px;
    color: #999;
  }
  .pop-title {
    font-size: 32px;
    color: #000;
    text-align: center;
    padding-bottom: 40px;
    margin-top: 20px;
    font-weight: 700;
  }
  .card {
    .item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      .le {
        color: #939394;
      }
      .ri {
        color: #000;
      }
    }
  }
  input {
    // width: 681px;
    height: 101px;
    background: rgba(87, 114, 150, 0);
    border-radius: 20px;
    border: 2px solid #e9e9e9;
    padding-left: 20px;
  }
  .utils-sigin {
    display: flex;
    justify-content: center;
    border-top: 1px solid #e9e9e9;
    padding: 40px 0;
    margin-top: 50px;
  }
  .utils {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    // border-top: 1px solid #E9E9E9;
    padding-top: 80px;
    view {
      width: 181px;
      height: 88px;
      border-radius: 20px;
      font-size: 28px;
      color: #000;
      text-align: center;
      line-height: 88px;
    }
    button {
      width: 460px;
      height: 88px;
      background: #1352fd;
      border-radius: 20px;
      font-size: 28px;
      color: #fff;
      text-align: center;
      line-height: 88px;
    }
  }
  .class-box {
    .at-checkbox__icon-cnt {
      border-color: #6c7c9c;
    }
    .at-checkbox::before,
    .at-checkbox::after {
      border-color: transparent;
    }
    .at-checkbox__title {
      color: #000;
      font-weight: 700;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 360px;
      display: block;
    }
    > .utils {
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      padding-left: 72px;
      button {
        width: 424px;
      }
    }
    > .list {
      padding: 0 30rpx;
      box-sizing: border-box;
      padding-bottom: 300px;
      overflow: auto;
      height: 700rpx;
      .item {
        border: 1px solid #d7dbe8;
        min-height: 110px;
        box-sizing: border-box;
        margin-top: 30px;
        background: #ffffff;
        border-radius: 20px;
        padding: 20px;
        .tag {
          background: #f86811;
          border-radius: 15px;
          display: flex;
          justify-content: center;
          color: #fff;
          align-items: center;
          font-size: 30px;
          transform: translateX(10px);
          padding: 3px 10px;
          font-weight: 700;
        }
        ._bottom {
          border-top: 1px solid #d7dbe8;
          padding-top: 15px;
          padding-bottom: 20px;
          animation: opacity 0.3s;
          @keyframes opacity {
            0% {
              opacity: 0;
            }
            100% {
              opacity: 1;
            }
          }
          .tit {
            display: flex;
            align-items: center;
            font-size: 32px;
            color: #204eca;
            font-weight: 700;
            .icon-s {
              width: 32px;
              height: 32px;
              vertical-align: middle;
              margin-right: 10px;
            }
          }
          .li {
            display: flex;
            // justify-content: space-between;
            margin-top: 10px;
            font-size: 26px;
            .le {
              color: #090909;
              display: flex;
              align-items: center;
              .iconfont {
                font-size: 24px;
                color: #6f7c9a;
                margin-right: 5px;
              }
            }
            .ri {
              color: #0072ff;
              margin-left: 20px;
            }
          }
        }
        .top {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-bottom: 15px;
          padding-top: 15px;
          .left {
            display: flex;
            align-items: center;
            // width: 340px;
            font-size: 24px;
            .iconfont {
              font-size: 25px;
              margin-right: 7px;
              color: #000;
            }
            .title {
              margin-right: 10px;
              font-size: 36px;
              font-weight: 700;
              overflow: hidden;
              text-overflow: ellipsis;
              // width: 100%;
            }
          }
          .right {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 168px;
            height: 43px;
            image {
              width: 30px;
              height: 30px;
            }
          }
        }
      }
    }
    .emty-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #999;
      transform: translateY(-10%);
      .emty-img {
        width: 360px;
        height: 284px;
        margin: 0 auto;
      }
    }
  }
}
</style>
