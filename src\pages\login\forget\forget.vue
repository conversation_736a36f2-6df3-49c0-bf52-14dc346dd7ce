<template>
  <View class="container-forget">
    <view class="login-top">
      <view class="logo2"><image :src="Logo2" class="logo2-img"></image></view>
      <view class="t1">忘记密码</view>
    </view>
    <view class="login-form">
      <MyInput placeholder="请输入">
        <template #prefix><text>手机号</text></template>
        <template #content><input class="my-input-m" v-model="formState.mobile" type="number" placeholder="请输入" /></template>
      </MyInput>
      <MyInput prefix="验证码" placeholder="请输入">
        <template #suffix>
          <text class="btn-text-small" v-if="!isSend" @tap="getSmsCode">获取验证码</text>
          <text class="color-low" v-if="isSend">{{timeout}}秒后重发</text>
        </template>
        <template #content><input class="my-input-m" v-model="formState.sms_code" type="number" placeholder="请输入" /></template>
      </MyInput>
      <MyInput prefix="设置密码" placeholder="请输入">
        <template #suffix>
            <image @tap="onChangePasswordType" v-if="passwordType === 'password'" :src="iconEye" class="icon-eye"></image>
            <image @tap="onChangePasswordType" v-if="passwordType === 'text'" :src="iconEyeClose" class="icon-eye"></image>
        </template>
        <template #content><input class="my-input-m" v-model="formState.password" :type="passwordType" placeholder="请输入" /></template>
      </MyInput>
      <MyInput prefix="确认密码" placeholder="请输入">
        <template #suffix>
            <image @tap="onChangePasswordType" v-if="passwordType === 'password'" :src="iconEye" class="icon-eye"></image>
            <image @tap="onChangePasswordType" v-if="passwordType === 'text'" :src="iconEyeClose" class="icon-eye"></image>
        </template>
        <template #content>
          <input class="my-input-m" v-model="formState.confirm_password" :type="passwordType" placeholder="请输入" />
        </template>
      </MyInput>
      <view class="privacy flex flex-v-center">
        <view class="privacy-left">
          <image @tap="onChangePrivacy(1)" v-if="!formState.privacy" :src="iconRadio" class="icon-radio"></image>
            <image @tap="onChangePrivacy(0)" v-if="formState.privacy" :src="iconRadioChecked" class="icon-radio"></image>
          <text @tap="onChangePrivacy(1)">阅读并同意</text> <text>用户手册</text> 和 <text>隐私政策</text></view>
      </view>
      <view>
        <button class="btn-primary" @tap="handleLogin">确认修改</button>
      </view>
    </view>
    <view class="reg" @tap="onLogin">
        立即登录 <image :src="iconRegArrow" class="icon-reg-arrow"></image>
    </view>
  </View>
</template>

<script setup>
  import { ref } from 'vue'
  import Taro, { useDidShow, useDidHide, useReady, useLoad } from '@tarojs/taro'
  import request from '@/utils/request'
  import {checkPhoneNumber, checkPassword} from '@/utils/index'
  import { useGlobalStore } from '@/stores'
  import MyInput from '@/components/MyInput'

  const Logo2 = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/logo2.png'
  const iconEye = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-eye.png'
  const iconEyeClose = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-eye-close.png'
  const iconRadio = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-radio.png'
  const iconRadioChecked = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-radio-checked.png'
  const iconRegArrow = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/icon-reg-arrow.png'

  definePageConfig({
    navigationBarTitleText: "忘记密码",
    navigationBarBackgroundColor: "#E9F2FF",
    navigationBarTextStyle: 'black'
  });

  const globalStore = useGlobalStore()

  const formState = ref({})

  const passwordType = ref('password')

  const onChangePasswordType = () => {
    if (passwordType.value === 'password') {
        passwordType.value = 'text'
    } else {
        passwordType.value = 'password'
    }
  }
  const onChangePrivacy = (val) => {
    formState.value.privacy = val
  }

  const handleLogin = () => {
    console.log(formState)
    if (!formState.value.mobile || !formState.value.password || !formState.value.confirm_password || !formState.value.sms_code) {
      Taro.showToast({
        title: '表单填写不全',
        icon: 'none'
      })
      return
    }
    if (!checkPhoneNumber(formState.value.mobile)) {
      Taro.showToast({
        title: '手机号码格式错误',
        icon: 'none'
      })
      return
    }
    if (!checkPassword(formState.value.password)) {
      Taro.showToast({
        title: '密码不能少于6位数',
        icon: 'none'
      })
      return
    }
    if (formState.value.password !== formState.value.confirm_password) {
      Taro.showToast({
        title: '两次输入的密码不一致',
        icon: 'none'
      })
      return
    }
    if (!formState.value.privacy) {
      Taro.showToast({
        title: '请阅读并同意用户手册及隐私协议',
        icon: 'none'
      })
      return
    }
    //发起网络请求
    request.post({
      url: 'user/changePassword',
      data: formState.value,
    }).then((res) => {
      Taro.showModal({
        title: '提示',
        content: '密码修改成功！请妥善保管',
        confirmText: '立即登录',
        showCancel: false,
        success: function (res) {
          if (res.confirm) {
            Taro.navigateBack()
          } else if (res.cancel) {
            console.log('用户点击取消')
          }
        }
      })
    })

  }

  const onLogin = () => {
    Taro.redirectTo({
      url: '/pages/login/login'
    })
  }

  const isSend = ref(false)
  const timeout = ref(60)

  const getSmsCode = () => {
    if (!formState.value.mobile) {
      Taro.showToast({
        title: '请输入手机号码',
        icon: 'none'
      })
      return
    }
    request.post({
      url: 'sendSmsCode',
      data: {
        mobile: formState.value.mobile
      }
    }).then(res => {
      isSend.value = true
      timeoutCountdown()
    })
  }

  const timeoutCountdown = () => {
    setTimeout(() => {
      timeout.value = timeout.value - 1

      if (timeout.value > 0) {
        timeoutCountdown()
      } else {
        isSend.value = false
        timeout.value = 60
      }
    }, 1000)
  }

</script>
<style lang="scss">

  page,body {
  background-color: #E9F2FF;
}
.container-forget {
  padding: 0 38px 38px 38px;

  .login-top {
    text-align: center;
    padding-top: 60px;
  }
  .t1 {
    font-size: 36px;
    margin-top: 38px;
  }
  .t2 {
    font-size: 30px;
    margin-top: 25px;
    color: #B6BEC5;
  }

  .logo2-img {
    width: 119px;
    height: 119px;
  }

  .login-form {
    padding-top: 60px;

    .privacy {
        padding: 36px 0 60px 0;

        .privacy-left {
            color: #B6BEC5;
            font-size: 24px;
            flex: 1;

            text {
                color: #6B8ADF;
            }
        }
        .privacy-right {
            font-size: 24px;
            color: #B6BEC5;
            width: 140px;
        }
    }
    .icon-eye {
        width: 36px;
        height: 27px;
    }
    .icon-radio {
      width: 26px;
      height: 26px;
      vertical-align: middle;
      margin-right: 10px;
    }
  }
  .reg {
    margin-top: 180px;
    text-align: center;
    color: #6B8ADF;
    font-size: 36px;
  }
  .icon-reg-arrow {
        width: 31px;
        height: 27px;
        vertical-align: middle;
        margin-left: 11px;
    }
}

</style>
