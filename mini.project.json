{"format": 2, "compileType": "mini", "miniprogramRoot": "dist/", "developOptions": {"hotReload": true, "parallel": false, "sourcemap": true, "skipTranspile": false}, "uploadExclude": ["src/**", "node_modules/**", "dist/prebundle/743.js.LICENSE.txt", "dist/prebundle/678.js.LICENSE.txt", "dist/prebundle/643.js.LICENSE.txt", "dist/prebundle/878.js.LICENSE.txt", "dist/prebundle/828.js.LICENSE.txt", "dist/vendors.js.LICENSE.txt", "dist/mini.project.json", "project.tt.json", "README.md", "project.private.config.json", "project.config.json", "yarn.lock", "__tests__", "pnpm-lock.yaml", "phpstorm.config.js", "package-lock.json", "jsconfig.json", "jest.config.js", "babel.config.js", "patches", "config", ".vscode", ".giti<PERSON>re", ".git", ".mini-ide", ".swc", "dist/pages/index/index.js.map", "dist/pages/my/changePwd/changePwd.js.map", "dist/pages/my/changeBusinessPwd/changeBusinessPwd.js.map", "dist/pages/my/fdd/fdd.js.map", "dist/pages/my/changePhone/changePhone.js.map", "dist/pages/my/my.js.map", "dist/pages/bindDevice/bindDevice.js.map", "dist/pages/conf/conf.js.map", "dist/pages/userInfo/userInfo.js.map", "dist/pages/qa/index.js.map", "dist/pages/qa/detail.js.map", "dist/pages/chuzu/chuzu.js.map", "dist/pages/bill/bill.js.map", "dist/pages/messagePage/messagePage.js.map", "dist/pages/webview/webview.js.map", "dist/pages/webview/article.js.map", "dist/pages/repair/detail/detail.js.map", "dist/pages/repair/repair.js.map", "dist/pages/article/article.js.map", "dist/pages/login/loginByCode.js.map", "dist/pages/login/login.js.map", "dist/pages/who/who.js.map", "dist/pages/message/message.js.map", "dist/pages/qianbao/qianbao.js.map", "dist/pages/house/lockPwdEdit/lockPwdEdit.js.map", "dist/pages/house/lockDetail/lockDetail.js.map", "dist/pages/house/addWaterDevice/addWaterDevice.js.map", "dist/pages/house/lockPwdAdmin/lockPwdAdmin.js.map", "dist/pages/house/add/add.js.map", "dist/pages/house/addLock/addLock.js.map", "dist/pages/house/addDevice/addDevice.js.map", "dist/pages/house/lockRizhi/lockRizhi.js.map", "dist/pages/house/edit/edit.js.map", "dist/pages/house/detail/detail.js.map", "dist/pages/house/passwordAdmin/passwordAdmin.js.map", "dist/pages/house/house.js.map", "dist/pages/other/publicLog/publicLog.js.map", "dist/pages/other/waterDiNumEdit/waterDiNumEdit.js.map", "dist/pages/other/publicShareSonSetting/publicShareSonSetting.js.map", "dist/pages/other/publicShareSetting/publicShareSetting.js.map", "dist/pages/other/houseTagEdit/houseTagEdit.js.map", "dist/pages/other/publicLogDetail/publicLogDetail.js.map", "dist/pages/other/houseTagAdmin/houseTagAdmin.js.map", "dist/pages/other/publicShare/publicShare.js.map", "dist/pages/other/waterDetiveDetail/waterDetiveDetail.js.map", "dist/pages/tenant/order/order.js.map", "dist/pages/tenant/tenantGongAnInfo/tenantGongAnInfo.js.map", "dist/pages/tenant/bill/bill.js.map", "dist/pages/tenant/tenantInfo/tenantInfo.js.map", "dist/pages/tenant/house/house.js.map", "dist/pages/tenant/repair/repair.js.map", "dist/pages/tenant/device/device.js.map", "dist/pages/tenant/contract/contract.js.map", "dist/pages/tenant/tempDevice/tempDevice.js.map", "dist/pages/tenant/deviceDetail/deviceDetail.js.map", "dist/pages/tenant/repairAdd/repairAdd.js.map", "dist/pages/tenant/repairDetail/repairDetail.js.map", "dist/pages/device/log/log.js.map", "dist/pages/device/moreList/moreList.js.map", "dist/pages/device/failOrder/failOrder.js.map", "dist/pages/device/rebind/rebind.js.map", "dist/pages/device/consumption-record/consumption-record.js.map", "dist/pages/device/detail/detail.js.map", "dist/pages/device/recharge/recharge.js.map", "dist/pages/device/fastCheck/fastCheck.js.map", "dist/pages/device/device.js.map", "dist/pages/contract/add/add.js.map", "dist/pages/contract/billCancel/billCancel.js.map", "dist/pages/contract/billPayForm/billPayForm.js.map", "dist/pages/contract/billPay/billPay.js.map", "dist/pages/contract/createZcontract/createZcontract.js.map", "dist/pages/contract/breakForm/breakForm.js.map", "dist/pages/contract/createResult/createResult.js.map", "dist/pages/contract/rent/rent.js.map", "dist/pages/contract/chooseType/chooseType.js.map", "dist/pages/contract/form/form.js.map", "dist/pages/contract/tenant/tenant.js.map", "dist/pages/contract/bill/bill.js.map", "dist/pages/contract/detail/detail.js.map", "dist/pages/contract/billList/billList.js.map", "dist/pages/contract/contract.js.map", "dist/pages/money/stat/stat.js.map", "dist/pages/money/draw.js.map", "dist/pages/money/cashLog.js.map", "dist/pages/money/cardInfo.js.map", "dist/pages/money/card.js.map", "dist/pages/money/bindCard.js.map", "dist/pages/money/bill.js.map", "dist/customize-tab-bar/index.js.map", "dist/vendors.js.map", "dist/taro.js.map", "dist/runtime.js.map", "dist/common.js.map", "dist/app.js.map"], "compileOptions": {"globalObjectMode": "enable", "component2": true, "transpile": {"script": {"ignore": ["node_modules/**"]}}}}