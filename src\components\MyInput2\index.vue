<template>
  <view class="my-input2">
    <view class="my-input2-prefix" v-if="$slots.prefix"><slot name="prefix"></slot></view>
    <view class="my-input2-prefix" v-if="!$slots.prefix && prefix">{{prefix}}</view>
    <view class="my-input2-content">
      <slot name="content"></slot>
    </view>
    <view class="my-input2-suffix" v-if="$slots.suffix"><slot name="suffix"></slot></view>
    <view class="my-input2-suffix" v-if="!$slots.suffix && suffix">{{suffix}}</view>
  </view>
</template>

<script setup>
  const props = defineProps({
    val: {
      type: String,
      required: false,
      default: ''
    },
    placeholder: {
      type: String,
      required:false,
      default: ''
    },
    prefix: {
      type: String,
      required:false,
      default: ''
    },
    suffix: {
      type: String,
      required:false,
      default: ''
    },
    type: {
      type: String,
      required:false,
      default: 'text'
    }
  })

  const emit = defineEmits(['change'])

  const onChange = (e) => {
    emit('change', e.detail.value)
  }

</script>
<style>
.my-input2 {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  border: 1px solid #E9E9E9;
  padding: 0 35px;
  border-radius: 20px;
}
.my-input2-prefix {
  width: 120px;
  text-align: left;
  font-size: 28px;
  line-height: 83px;
  font-family: OPPOSans;
  font-weight: 500;
}
.my-input2-content {
  flex: 1;
}
.my-input2-m {
  font-size: 28px;
}
.my-input2-m:-ms-input-placeholder {
  color: #E9F2FF;
}
.my-input2-suffix {
  max-width: 160px;
  min-width: 80px;
  text-align: right;
  line-height: 83px;
  font-size: 28px;
}
</style>
