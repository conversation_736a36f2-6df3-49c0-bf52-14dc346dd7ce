.container {
  padding-bottom: 150px;
  
  .status-filter {
    background-color: #1352FD;
    padding: 0 13px 25px 13px;
    text-align: center;

    .tag {
      margin: 0 4px;
    }
  }
  .house-stat {
    background-color: #CFDCFF;
    padding: 20px 37px;
    font-size: 26px;

    .stat-num {
      font-size: 40px;
      color: #1352FD;
      font-weight: 400;
      font-family: Bahnschrift;
      display: inline-block;
      margin: 0 10px;
    }
    .link {
      color: #1352FD;
      font-weight: 500;
      font-size: 26px;
      vertical-align: middle;
    }
    .icon-about-status {
      width: 26px; 
      height: 26px; 
      vertical-align: middle;
    }
  }
  .house-list {
    padding: 50px 25px;
    .item-box-q {
      margin-bottom: 31px;
    }

    .house-item {
      border-radius: 14px;
      background-color: #F4EDF8;
      // margin-bottom: 31px;
      padding: 26px 21px 29px 48px;
      position: relative;

      .house-title {
        font-family: OPPOSans;
        font-weight: 500;
        font-size: 36px;
      }

      .icon-house-mini {
        width: 36px;
        height: 31px;
        vertical-align: middle;
        display: inline-block;
        margin-right: 10px;
      }
      .house-day {
        font-size: 26px;
        font-weight: 500;
      }
      .house-rent {
        background-color: #F86D37;
        padding: 5px 12px;
        font-size: 24px;
        color: #FFFFFF;
        display: inline-block;
        border-radius: 12px;
        margin-top: 14p;
      }
      .house-tenant {
        font-size: 26px;
      }
      .house-status {
        width: 134rpx;
        height: 47rpx;
        background: #7B459A;
        border-radius: 14rpx 0rpx 0rpx 14rpx;
        font-size: 26rpx;
        font-family: OPPOSans;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 47rpx;
        position: absolute;
        right: 0;
        top: 20px;
        text-align: center;
      }
      .house-left-border {
        width: 20rpx;
        height: 100%;
        background: #7B459A;
        border-radius: 14rpx 0rpx 0rpx 14rpx;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
    .house-status-vacant {
      background-color: #EFF5FF;

      .house-status {
        background-color: #3F83FF;
      }
      .house-left-border {
        background-color: #3F83FF;
      }
    }
    .house-status-signing {
      background-color: #ECFDEA;
      .house-status {
        background-color: #6BD65A;
      }
      .house-left-border {
        background-color: #74D964;
      }
    }
    .house-status-expire {
      background-color: #FDEEF6;
      .house-status, .house-left-border {
        background-color: #E6479C;
      }
    }
    .house-status-expired {
      background-color: #FFF1E6;
      .house-status {
        background-color: #F86D37;
      }
      .house-left-border {
        background-color: #F86D37;
      }
    }
    .house-status-fail {
      background-color: #FEECE9;

      .house-status {
        background-color: #F93A1B;
      }
      .house-left-border {
        background-color: #F93A1A;
      }
    }
  }
}
