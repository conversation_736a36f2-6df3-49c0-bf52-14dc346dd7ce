<template>
  <view class="container">
    <view class="flex device-top">
        <view class="device-icon device-icon-new" style="background-color: #1454c8;">
          <MyIcon v-if="device?.type != 2" icon="icon-dianbiao" width="44rpx" height="57rpx"></MyIcon>
         <image style="width: 43rpx;height: 76rpx;margin-top: -8rpx;" v-else :src="shuiImg" mode="aspectFill"></image>
        </view>
        <view style="flex: 1"><view class="device-name">房间号：{{ device?.house?.name }}</view><view class="device-sn">
          <text v-if="device.net_type === 1 || isNetNoSingle">{{ device?.type == 2 ? '4G双模水表号' : '4G双模电表号' }}</text><text v-if="device.net_type !== 1 && !isNetNoSingle">{{ device?.type == 2 ? '蓝牙水表号' : '蓝牙电表号' }}</text>: {{device.sn}} </view></view>
          <!-- 谷峰表配置 -->
          <image v-if="device?.type == 5 || device?.type == 6" @tap="deviceGConfigHandel" style="width: 70rpx;height: 58rpx;margin-right: 15rpx;" src="https://yimits.oss-cn-beijing.aliyuncs.com/images/fl_setting_2.png" mode="aspectFill"></image>
          <view class="params_box" @tap="paramsClick" v-if="device?.type != 2">
            <text class="iconfont icon-nav_icon_cssz_spe"></text>
            <text>参数</text>
          </view>
        <view style="width: 64rpx;" v-if="device.net_type === 1 || isNetNoSingle">
          <MyIcon class="sing-con" :icon="'signal/' +  device.signal_num" width="60rpx" height="49rpx"></MyIcon>
        </view>
    </view>
    <view class="device-du">
      <view class="device-du1">{{ device?.type == 2 ? '剩余水量(m³)' : (isShare || device?.type == 5 || device?.type == 6) ? '剩余金额(元)' : '剩余电量(度)' }}</view>
      <view class="device-du2">{{ isShare ? (device.du * (Number(device.price) + Number(device.agent?.service))).toFixed(2) || 0 : device.du}}</view>
      <view class="device-du3">读表时间</view>
      <view class="device-du4"><text v-if="!device.read_at">未读表</text><text v-if="device.read_at">{{device.read_at}}</text></view>
    </view>
    <view class="flex text-center flex-v-center bot-btn-box">
      <view class="device-total">
        <view class="val">{{ Number(device?.total || 0).toFixed(2) }}</view>
        <view class="lab">{{ device?.type == 2 ? '总用水量' : '总用电量' }}</view>
      </view>
      <view class="device-line1"></view>
      <view class="device-price">
        <view class="val" style="font-size: 32rpx;" v-if="device?.type == 5 || device?.type == 6">分段电价</view>
        <view class="val" v-else>{{ device.price }}</view>
        <view class="lab" v-if="device?.type == 5 || device?.type == 6">电价</view>
        <view class="lab" v-else>{{ device?.type == 2 ? '水价(元/m³)' : '电价(元/度)' }}</view>
        <view class="iconfont icon-bianji" @tap="onShowPrice"></view>
      </view>
    </view>
    <view class="main-btn flex flex-space-between text-center">
      <view class="main-btn-col" @tap="onChangeStatus">
        <view><MyIcon icon="icon-device-close" width="54rpx" height="54rpx"></MyIcon></view>
        <view class="mt10"><text v-if="device.status !== 2">{{ device?.type == 2 ? '点击断水' : '点击断电' }}</text><text v-if="device.status === 2">{{ device?.type == 2 ? '点击通水' : '点击通电' }}</text></view>
      </view>
      <!-- <view class="main-btn-col" @tap="onShowPrice">
        <view><MyIcon icon="icon-device-price" width="54rpx" height="54rpx"></MyIcon></view>
        <view class="mt10">单价</view>
      </view> -->
      <view class="main-btn-col" @tap="onShowSpeed" v-if="device?.type != 2">
        <view><MyIcon icon="icon-device-speed" width="54rpx" height="54rpx"></MyIcon></view>
        <view class="mt10">{{ device?.type == 4 || device?.type == 6 ? '变比设置' : '损耗设置' }}</view>
      </view>
      <!-- <view class="main-btn-col" @tap="dishuHandel()" v-else>
        <view>
          <image
            :src="dishu"
            style="width: 54rpx; height: 54rpx"
            mode="aspectFill"
          ></image>
        </view>
        <view class="mt10">底数校准</view>
      </view> -->
      <view class="main-btn-col" @tap="onRecharge" v-if="device?.business_show == 1">
        <view><MyIcon icon="icon-device-recharge" width="54rpx" height="54rpx"></MyIcon></view>
        <view class="mt10">免费充值</view>
      </view>
      <!-- <view class="main-btn-col btn-share">
        <button open-type="share" :plain="true" size="mini" class="text-btn">

        </button>
        <view><MyIcon icon="icon-device-share" width="54rpx" height="54rpx"></MyIcon></view>
        <view class="mt10">分享</view>
      </view> -->
    </view>
    <view class="other-btn flex flex-row flex-space-between">
      <view class="other-btn-col" @tap="shareHandel(device?.need_people)">
        <!-- device?.need_people == 1 ? '' :  'share'-->
        <button v-if="device?.need_people != 1" open-type="share" :plain="true" size="mini" class="btn-share-c">
        </button>
        <button v-if="device?.need_people == 1" :open-type="fastDetailInfo != null ? 'share' :  '' " :plain="true" size="mini" class="btn-share-c">
        </button>
        <MyIcon icon="icon-device-share" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">{{ isAp ? '支付宝分享' : '微信分享' }}</text>
        <!-- <text class="iconfont icon-wenhao"></text> -->
      </view>
      <view class="other-btn-col" @tap="bindHandel" v-if="device?.need_people != 1 && device?.type != 2">
        <text class="iconfont icon-bangding" style="font-size: 33rpx;"></text> <text class="">手机号分享</text>
        <!-- <text class="iconfont icon-wenhao"></text> -->
      </view>
      <view class="other-btn-col" @tap="fastCheckInHandel" v-if="device?.type != 2">
        <text class="iconfont icon-bangding" style="font-size: 33rpx;"></text> <text class="">快速入住</text>
        <!-- <text class="iconfont icon-wenhao"></text> -->
      </view>
      <view class="other-btn-col" @tap="onQueryStatus">
        <MyIcon icon="icon-device-cb" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">{{ device?.type == 2 ? '水表抄表' : '抄表' }}</text>
      </view>
      <view class="other-btn-col" @tap="onShowGaoJing" v-if="device?.type != 2">
        <MyIcon icon="icon-device-gj" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">告警设置</text>
      </view>
      <view class="other-btn-col" @tap="onShowQrCode">
        <MyIcon icon="icon-device-qr" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">二维码</text>
      </view>
      <!-- <view class="other-btn-col">
        <MyIcon icon="icon-device-bglog" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">设备变更</text>
      </view> -->
      <!-- <view class="other-btn-col">
        <MyIcon icon="icon-device-cblog" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">抄表记录</text>
      </view> -->
      <view class="other-btn-col" @tap="onClear">
        <MyIcon icon="icon-device-clear" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">{{ device?.type == 2 ? '水量清零' : '电量清零' }}</text>
      </view>
      <view class="other-btn-col" @tap="onRechargeLog">
        <MyIcon icon="icon-device-czlog" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">充值记录</text>
      </view>
      <view class="other-btn-col" @tap="onClearLog">
        <MyIcon icon="icon-device-qllog" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">清零记录</text>
      </view>
      <view class="other-btn-col" @tap="onDelete">
        <MyIcon icon="icon-device-bglog" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">删除设备</text>
      </view>
      <!-- <view class="other-btn-col" @tap="onChangeHouse" v-if="device?.type != 2">
        <MyIcon icon="icon-device-bglog" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">设备换绑</text>
      </view> -->
      <view class="other-btn-col" @tap="onNiteReloadHnadel" v-if="(device.net_type === 1 || isNetNoSingle) && device.is_master == 1">
        <MyIcon icon="icon-device-clear" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">网络刷新</text>
      </view>
      <view class="other-btn-col" @tap="reloadMainHandel" v-if="device.client_id && device.is_master == 0 && device.signal_num == 0">
        <MyIcon icon="icon-device-clear" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">刷新网络</text>
      </view>
      <view class="other-btn-col" @tap="toLTempOrder" v-if="device.show_order == 1">
        <MyIcon icon="icon-device-bglog" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">临时订单</text>
      </view>
      <view class="other-btn-col" @tap="linshiJumpHandel" v-if="device?.agent?.type != 2 && device?.type != 2">
        <MyIcon icon="icon-device-recharge" width="33rpx" height="33rpx"></MyIcon> <text class="text-v-center">临时用电</text>
      </view>
      <!-- <view class="other-btn-col" @tap="onSwitchHandel" v-if="(device?.agent?.type == 2)">
        <text class="iconfont icon-setting" style="font-size: 28rpx;margin-right: 15rpx;display:inline-block;transform: translateY(4rpx);"></text>
        <text class="text-v-center">控制显示</text>
      </view> -->
    </view>

    <MyPopup :show="showGaoJing" title="告警设置" @close="onCloseGaoJing">
      <template #content>
        <view style="color: #000000; padding: 35rpx;">
          <view>剩余电量告警通知数值</view>
          <view class="text-center" style="font-size: 100rpx; font-weight: 500; padding: 30rpx 0;"><text v-if="device.low > 0"> &lt; {{device.low}}</text><text v-if="device.low === 0">未设置</text></view>
          <view class="p20"><MyInput2 suffix="度">
            <template #content><input class="input-custom" type="number" v-model="gjLow" placeholder="请输入剩余度数" /></template>
          </MyInput2></view>
          <view class="text-center" style="padding: 30rpx 170rpx;">
            <button class="btn-primary" @tap="onSubmitGaoJing">确定</button>
          </view>
        </view>
      </template>
    </MyPopup>

    <MyPopup :show="paramsShow" title="参数详情" @close="paramsShow = false">
      <template #content>
        <view class="params-container">
          <view class="li">
            <view>
              电压：
            </view>
            <view>{{ device?.volt >= 400 ? '--' : (device?.volt || '0') }} V</view>
          </view>
          <view class="li">
            <view>
              电流：
            </view>
            <view>{{ device?.type == 4 || device?.type == 6 ?  ((device?.speed / 1000) * device?.curr).toFixed(2) || '0' : Number(device?.curr).toFixed(2) || '0' }} A</view>
          </view>
          <view class="li">
            <view>
              功率：
            </view>
            <view>{{ device?.type == 4 || device?.type == 6 ? ((device?.speed / 1000) * device?.volt * device?.curr).toFixed(2) || '0' : (device?.volt * device?.curr).toFixed(2) || '0' }} W</view>
          </view>
          <view class="btn" @tap="paramsShow = false">
            确认
          </view>
        </view>
      </template>
    </MyPopup>

    <!-- 谷峰表配置-展示 -->
    <MyPopup :show="gfShowPopup"  @close="gfShowPopup = false">
      <template #content>
        <view class="gf-container">
          <view class="title">
            <text class="t">{{ tableData && tableData[0] && tableData[0]?.provinceName && tableData[0]?.provinceName[0] }} 峰谷时段划分</text>
            <view class="r" @tap="refshHandelr(true)">
              <image src="https://yimits.oss-cn-beijing.aliyuncs.com/images/refsule_2.png" mode="aspectFill"></image>
              <text>刷新</text>
            </view>
          </view>
          <view class="container">
            <view class="table-wrapper" v-if="tableData">
              <view class="table">
                <view class="thead">
                  <view class="tr">
                    <view class="th" style="width: 105rpx;">季节</view>
                    <view class="th" style="width: 134rpx;padding: 0 10rpx;">月份</view>
                    <view class="th" style="width: 84rpx;">时段</view>
                    <view class="th" style="width: 386rpx;">具体时间</view>
                  </view>
                </view>
                <view class="tbody">
                  <!-- {/* 春秋季 */} -->
                  <view class="tr" v-for="(item, index) in tableData" :key="index">
                    <view class="td" style="min-height: 280rpx;width: 105rpx;">{{ item.name }}</view>
                    <view class="td" style="width: 134rpx;">{{ item.month?.join(',') || '/' }}</view>
                    <view class="td" style="width: 470rpx;">
                      <view class="td-son">
                        <view>尖峰</view>
                        <view>{{ item.coef4Merge.length ? item.coef4Merge?.join('，') : '/' }}</view>
                      </view>
                      <view class="td-son">
                        <view>高峰</view>
                        <view>{{ item.coef3Merge.length ? item.coef3Merge?.join('，') : '/' }}</view>
                      </view>
                      <view class="td-son">
                        <view>
                          平峰
                        </view>
                        <view> {{ item.coef2Merge.length ? item.coef2Merge?.join('，') : '/' }}</view>
                      </view>
                      <view class="td-son">
                        <view>谷峰</view>
                        <view> {{ item.coef1Merge.length ? item.coef1Merge?.join('，') : '/' }}</view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <view v-else style="text-align: center;">暂无数据~</view>
          </view>
          <view class="btn"  @tap="gfShowPopup = false; gfConfigShowPopup = true;refshHandelr(false)">
            {{ tableData ? '修改' : '配置' }}
          </view>
        </view>
      </template>
    </MyPopup>

    <!-- 谷峰表-配置 -->
    <MyPopup :show="gfConfigShowPopup"  @close="gfConfigShowPopup = false">
      <template #content>
        <view class="gf-container gf-config-container">
          <view class="title">
            <text class="t">设置</text>
          </view>
          <view class="container">
            <!-- 上 -->
            <view class="top">
              <view class="item">
                <view class="label">
                  选择省份
                </view>
                <view class="right">
                  <picker @change="bindPickerChange" :value="areaIndex" :range="areaList" range-key="name">
                      <text>{{ areaList[areaIndex]?.name }}</text>
                  </picker>
                  <text class="iconfont icon-youjiantou1"></text>
                </view>
              </view>
              <view class="item">
                <view class="label">
                  选择计费组
                </view>
                <view class="right">
                  <!-- <picker @change="bindPickerSeasonIndexChange" :value="seasonIndex" :range="tableData" range-key="name">
                      <text>{{ tableData && tableData.length && tableData[seasonIndex]?.name }}</text>
                  </picker>
                  <text class="iconfont icon-youjiantou1"></text> -->
                  <view class="list">
                    <view class="month iconfont" :class="tableData && tableData.length && idx == seasonIndex ? 'active' : ''" v-for="(item,idx) in tableData" :key="item.name" @tap="seasonIndex = idx">
                      {{ item?.name }}
                    </view>
                  </view>
                </view>
              </view>
              <view class="item3">
                <view class="label">
                  对应月份
                </view>
                <view class="right">
                  <!-- 月份列表 -->
                  <view class="list">
                    <view class="month iconfont" :class="tableData && tableData.length && tableData[seasonIndex]?.month.includes(i) ? 'active' : getSeasonMonthIndex(i) != -1 ?  'disabled' : ''" v-for="i in 12" :key="i" @tap="onMonthSelect(i)">
                      {{ i }}月
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 中 -->
            <view class="middle">
              <view class="utils">
                <view class="li" :class="preiodIndex === index ? 'active' : ''" v-for="(item,index) in preiods" :key="item.value" @tap="preiodIndex = index">
                  <text> {{ item.label }}</text>
                  <text v-if="index !== preiods.length - 1" class="iconfont icon-youjiantou1"></text>
                </view>
              </view>
            </view>
             <!-- 下 -->
            <view class="_bottom">
              <view class="label">选择时段开始时间 <text @tap="resetTimeSolt">重置</text></view>
              <view class="list">
                <!-- getSeasonTimeIndex(time).idx != -1 ?  'disabled' : -->
                <view class="time iconfont" :class="transTime?.includes(time) ? 'active' : getSeasonTimeIndex(time)?.idx != -1 ?  'disabled' : ''" v-for="time in timeList" :key="i" @tap="onSelectTime(time)">
                    {{ time }}
                </view>
              </view>
            </view>
          </view>
          <view class="btn" @tap="stepHandeler">
            {{ preiodIndex == 3 ? '保存' : '下一步' }}
          </view>
        </view>
      </template>
    </MyPopup>

    <MyPopup :show="showQrCode" title="设备二维码" @close="onCloseQrCode">
      <template #content>
        <view style="color: #000000; padding: 35rpx;">
          <view>
            <view class="flex device-top">
              <view class="device-icon">
                <MyIcon icon="icon-dianbiao" width="44rpx" height="57rpx"></MyIcon>
              </view>
              <view style="flex: 1;">
              <!-- <view class="device-name" style="color: #333;">电表</view> -->
              <view class="device-name" style="color: #333;">房间号: {{ device?.house?.name }} </view>
              <!-- <view class="device-sn" style="color: #333;">
                <text v-if="device.net_type === 1 || isNetNoSingle">4G双模电表号</text>
                <text v-if="device.net_type !== 1 && !isNetNoSingle">蓝牙电表号</text>: {{device.sn}} 
              </view> -->
            </view>
            </view>
          </view>
          <view class="text-center p20" @longpress="onLongPress(device.qrcode)" @LongTap="onLongPress(device.qrcode)">
            <image @load="drawCanvas" @Ready="drawCanvas" :src="device.qrcode" style="width: 300rpx; height: 300rpx" ></image>
          </view>
          <view class="text-center p20 font-24">长按保存到相册 </view>
        </view>
      </template>
    </MyPopup>

    <MyPopup :show="showSpeed" :title="device?.type == 4 || device?.type == 6 ? '变比设置' : '线路损耗'" @close="onCloseSpeed">
      <template #content>
        <view style="color: #000000; padding: 35rpx;">
          <!-- <view class="font-24 set-c-f">
            第1步：点【读取】按键，查看基础数值
          </view> -->
          <view class="font-24 set-c-f">
            {{ device?.type == 4 || device?.type == 6 ? '默认值：1倍' : '默认值：100' }}
          </view>
          <view class="font-24 set-c-f">
            {{ device?.type == 4 || device?.type == 6 ? '如互感器变比参数：100/5A,输入20倍' : '如增加1%的公摊电费，输入101' }}
          </view>
          <view class="font-24 set-c-f">
            点【设置】按键，等待提示“操作成功”
          </view>
          <view class="input-box"><MyInput2 :suffix="device?.type == 4 || device?.type == 6 ? '倍' : '%'">
            <template #content><input class="input-custom gongtaninput" type="digit" v-model="speed" cursor-spacing="120" :placeholder="device?.type == 4 || device?.type == 6 ? '请输入互感器变比倍数' : '请输入线路损耗比例'" /></template>
          </MyInput2></view>

          <view class="text-center" style="padding: 30rpx;">
            <button class="btn-primary btn-inline" style="width: 190rpx;" @tap="onQuerySpeed">读取</button>
            <button class="btn-second btn-inline ml33" style="width: 190rpx;" @tap="onSubmitSpeed">设置</button>
          </view>
        </view>
      </template>
    </MyPopup>

    <!-- <MyPopup :show="showSwitch" title="选项控制" @close="showSwitch = false">
      <template #content>
        <view style="color: #000000; padding: 35rpx;">
          <view class="p20">
            <at-switch
              title="是否显示基础电价"
              :checked="isShowBase"
              @change="changeSwitch($event,1)"
            />
            <at-switch
              title="是否显示服务费"
              :checked="isShowServe"
              @change="changeSwitch($event,2)"
            />
          </view>
          <view class="text-center" style="padding: 30rpx;">
            <button class="btn-primary btn-inline" style="width: 220rpx;" @tap="onSubmitSwitch">确定</button>
          </view>
        </view>
      </template>
    </MyPopup> -->

    <MyPopup :show="showPrice" :title="device?.type == 2 ? '水量定价' : '电量定价'" @close="onClosePrice">
      <template #content>
        <view style="color: #000000; padding: 35rpx;">
          <!-- 峰谷表 -->
          <view class="p20" v-if="device?.type == 5 || device?.type == 6">
            <MyInput2 suffix="元" prefix="尖峰电价">
              <template #content><input class="input-custom" type="digit" cursor-spacing="120" v-model="coef4" placeholder="请输入尖峰电价" /></template>
            </MyInput2>
            <MyInput2 suffix="元" prefix="高峰电价">
              <template #content><input class="input-custom" type="digit" cursor-spacing="120" v-model="coef3" placeholder="请输入高峰电价" /></template>
            </MyInput2>
            <MyInput2 suffix="元" prefix="平段电价">
              <template #content><input class="input-custom" type="digit" cursor-spacing="120" v-model="coef2" placeholder="请输入平段电价" /></template>
            </MyInput2>
            <MyInput2 suffix="元" prefix="低谷电价">
              <template #content><input class="input-custom" type="digit" cursor-spacing="120" v-model="coef1" placeholder="请输入低谷电价" /></template>
            </MyInput2>
          </view>
          <template v-else>
            <view class="font-24">
            {{ device?.type == 2 ? ' 综合水价' : ' 综合电价' }} {{ price }} {{ device?.type == 2 ? ' (元/m³)' : ' (元/度)' }}
            </view>
            <view class="p20">
              <MyInput2 suffix="元" :prefix="device?.type == 2 ? '基础水价' : '基础电价'">
                <template #content><input class="input-custom" type="digit" cursor-spacing="120" v-model="basic_price" :placeholder="device?.type == 2 ? '请输入基础水价金额' : '请输入基础电价金额'" /></template>
              </MyInput2>
              <MyInput2 suffix="元" prefix="服务费">
                <template #content><input class="input-custom" type="digit" cursor-spacing="120" v-model="service_price" :placeholder="device?.type == 2 ? '请输入服务费金额' : '请输入服务费金额'" /></template>
              </MyInput2>
            </view>
          </template>
          <view class="text-center" style="padding: 30rpx;">
            <button class="btn-primary btn-inline" style="width: 220rpx;" @tap="onSubmitPrice">确定</button>
          </view>
        </view>
      </template>
    </MyPopup>

    <MyPopup :show="showBindPhone" title="手机号分享" @close="showBindPhone = false">
      <template #content>
        <view style="color: #000000; padding: 35rpx;">
          <view class="font-24 custom-top">
            <view>
              <text>房间号：</text>
              <text>{{ device.house?.name }}</text>
            </view>
            <view>
              <text>电表号：</text>
              <text>{{device.sn}}</text>
            </view>
          </view>
          <view class="phone">
            房客手机号：
          </view>
          <view class="input-box">
            <input class="input-custom-c" :style="isAp ? 'width:88%' : ''" cursor-spacing="120" type="digit" maxlength="11" v-model="bindPhone" placeholder="请输入手机号" />
         </view>

          <view class="text-center" style="padding: 30rpx;">
            <button class="btn-primary btn-inline" style="width: 220rpx;" @tap="onSubmitBindPhone">确定</button>
          </view>
        </view>
      </template>
    </MyPopup>

    <YModal
      title="房客充值电费步骤"
      confirmText="下一步"
      :show="showTipModal"
      @close="showTipModal = false"
      @confirm="tipConfirmHandel"
      showCancel
    >
      <template #content>
        <view class="content-modal">
          <view>
            第一步：
          </view>
          <view>
            房客微信搜索小程序：“闪租婆”
          </view>
          <view>
            第二步：
          </view>
          <view>
            打开并登录“闪租婆”，即可充值电费
          </view>
        </view>
      </template>
    </YModal>

    <YModal
      title="开通成功!"
      confirmText="确认"
      :show="showTipSusModal"
      @close="showTipSusModal = false"
      @confirm="tipSusConfirmHandel"
    >
      <template #content>
        <view class="content-modal content-modal2">
          <view>
            <text>房间号：</text>
            <text>{{ device.house?.name }}</text>
          </view>
          <view>
            <text>电表号：</text>
            <text>{{ device.sn }}</text>
          </view>
          <view>
            <text>房客手机号：</text>
            <text>{{ bindPhone }}</text>
          </view>
        </view>
      </template>
    </YModal>

    <YModal
      title="no"
      confirmText="no"
      :show="showNetModal"
      @close="showNetModal = false"
      @confirm="confirmNetHandel"
      :maskClose="false"
      :bodyStyle="{width:'680rpx',paddingTop:'100rpx',borderRadius:'30rpx',height: '625rpx'}"
    >
      <template #content>
        <view class=" content-modal2" style="text-align:center">
          <image :src="netErroImg" style="width: 218rpx;height:155rpx"></image>
          <view :style="{
            color: '#000',
            fontWeight: 700,
            marginTop: '79rpx',
            marginBottom: '38rpx',
            fontSize:'40rpx'}">远程网络<text style="color:#db2b2b ;">异常</text></view>
          <view>
            <text style="font-size: 35rpx;font-weight: 500;">与电表保持3米以内距离打开蓝牙进行操作</text>
          </view>
          <view class="btn-box" @tap="showNetModal = false">
            <view @tap="clickYesHandel" style="color: #000;">我知道了</view>
          </view>
        </view>
      </template>
    </YModal>

    <YModal
      title="温馨提示!"
      :show="wxShareModalSHow"
      @close="wxShareModalSHow = false"
      @confirm="confirmNetHandel"
      maskClose
    >
      <template #content>
        <view class="content-modal content-modal2">
          <!-- <view class="content-ipt">
            <input type="number" placeholder="请填写入住人数" v-model="wxNum"/>
            <text>人</text>
          </view> -->
          <view>
            新租客首次入住，请先完善信息！
          </view>
        </view>
      </template>
      <template #confirm>
        <view class="confirm" @tap="wxConfirmHandel">
          知道了
          <!-- <button :open-type="wxNum ? 'share' : ''" :plain="true" size="mini" class="share-btn" @tap="wxShareBtnHandel">
          </button> -->
        </view>
      </template>
    </YModal>

    <YModal
      title="温馨提示!"
      :show="globalStore.isWxShare"
      @close="globalStore.setShare(false)"
      @confirm="globalStore.setShare(false)"
      maskClose
    >
      <template #content>
        <view class="content-modal content-modal2">
          <view>
            分享给好友
          </view>
        </view>
      </template>
      <template #confirm>
        <view class="confirm">
          确认
          <button :open-type="'share'" :plain="true" size="mini" class="share-btn" @tap="wxShareBtnHandel">
          </button>
        </view>
      </template>
    </YModal>
    <GlobDialog @confirm="confirmGlobHandel" :device="device" @reloadMainHandel="reloadMainHandel"/>
   <YToast 
    :show="showToastTip"  
    width="200" 
    height="200" 
    padding="35rpx 35rpx" 
    :text="yToastText" 
    @close="showToastTip = false"
    :sencondText="curentSen"
    :closeSencond="3"
   />
   <ClearModal 
      :show="showClear"
      @close="showClear = false"
      @confirm="yesClearHandel"
      confirmText="确认清零"
      maskClose
      showCancel
      :device="device"
      :price="(device.du * device.price).toFixed(2)"
   />
    <canvas canvas-id="myCanvas"  id="myCanvas" :type="isAp ? '2d':''" @Ready="onCanvasReady" :width="apWidth" :height="apHeight"  style="width:300px; height:330px;opacity: 0;position: absolute;left: -100000px;z-index: -1;"></canvas>
  </view>
</template>
<script setup>
  import { ref, unref, computed  } from 'vue'
  import './detail.scss'
  import Taro, { useDidShow, useDidHide, useReady, useLoad, useUnload, useShareAppMessage, usePullDownRefresh, useReachBottom} from '@tarojs/taro'
  import request from '@/utils/request'
  import deviceConnect from '@/utils/deviceConnect'
  import {expandTimeRanges, formatTime, processData, combineTimeRanges, mergeTimeRanges, checkNetInfo, checkIsConnectRssi} from '@/utils/index'
  import { AtSwitch } from "taro-ui-vue3";
  import ClearModal from '../components/clearModal/index.vue'

  import MyIcon from '@/components/MyIcon'
  import MyPopup from '@/components/MyPopup'
  import MyInput2 from '@/components/MyInput2'

  import GlobDialog from '@/components/globDialog/index.vue'

  import YModal from '@/components/YModal/index.vue'
  import YToast from '@/components/YToast/index.vue'

  const isAp = process.env.TARO_ENV === "alipay";

  const showToastTip = ref(false)

  const showClear = ref(false)

  const yToastText = ref('秒后重试')

  const timerCoef = ref(null)

  const netErroImg = 'https://yimits.oss-cn-beijing.aliyuncs.com/images/net-erro.png'
  import shuiImg from '@/assets/water/device.png'
  import dishu from "@/assets/water/dishu.png";


  const preiods = ref([
    {
      value: 'coef4',
      expan:'coef4Expand',
      label: '尖峰'
    },
    {
      value: 'coef3',
      expan:'coef3Expand',
      label: '高峰'
    },
    {
      value: 'coef2',
      expan:'coef2Expand',
      label: '平电'
    },
    {
      value: 'coef1',
      expan:'coef1Expand',
      label: '谷电'
    }
  ])

  const preiodIndex = ref(0)

  const timeList = ref([
      "00:00-01:00",
      "01:00-02:00",
      "02:00-03:00",
      "03:00-04:00",
      "04:00-05:00",
      "05:00-06:00",
      "06:00-07:00",
      "07:00-08:00",
      "08:00-09:00",
      "09:00-10:00",
      "10:00-11:00",
      "11:00-12:00",
      "12:00-13:00",
      "13:00-14:00",
      "14:00-15:00",
      "15:00-16:00",
      "16:00-17:00",
      "17:00-18:00",
      "18:00-19:00",
      "19:00-20:00",
      "20:00-21:00",
      "21:00-22:00",
      "22:00-23:00",
      "23:00-24:00",
  ])

  const isShare = ref(false)

  const showSwitch = ref(false)

  const isShowBase = ref(false)
  const isShowServe = ref(false)


  const showTipModal = ref(false)
  const showTipSusModal = ref(false)
  const readDevTime = ref(null)
  const readSpeedTime = ref(null)
  const readSpeedStateTime = ref(null)
  const readSpeedStateFourTime = ref(null)
  const readSpeedStateZeroTime = ref(null)
  const readSpeedStateZeroBleTime = ref(null)
  const readSpeedSTateBleTime = ref(null)
  const timerSet = ref(null)

  const pageIsAlive = ref(true)
  const paramsShow = ref(false)
  const gfShowPopup = ref(false)
  const gfConfigShowPopup = ref(false)

  useDidHide(() => {
    pageIsAlive.value = false
    isShare.value = false
    console.log('hide');
    console.log(pageIsAlive.value);
    // clearInterval(globalStore.countdown)
    clearInterval(readDevTime.value)
    // globalStore.setGlobDialog({show:false,type:''})
    // globalStore.setDeviceTime(15)
  })

  const pixelRatio = ref()

  const apCanvans = ref()

  const apWidth = ref()

  const apHeight = ref()

  if(isAp) {
      // 获取dpr
      my.getSystemInfo({
        success: (res) => {
          console.log("getSystemInfo",res)
          pixelRatio.value = res.pixelRatio
          apWidth.value = 300 * pixelRatio.value
          apHeight.value = 330 * pixelRatio.value
        }
    })
  }

 const paramsClick = () => {
  // if (device.value?.type == 4) {
  //    onQuerySpeed()
  // }
  paramsShow.value = true
  }

  const deviceGConfigHandel = () => {
      gfShowPopup.value = true
  }

  const onSubmitSwitch = () => {
    console.log('onSubmitSwitch');
    console.log(isShowBase.value, "isShowBase");
    console.log(isShowServe.value, "isShowServe");
    showSwitch.value = false
  }

  const onSwitchHandel = () => {
    showSwitch.value = !showSwitch.value
  }

  const changeSwitch = (e,type) => {
    if (type == 1) {
      console.log(e,type);
      isShowBase.value = e
    } else {
      console.log(e, type);
      isShowServe.value = e
    }
  }


// 支付宝生成图片
 const onCanvasReady = async() =>{
    if(!isAp) return
    await getDetail()
    // 绘制图片
      my.createSelectorQuery().select('#myCanvas').node().exec(async(res) => {
        const canvas = res[0].node
        const ctx = canvas.getContext('2d')
        const imageSrc = device.value.qrcode;
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, 300*pixelRatio.value, 330*pixelRatio.value);

        let img = canvas.createImage()
        img.src = imageSrc
        img.onload = function () {
            console.log('load image success')
            // 绘制图片
            ctx.drawImage(img, 0, 0, 300*pixelRatio.value, 310*pixelRatio.value)
            ctx.fillStyle = 'black'; // 文字颜色

            // 设置文字样式
            ctx.font = `bold ${24*pixelRatio.value}px sans-serif`;
            // ctx.fillText(device.value.sn, 80*pixelRatio.value, 322*pixelRatio.value)
            ctx.fillText(`房号:${device.value?.house?.name}` || '无房源名', 80*pixelRatio.value, 322*pixelRatio.value)

            ctx.strokeStyle = 'black'; // 描边颜色
            ctx.setLineWidth(1); // 设置描边宽度以达到加粗效果
            // ctx.strokeText(device.value.sn, 80*pixelRatio.value, 322*pixelRatio.value); // 描边加粗
            ctx.strokeText(`房号:${device.value?.house?.name}` || '无房源名', 80*pixelRatio.value, 322*pixelRatio.value); // 描边加粗
        }
        ctx.restore()

        setTimeout(() => {
          apCanvans.value = canvas
        },1000)
    })
  }



/**
 * 绘制canvas
 */
const drawCanvas = async() => {
  const ctx = Taro.createCanvasContext('myCanvas');
  const imageSrc = device.value.qrcode;

    // 下载图片到本地
  const imageInfo = await Taro.downloadFile({
    url: imageSrc,
  }).catch(err => console.error('下载图片失败:', err))

  // 先填充整个画布为白色背景
  ctx.fillStyle = 'white';
  ctx.fillRect(0, 0, 300, 330);

  // 绘制图片，可以是二维码
  ctx.drawImage(imageInfo.tempFilePath, 0, 0, 300, 300);

  // 设置文字样式
  ctx.setFillStyle('black') // 文字颜色
  ctx.setFontSize(24) // 文字字号
  // ctx.font = 'bolder 24px sans-serif'
  ctx.font = 'bold 24px sans-serif';
  ctx.fillStyle = 'black'; // 文字颜色
  ctx.setTextAlign('center') // 文字居中

  ctx.strokeStyle = 'black'; // 描边颜色
  ctx.lineWidth = 1; // 设置描边宽度以达到加粗效果
  // ctx.strokeText(device.value.sn, 150, 320); // 描边加粗
  ctx.strokeText(`房号:${device.value?.house?.name}` || '无房源名', 150, 320); // 描边加粗


  // 绘制文字
  // ctx.fillText(device.value.sn, 150, 320); // 在画布上绘制填色的文本
  ctx.fillText(`房号:${device.value?.house?.name}` || '无房源名', 150, 320); // 在画布上绘制填色的文本

  // 实际地在画布上进行绘制
  ctx.draw(true);
};

  const showNetModal = ref(false)


  import { useGlobalStore } from '@/stores'
  const globalStore = useGlobalStore()

  const showBindPhone = ref(false)

  const bindPhone = ref('')

  const id = ref(0)

  const isNetNoSingle = ref(false)//是否为4g并且没信号

  const deviceSouce = ref({
    du: 0,
    total: 0,
  })

  const device = ref({

  })
  let lastOperationTime = 0;
  const MAXSEN = 12
  const curentSen = ref(12)
  const timerTip = ref(null)

  useUnload(() => {
    console.log('hide-unload');
    globalStore.setGlobDialog({show:false,type:''})
    pageIsAlive.value = false
    globalStore.setShare(false)
   clearInterval(globalStore.countdown)
   clearInterval(timerSet.value)
   clearInterval(readDevTime.value)
   clearInterval(timerCoef.value)
   globalStore.setDeviceTime(15)
  })

  let videoAd = null

  useLoad((options) => {
    id.value = options.id
    if (process.env.TARO_ENV === 'weapp') {
      if (wx.createRewardedVideoAd) {
        videoAd = wx.createRewardedVideoAd({
          adUnitId: 'adunit-067f3dc10c9faf10'
        })
        videoAd.onLoad(() => {
          console.log('激励视频光告加载成功');
        })
        videoAd.onError((err) => {
          console.error('激励视频光告加载失败', err)
          Taro.showToast({
            icon: "none",
            title: '广告加载失败，请稍后再试'
          })
        })
      }
    }
  })

  useDidShow(() => {
    // globalStore.setGlobDialog({show:false,type:''})
    // clearInterval(globalStore.countdown)
    globalStore.listenClient = id.value
    pageIsAlive.value = true

    getDetail()
    checkIsCollectDetailInfo()
  })

  usePullDownRefresh(() => {
    Taro.stopPullDownRefresh()
    getDetail()
  })

  const tipConfirmHandel = () => {
    showTipModal.value = false;
    showBindPhone.value = true
  }


  const tipSusConfirmHandel = () => {
    showTipSusModal.value = false
  }

  const confirmNetHandel = () => {
    showNetModal.value = false
    // device.value.net_type = 2
    // isNetNoSingle.value = true
    // deviceConnect.init(device.value.mac, device.value.net_type)
  }

 const sleepHandel = (time) => {
  return new Promise((resolve, reject) => {
     let tim = setTimeout(() => {
        resolve()
        clearTimeout(tim)
      }, time)
    })
  }



const fastInfo = ref(null)
// 判断是否搜集信息
const checkIsCollectInfo = () => {
  return new Promise((reslove, reject) => {
    // if(globalStore.who != "tenant") return reslove(false)
    request.get({
      url: 'user/checkFast',
      data: {
        device_id: id.value
      }
    }).then(res => {
      const isNeed = res.data.is_need
      fastInfo.value = res.data
      // const isNeed = true
      reslove(isNeed)
    }).catch(() => {
      reslove(false)
    })
  })
}

const fastDetailInfo = ref(null)
// 判断是否搜集信息
const checkIsCollectDetailInfo = () => {
  return new Promise((reslove, reject) => {
    // if(globalStore.who != "tenant") return reslove(false)
    request.get({
      url: 'contract/fastDetail',
      data: {
        device_id: id.value
      }
    }).then(res => {
      fastDetailInfo.value = res.data
    }).catch(() => {
      reslove(false)
    })
  })
}

  const wxShareModalSHow = ref(false)

  const wxNum = ref()//微信分享入住人数

const wxShareBtnHandel = () => {
    globalStore.setShare(false)
  // if (!wxNum.value) {
  //   Taro.showToast({
  //     title: '请填写入住人数',
  //     icon:"none"
  //     })
  //   }
  }

const shareHandel = (type) => {
  if (type == 1 && !fastDetailInfo.value) {
      // 指定采集
      wxShareModalSHow.value = true
    }
  }

  const wxConfirmHandel = async() => {
    wxShareModalSHow.value = false
    // 分享
    if(!fastDetailInfo.value) {
      const obj = {
        id: device.value.id,
        need_people:device.value.need_people,
        estate_name:device.value.house.estate_name,
        name:device.value.house.name,
        sn:device.value.sn
      }
      Taro.navigateTo({
        url: '/pages/device/fastCheck/fastCheck?deveice=' + JSON.stringify(obj)
      })
    }

  }

const onLongPress = (url) => {
    console.log('长按');
  if (isAp) {
    Taro.showActionSheet({
       itemList: ['保存图片'],
    }).then(res => {
      console.log(res);
        if (res.index === 0) {
        // 用户确定保存图片
        saveCanvasImage()
      }
    }).catch(err => console.error(err));
    } else {
      Taro.showActionSheet({
        itemList: ['保存图片'],
       }).then(res => {
          if (res.tapIndex === 0) {
            // 用户确定保存图片
            // saveImage(url);
            saveCanvasImage()
          }
      }).catch(err => console.error(err));
   }
  }


  // 支付宝保存图片
  const apSaveImage = () => {
    my.showAuthGuide({
      authType:'PHOTO',
      success:(res)=>{
          //shown为true时表示会显示权限引导弹窗，为false时表示用户已经授权
          // my.alert({content: '调用成功：'+JSON.stringify(res), });
        //使画布内容生成图片
          my.canvasToTempFilePath({
            canvas: apCanvans.value,
              // x:0,
              // y:0,
              // width:300,
              // height:330,
              destWidth:300*pixelRatio.value,
              destHeight:330*pixelRatio.value,
              // fileType:"jpg",
              // quality:1,
              success(res) {//生成的只是个临时图片，需要自己保存。
                my.saveImage({
                  //保存图片到本地
                  url:res.apFilePath,
                  fail(res) {
                    my.alert({
                      content: res.errorMessage || res.error,
                    });
                  },
                  success: () =>{
                    console.log('ok');
                    Taro.showToast({
                      title: '保存成功！',
                      icon:'success'
                    })
                  }
                });
              },
              fail(res){
                my.alert({
                  title:JSON.stringify("生成图片失败"), // alert 框的标题
                  success: (res) => {
                  },
                });
              }
            });
      },
      fail:(error)=>{
          my.alert({content: '调用失败：'+JSON.stringify(error), });
      },
  });
  }

  /**
   * canvas画布保存图片到相册
   * 需要先请求用户授权保存相册的权限
   * 参考文档：https://developers.weixin.qq.com/miniprogram/dev/api/media/image/wx.saveImageToPhotosAlbum.html
   * 注意：保存图片到相册的权限需要用户手动授权，需要在用户触发保存图片的操作时再进行授权提示
   * 保存图片到相册的操作需要在用户授权后才能进行
   * <AUTHOR>
   * @date 2024-05-08
   */
  const saveCanvasImage = () => {
      // 请求保存图片到相册的权限
      console.log('0');
      if (isAp) {
         apSaveImage()
        return
      }
      Taro.getSetting().then(res => {
        if (!res.authSetting['scope.writePhotosAlbum']) {
          Taro.authorize({
            scope: 'scope.writePhotosAlbum',
          }).then(() => {
            saveImageHandel()
          }).catch(() => {
            // 用户拒绝授权，引导到设置页面
            Taro.showModal({
              title: '提示',
              content: '需要您授权保存相册',
              showCancel: false,
            }).then(modalRes => {
              if (modalRes.confirm) {
                Taro.openSetting();
              }
            });
          });
        } else {
          // 已有授权或用户同意授权
          saveImageHandel()
        }
    });
};

const saveImageHandel = () => {
  // 获取画布内容
  Taro.canvasToTempFilePath({
    canvasId: 'myCanvas',
    success: function(res) {
      //保存图片到相册
      Taro.saveImageToPhotosAlbum({
        filePath: res.tempFilePath,
        success() {
          Taro.showToast({ title: '保存成功', icon: 'success' });
        },
        fail() {
          Taro.showToast({ title: '保存失败', icon: 'none' });
        }
      });
    }
  });
}

/**
 * 保存图片到相册
 * @param {string} src 图片链接
 * <AUTHOR>
 * @date 2024-05-08
 */
  const saveImage = (src) => {
  // 请求保存图片到相册的权限
  Taro.getSetting().then(res => {
    if (!res.authSetting['scope.writePhotosAlbum']) {
      Taro.authorize({
        scope: 'scope.writePhotosAlbum',
      }).then(() => {
        downloadAndSaveImage(src);
      }).catch(() => {
        // 用户拒绝授权，引导到设置页面
        Taro.showModal({
          title: '提示',
          content: '需要您授权保存相册',
          showCancel: false,
        }).then(modalRes => {
          if (modalRes.confirm) {
            Taro.openSetting();
          }
        });
      });
    } else {
      // 已有授权或用户同意授权
      downloadAndSaveImage(src);
    }
  });
};

// 点击快捷入驻
const fastCheckInHandel = () => {
  console.log(device.value);
  const obj = {
    id: device.value.id,
    need_people:device.value.need_people,
  }
  Taro.navigateTo({
    url: '/pages/device/fastCheck/fastCheck?deveice=' + JSON.stringify(obj)
  })
}

const dishuHandel = () => {
  Taro.navigateTo({
    url: "/pages/other/waterDiNumEdit/waterDiNumEdit",
  });
}

const downloadAndSaveImage = (src) => {
  Taro.downloadFile({
    url: src,
  }).then(downloadRes => {
    if (downloadRes.statusCode === 200) {
      Taro.saveImageToPhotosAlbum({
        filePath: downloadRes.tempFilePath,
      }).then(() => {
        Taro.showToast({
          title: '保存成功',
          icon: 'success',
        });
      }).catch(() => {
        Taro.showToast({
          title: '保存失败，请稍后重试',
          icon: 'none',
        });
      });
    }
  });
};

  const refshHandelr = (showTaost,adcode) => {
    return new Promise((reslove,reject) => {
      // 地区code合设备id(二选一)
      let refData = {
      }
      if (adcode) {
        refData = {
          adcode: adcode
        }
      } else {
        refData = {
          device_id: device.value.id
        }
      }
      // 获取table数据
      request.get({
        url: 'business/getLvPrice',
        data: refData
      }).then((res) => {
        console.log(res);
        showTaost && Taro.showToast({
          title: '刷新成功！',
          icon:'success'
        })
        if (!res.data?.length) {
          tableData.value = [
            { name: '春秋季', month: [], coef1: [], coef2: [], coef3: [], coef4: [],provinceName: [],provinceCode:[] },
            { name: '夏冬季', month: [], coef1: [], coef2: [], coef3: [], coef4: [],provinceName: [],provinceCode:[] }
          ]
          // 添加展开的时间范围
          tableData.value = tableData.value.map(item => {
            return {
              ...item,
              coef1Expand: expandTimeRanges(item.coef1),
              coef2Expand: expandTimeRanges(item.coef2),
              coef3Expand: expandTimeRanges(item.coef3),
              coef4Expand: expandTimeRanges(item.coef4),
              coef1Merge: mergeTimeRanges(item.coef1),
              coef2Merge: mergeTimeRanges(item.coef2),
              coef3Merge: mergeTimeRanges(item.coef3),
              coef4Merge: mergeTimeRanges(item.coef4),
            }
          })
          console.log(tableData.value,"processData");
          reslove()
          return 
        }
        tableData.value = processData(res.data)
        // 添加展开的时间范围
        tableData.value = tableData.value.map(item => {
          return {
            ...item,
            coef1Expand: expandTimeRanges(item.coef1),
            coef2Expand: expandTimeRanges(item.coef2),
            coef3Expand: expandTimeRanges(item.coef3),
            coef4Expand: expandTimeRanges(item.coef4),
            coef1Merge: mergeTimeRanges(item.coef1),
            coef2Merge: mergeTimeRanges(item.coef2),
            coef3Merge: mergeTimeRanges(item.coef3),
            coef4Merge: mergeTimeRanges(item.coef4),
          }
        })
        console.log(tableData.value,"processData");
        reslove()
      }).catch(reject)
    })
  }

  const getDetail = () => {
    return new Promise(reslove => {
      request.get({
        url: 'device/' + id.value,
        showLoading:true
      }).then(res => {
        device.value = res.data
        basic_price.value = res.data?.basic_price
        service_price.value = res.data?.service_price
        coef2.value = res.data.coef2
        coef1.value = res.data.coef1
        coef3.value = res.data.coef3
        coef4.value = res.data.coef4
        if(device.value.type == 5 || device.value?.type == 6) {
          // 获取地区
          request.get({
            url: '/api/area/all',
            ISCUSTOM: true
          }).then(async res => {
            areaList.value = res.data
            await refshHandelr(false)
            // 根据当前省份渲染areaIndex
            const idx = areaList.value.findIndex(item => item.name.indexOf(tableData.value && tableData.value[0] && tableData.value[0]?.provinceName && tableData.value[0]?.provinceName[0]) > -1)
            if (idx != -1) {
              areaIndex.value = idx
            } else {
              // 如果没有匹配到，则根据房源信息的省份匹配
             const idx = areaList.value.findIndex(item => item.name.indexOf(device.value?.house?.province) > -1)
              if (idx != -1) {
                areaIndex.value = idx
              }
            }
          })
        }
        reslove()
        // 防止第一次读取失败
        if (!res.data.read_speed_at) {
          device.value = {...res.data,read_speed_at:'2000-01-01 00:00:00'}
        }
        // 防止第一次清表失败
        if (!res.data.recharge_callback_at) {
          device.value = {...res.data,recharge_callback_at:'2000-01-01 00:00:00'}
        }
        if (!res.data.coef_callback_at) {
            device.value.coef_callback_at = ''
        }
        if (res.data.net_type == 1 && res.data.signal <= 0) {
          // 为4G设备且没有信号 走蓝牙操作表的逻辑
          // TODO
          showNetModal.value = true
          device.value.net_type = 2
          isNetNoSingle.value = true
          deviceConnect.init(device.value.mac, device.value.net_type)
          return
        }
        // 非4G设备扫描蓝牙
        deviceConnect.init(device.value.mac, device.value.net_type)
      })
    })
  }

 const bindHandel = () => {
  //  Taro.showToast({
  //     icon:'error',
  //     title: '功能正在完善中...',
  //     duration:3000
  //   })
    showTipModal.value = true;
  }

const onSubmitBindPhone = () => {
  if (bindPhone.value.length === 0) return Taro.showToast({
    title: '请输入手机号',
    icon:'none'
  })
  if (!new RegExp(/^(?:(?:\+|00)86)?1[3-9]\d{9}$/).test(+bindPhone.value)) return Taro.showToast({
    title: '请输入正确的手机号',
    icon:'none'
  })
    // 接口存进登录态
    request.post({
      url: 'device/shareMobile',
      data: {
        sn: unref(device).sn,
        mobile: unref(bindPhone)
      }
    }).then((_res) => {
      showBindPhone.value = false
      showTipSusModal.value = true
      // Taro.showToast({
      //     icon:'success',
      //     title: '开通成功!',
      //     duration:3000
      //   })
    })
  }

  const showGaoJing = ref(false)
  const gjLow = ref('')
  const onShowGaoJing = () => {
    console.log('onShowGaoJing')
    showGaoJing.value = true
  }
  const onCloseGaoJing = () => {
    showGaoJing.value = false
  }
  const onSubmitGaoJing = () => {
    request.post({
      url: 'device/' + id.value + '/update',
      data: {
        low: gjLow.value
      }
    }).then(res => {
      device.value.low = gjLow.value
      gjLow.value = ''
      onCloseGaoJing()
    })
  }

  const showQrCode = ref(false)
  const onShowQrCode = () => {
    if (device.value.qrcode) {
      showQrCode.value = true
    } else {
      request.post({
        url: 'device/' + id.value + '/qrcode'
      }).then(res => {
        device.value.qrcode = res.data.url
        showQrCode.value = true
      })
    }
  }
  const onCloseQrCode = () => {
    showQrCode.value = false
  }

  const showSpeed = ref(false)
  const speed = ref('')
  const onShowSpeed = () => {
    showSpeed.value = true
  }
  const onCloseSpeed = () => {
    showSpeed.value = false
  }
  const onSubmitSpeed = () => {
    console.log(speed.value);
    if (device.value?.type == 4 || device.value?.type == 6) {
      // 互感电表
      if (speed.value <= 0 || speed.value > 100000) {
        return Taro.showToast({
          icon: 'error',
          title:'无效数据！'
        })
      }
      if (speed.value > 0) {
        Taro.showLoading({
          title: '正在写入中...',
          mask: true
        })
        if (device.value.net_type === 1 && !isNetNoSingle.value) {
          deviceConnect.speed(speed.value, true).then(_=> {
              listenDeviceSpeed(false)
          })
        } else {
          deviceConnect.getConnection()
            .then(() => {
              deviceConnect.speed(speed.value, true).then(_=> {
                listenDeviceSpeedBle()
              })
            })
            .catch(() => {
              if (pageIsAlive.value) {
                globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
                globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
              }
          })
        }
      }
      return
    }
    if (speed.value < 10 || speed.value > 120) {
      return Taro.showToast({
        icon: 'error',
        title:'无效数据！'
      })
    }
    if (speed.value > 0) {
      Taro.showLoading({
        title: '正在写入中...',
        mask: true
      })
      if (device.value.net_type === 1 && !isNetNoSingle.value) {
        deviceConnect.speed(speed.value / 100,true).then(_=> {
            listenDeviceSpeed(false)
        })
      } else {
        deviceConnect.getConnection()
          .then(() => {
            deviceConnect.speed(speed.value / 100,true).then(_=> {
              listenDeviceSpeedBle()
            })
          })
          .catch(() => {
            if (pageIsAlive.value) {
              globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
              globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
            }
        })
      }
    }
  }

  const coef2 = ref('')//平段电价
  const coef4 = ref('')//尖电价
  const coef1 = ref('')//低谷电价
  const coef3 = ref('')//高峰电价
  const areaList = ref([])
  const areaIndex = ref(0)
  const seasonIndex = ref(0)
  const tableData = ref(null)

  const transTime = computed(() => {
    return tableData.value && tableData.value.length && tableData.value[seasonIndex.value][preiods.value[preiodIndex.value].expan]
  })

  const bindPickerChange = (e) => {
    console.log(e.detail.value);
    areaIndex.value = e.detail.value;
    refshHandelr(false,areaList.value[e.detail.value].adcode)
  }

  const bindPickerSeasonIndexChange = (e) => {
    seasonIndex.value = e.detail.value;
  }

  // 重置时间段所有选择
  const resetTimeSolt = () => {
    preiods.value.forEach((item,index) => {
      tableData.value[seasonIndex.value][item.expan] = []
    })
  }

  const getSeasonTimeIndex = (t) => {
    let obj = {idx:-1}
    if(seasonIndex.value == 0) {
      // 春秋季
      // 此时需要查询夏冬季有没有对应时间有的话提示用户删除
      tableData.value && tableData.value.length && tableData.value.forEach((item,index) => {
        if(index == 0) {
          // 夏东季
          preiods.value.filter(p => p.expan != preiods.value[preiodIndex.value].expan).forEach((mItem,sidx) => {
            // console.log(mItem,"mItem");
            item[mItem.expan].forEach((titem, tidx) => { 
              // console.log(titem, tidx,"titem, tidx");
              if (titem == t) { 
                // console.log(mItem,titem, tidx,"mItem","titem, tidx");
                obj = {idx: tidx, preiod: mItem,preIdx: sidx}
              }
            })
          })
        }
      })
    } else {
      // 夏东季
      tableData.value && tableData.value.length && tableData.value.forEach((item,index) => {
        if(index == 1) {
          // 夏东季
          preiods.value.filter(p => p.expan != preiods.value[preiodIndex.value].expan).forEach((mItem,sidx) => {
            // console.log(mItem,"mItem");
            item[mItem.expan].forEach((titem, tidx) => { 
              if (titem == t) { 
                // console.log(mItem,titem, tidx,"mItem","titem, tidx");
                obj = {idx: tidx, preiod: mItem,preIdx: sidx}
              }
            })
          })
        }
      })
    }
    return obj
  }

  // 时段选择
  const onSelectTime = (time) => {
    console.log(time);
    console.log(getSeasonTimeIndex(time), "getSeasonTimeIndex");
    const obj = getSeasonTimeIndex(time)
    if (obj.idx != -1) {
      Taro.showToast({
        icon: 'none',
        title: `该时段已存在${obj.preiod.label},请先删除！`
      })
      return
    }
    // 如果已经有的话就删除，没有的话就添加
    const idx = transTime.value.indexOf(time)
    if (idx != -1) {
      transTime.value.splice(idx, 1)
    } else {
      tableData.value[seasonIndex.value][preiods.value[preiodIndex.value].expan].push(time)
    }
    console.log(tableData.value, "tableData.value");
  }

  /**
   * 轮询查询coef_callback_at的值是否更新大于上次的1s一次60s超时
   */ 
  const pollingCoefCallbackAt = () => {
    return new Promise((resolve, reject) => {
      timerCoef.value = setInterval(() => {
        request.get({
          url: 'device/' + id.value,
          showLoading:false
        }).then(res => {
          if ((res.data.coef_callback_at ? res.data.coef_callback_at : '') > device.value.coef_callback_at) {
            clearInterval(timerCoef.value)
            resolve()
           Taro.hideLoading()
          }
        })
      }, 1000)
      setTimeout(() => {
        clearInterval(timerCoef.value)
        Taro.showToast({
          icon: 'error',
          title: '操作超时！'
        })
        Taro.hideLoading()
        reject()
      }, 1000 * 60)
    })
  }

  // 下一步
  const stepHandeler = () => {
    if (preiodIndex.value < 3) {
     return preiodIndex.value++
    }
    console.log(combineTimeRanges(tableData.value[seasonIndex.value]), "combineTimeRanges");

    // 计算总月数
    let monthCount = 0
    tableData.value && tableData.value.length && tableData.value.forEach((item,index) => {
      monthCount += item.month.length
    })
    console.log(monthCount, "monthCount");

    if (monthCount < 12) { 
      return Taro.showToast({
        icon: 'none',
        title: '月份不能为空,请勾选！'
      })
    }
    
    if (combineTimeRanges(tableData.value[seasonIndex.value]).length < 24) {
      return Taro.showToast({
        icon: 'none',
        title: '时段不能为空,请勾选！'
      })
    }
    console.log('保存');
    // 两个季节都保存

    console.log(tableData.value);
    // const data = {
    //   adcode: areaList.value[areaIndex.value].adcode,
    //   month: JSON.stringify(tableData.value[seasonIndex.value].month),
    //   data: JSON.stringify(combineTimeRanges(tableData.value[seasonIndex.value])),
    //   season: seasonIndex.value == 0 ? '1' : '2',
    //   device_id: device.value.id,
    // }
    const data1 = {
      adcode: areaList.value[areaIndex.value].adcode,
      month: JSON.stringify(tableData.value[0].month),
      data: JSON.stringify(combineTimeRanges(tableData.value[0])),
      season: '1',
      device_id: device.value.id,
    }
    const data2 = {
      adcode: areaList.value[areaIndex.value].adcode,
      month: JSON.stringify(tableData.value[1].month),
      data: JSON.stringify(combineTimeRanges(tableData.value[1])),
      season: '2',
      device_id: device.value.id,
    }
    Taro.showLoading({
      title: '保存中...',
      mask: true
    })
    request.post({
      url:'business/createLvPrice',
      data: data1,
      showLoading:false
    }).then( async (res) => {
     await request.post({
        url:'business/createLvPrice',
        data: data2,
        showLoading:false
      })
      // 需要执行设备操作
      request.get({
        url: 'business/makeFg',
        data: {
          sn: device.value.sn
        }
      },{
        showLoading:true
      }).then(() => {
        Taro.showLoading({
          title: '正在操作...',
          mask: true
        })
        pollingCoefCallbackAt()
          .then(() => {
            refshHandelr()
             .then(() => {
                 Taro.showToast({
                   icon: 'success',
                   title: '保存成功！'
                 })
                 gfConfigShowPopup.value = false
             })
          })
      })
    })
    console.log(data,"data");
  }

  const getSeasonMonthIndex = (m) => {
    let idx = -1
    if(seasonIndex.value == 0) {
      // 春秋季
      // 此时需要查询夏冬季有没有对应月份有的话提示用户删除
      tableData.value && tableData.value.length && tableData.value.forEach((item,index) => {
        if(index == 1) {
          // 夏东季
          item.month.forEach((mItem,sidx) => {
            if(mItem == m) {
              idx = sidx
            }
          })
        }
      })
    } else {
      // 夏东季
      tableData.value && tableData.value.length && tableData.value.forEach((item,index) => {
        if(index == 0) {
          // 夏东季
          item.month.forEach((mItem,sidx) => {
            if(mItem == m) {
              idx = sidx
            }
          })
        }
      })
    }
    return idx
  }

  // 月份选择
  const onMonthSelect = (m) => {
    console.log(m);
    // 如果选的月份在另一个季节，则提示用户删除之前的
    const idx = getSeasonMonthIndex(m)
    console.log(idx,"idx");
    
    if (idx != -1) {
      Taro.showToast({
        icon: 'none',
        title: `该月已存在${seasonIndex.value == 0 ? '夏冬季' : '春秋季'},请先删除！`
      })
      return
    }

    // 点击的有的话先删除，没有则添加
    const idx2 = tableData.value[seasonIndex.value].month.indexOf(m)
    if (idx2 != -1) {
      tableData.value[seasonIndex.value].month.splice(idx2, 1)
    } else {
      tableData.value[seasonIndex.value].month.push(m)
    }
    console.log(tableData.value, "tableData.value");

  }

  const showPrice = ref(false)
  // const price = ref('')
  const basic_price = ref('0')
  const service_price = ref('0')
  const price = computed(() => {
    // return device.value?.agent?.type == 2 ? (Number(basic_price.value) + Number(service_price.value) + Number(device.value?.agent?.service)).toFixed(2) : (Number(basic_price.value) + Number(service_price.value)).toFixed(2)
    // 统一 基础电价+服务费
    return (Number(basic_price.value) + Number(service_price.value)).toFixed(2)
  }
  )
  const onShowPrice = () => {
    showPrice.value = true
  }
  const onClosePrice = () => {
    showPrice.value = false
  }
const onSubmitPrice = () => {
    if (device.value.type == 5 || device.value?.type == 6) {
      // 谷峰表电价设置
      if (!coef2.value || !coef4.value || !coef1.value || !coef3.value) { 
        Taro.showToast({
          icon: 'none',
          title: '请输入所有分段电价！'
        })
        return
      }
      request.post({
        url: 'business/updateCoef',
        data: {
          coef1: coef1.value,
          coef2: coef2.value,
          coef3: coef3.value,
          coef4: coef4.value,
          sn: device.value.sn
        }
      }).then(res => {
          // 需要执行设备操作
        request.get({
          url: 'business/makeFg',
          data: {
            sn: device.value.sn
          }
        },{
          showLoading:true
        }).then(() => {
          Taro.showLoading({
            title: '正在操作...',
            mask: true
          })
          pollingCoefCallbackAt()
            .then(() => {
              onClosePrice()
              Taro.showToast({
                icon: 'success',
                title: '修改成功'
              })
            })
        })

      })
      return
    }
    if(basic_price.value == '') {
      return Taro.showToast({
        icon: 'error',
        title: device.value.type == 2 ? '请输入基础水价' : '请输入基础电价'
      })
    }
    if (!service_price.value || service_price.value == '0') {
        service_price.value = '0.00'
     }
    request.post({
      url: 'device/' + id.value + '/update',
      data: {
        price: price.value,
        basic_price: basic_price.value,
        service_price: service_price.value || '0.00'
      }
    }).then(res => {
      device.value.price = price.value
      onClosePrice()
      Taro.showToast({
        icon: 'success',
        title: '修改成功'
      })
    })
  }

  const onShare = () => {
    Taro.showShareMenu({
      menus: ['shareAppMessage'],
      success: () => {
        console.log('success')
      },
      fail: (err)=> {
        console.log(err)
      }
    })
  }

  // 广告
const pdHandel = () => {
  return new Promise((resolve, reject) => {
    if(process.env.TARO_ENV === "weapp")  {
        // 若在开发者工具中无法预览广告，请切换开发者工具中的基础库版本
        // 在页面中定义激励视频广告
        Taro.showLoading({
          title: '加载中...',
          mask: true
        })
      if (!videoAd && wx.createRewardedVideoAd) {
          videoAd = wx.createRewardedVideoAd({
          adUnitId: 'adunit-067f3dc10c9faf10'
          })
          videoAd.onLoad(() => {
            console.log('激励视频光告加载成功');
          })
          videoAd.onError((err) => {
            console.error('激励视频光告加载失败', err)
            Taro.showToast({
              icon: "none",
              title: '广告加载失败，请稍后再试'
            })
          })
        }
        // 在页面onLoad回调事件中创建激励视频广告实例
        if (wx.createRewardedVideoAd && videoAd) {
          videoAd.onClose((res) => {
            Taro.hideLoading()
             // 用户点击了【关闭广告】按钮
            if (res && res.isEnded) {
              // 正常播放结束，可以下发游戏奖励
              console.log('正常播放结束，可以下发游戏奖励');
              resolve()
            } else {
              // 播放中途退出，不下发游戏奖励
              Taro.showToast({
                icon: "none",
                title: '播放中途退出，请观看完整视频'
              })
              console.log('播放中途退出，不下发游戏奖励')
            }
          })
        }
        // 用户触发广告后，显示激励视频广告
        if (videoAd) {
          videoAd.show()
            .then(() => {
                Taro.hideLoading()
            })
            .catch(() => {
            // 失败重试
            videoAd.load()
              .then(() => videoAd.show()
                .then(() => {
                  Taro.hideLoading()
                })
              )
              .catch(err => {
                console.error('激励视频 广告显示失败', err)
                Taro.hideLoading()
              })
          })
        }
    } else {
      resolve()
    }
  })
  }

  const getCurrentPageUrl = () => {
    const pages = Taro.getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = currentPage.route // 获取当前页面路由
    const options = currentPage.options // 获取当前页面参数

    let urlWithParams = currentUrl

    if (Object.keys(options).length > 0) {
      urlWithParams += '?'
      for (const key in options) {
        urlWithParams += `${key}=${options[key]}&`
      }
      urlWithParams = urlWithParams.slice(0, -1) // 去除最后一个 '&'
    }

    return urlWithParams
  }

  function isExpired(pastDate, months) {
    const pastDateTime = new Date(pastDate);
    const currentDateTime = new Date();

    // 计算月份差
    const monthDiff = (currentDateTime.getFullYear() - pastDateTime.getFullYear()) * 12 + currentDateTime.getMonth() - pastDateTime.getMonth();
    console.log(monthDiff, "monthDiff");
    return monthDiff > months;
  }

  const linshiShow = ref(false)//是否点击了临时用电

  //临时用电-跳转
const linshiJumpHandel = async () => {
  linshiShow.value = true
     // 需要检测网络信号
    await checkNetInfo()
    console.log('ok');
    if (await checkIsCollectInfo()) {
      Taro.showModal({
          title: '温馨提示',
          showCancel: false,
          content: '应监管部门的要求，请完成入住人员身份信息登记，感谢您的配合。',
          success: function (res) {
            if (res.confirm) {
              console.log('用户点击确定')
            } else if (res.cancel) {
              console.log('用户点击取消')
            }
          }
        })
      return
    }
    // 蓝牙提示先抄表
    if (device.value.net_type === 1 && !isNetNoSingle.value) {
      // 4g
    } else {
      globalStore.setGlobDialog({show:true,type:'BleConnectionTip'})
      return
    }
    globalStore.setGlobDialog({show:false,type:''})
      Taro.navigateTo({
            url: `/pages/device/recharge/recharge?id=${id.value}&mode=${1}`
      })  
  }
  

  // 跳转充值页面 //mode 1:临时用电（需要加次数每日限制5次） 2：房东代充
const jumpRecharge = () => {
  if (device.value?.agent?.type == 2) {
    Taro.showActionSheet({
      itemList: ['临时用电', '代客充值'],
      success: (res) => {
        let Type = res.tapIndex == 0 ? 1 : 2;
        if (process.env.TARO_ENV === "alipay") {
          Type = res.index == 0 ? 1 : 2;
        }
          Taro.navigateTo({
            url: `/pages/device/recharge/recharge?id=${id.value}&mode=${Type}`
          })
      }
    })
  } else {
    // const whiteList = ['***********','***********']
    // if (isExpired(globalStore.userInfo?.business?.created_at,12) && !whiteList.includes(globalStore.userInfo?.mobile)) {
    //   // 注册12月以上
    //   pdHandel().then(() => {
    //     const currentPath = getCurrentPageUrl()
    //     console.log('当前页面路径：', currentPath)
    //     if(currentPath.indexOf('recharge') > -1) return
    //     Taro.navigateTo({
    //       url: '/pages/device/recharge/recharge?id=' + id.value
    //     })
    //   })
    // } else {
      Taro.navigateTo({
        url: '/pages/device/recharge/recharge?id=' + id.value
      })
    // }
  }
}

  const confirmGlobHandel = () => {
    deviceConnect.queryStatus()
      .catch(() => {
      if (pageIsAlive.value) {
        clearInterval(readDevTime.value)
        globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
        globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
      }
  })
      listenDeviceReadBle(false).then(() => {
        globalStore.setGlobDialog({show:false,type:''})
        setTimeout(() => {
          //如果点击的临时用电
          if (linshiShow.value) {
            Taro.navigateTo({
              url: `/pages/device/recharge/recharge?id=${id.value}&mode=${1}`
            }) 
          } else {
            jumpRecharge()
          }
          // Taro.navigateTo({
          //     url: '/pages/device/recharge/recharge?id=' + id.value
          //   })
        },0)
      })
  }

  const onRecharge = async() => {
    linshiShow.value = false
    // 需要检测网络信号
    await checkNetInfo()
    console.log('ok');
    if (await checkIsCollectInfo()) {
      Taro.showModal({
          title: '温馨提示',
          showCancel: false,
          content: '应监管部门的要求，请完成入住人员身份信息登记，感谢您的配合。',
          success: function (res) {
            if (res.confirm) {
              console.log('用户点击确定')
              // Taro.navigateTo({
              //   url:"/pages/tenant/tenantInfo/tenantInfo"
              // })
              // Taro.switchTab({
              //  url: '/pages/index/index'
              //  })
            } else if (res.cancel) {
              console.log('用户点击取消')
            }
          }
        })
      return
    }
    // 蓝牙提示先抄表
    if (device.value.net_type === 1 && !isNetNoSingle.value) {
      // 4g
    } else {
      globalStore.setGlobDialog({show:true,type:'BleConnectionTip'})
      return
    }
    globalStore.setGlobDialog({show:false,type:''})
    jumpRecharge()
  }
  const onChangeStatus = () => {
  // 水表的话每次操作需要间隔3秒才可以再次操作
    if(device.value.type == 2) {
      clearInterval(timerTip.value)
      if (lastOperationTime && Math.floor(Date.now() / 1000) - lastOperationTime < MAXSEN) {
        curentSen.value = MAXSEN - (Math.floor(Date.now() / 1000) - lastOperationTime)
        showToastTip.value = true
        timerTip.value = setInterval(() => {
          curentSen.value = MAXSEN - (Math.floor(Date.now() / 1000) - lastOperationTime)
          if (curentSen.value <= 0) {
            clearInterval(timerTip.value)
          }
        },1000)
        // Taro.showToast({
        //   title: `操作过于频繁，请${MAXSEN - (Math.floor(Date.now() / 1000) - lastOperationTime)}秒后再试`,
        //   icon: 'none'
        // });
        return;
      }
    }
    if (device.value.status !== 2) {
      Taro.showModal({
        title: device.value.type == 2 ? '水表锁定' : '电表锁定',
        content: '是否对当前设备进行强制拉闸',
        success: (res) => {
          if (res.confirm) {
            Taro.showLoading({
              title: '正在拉闸中...',
              mask: true
            })
            if (device.value.net_type === 1 && !isNetNoSingle.value) {
              deviceConnect.stop()
              listenDeviceStatus()
            } else {
              deviceConnect.getConnection()
              .then(() => {
               deviceConnect.stop()
              listenDeviceStatusBle()
            })
              .catch(() => {
                if (pageIsAlive.value) {
                  globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
                  globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
              }
            })
            }
          }
        }
      })
    } else {
      Taro.showLoading({
        title: '正在合闸中...',
        mask: true
      })
      if (device.value.net_type === 1 && !isNetNoSingle.value) {
          deviceConnect.start()
          listenDeviceStatus()
        } else {
          deviceConnect.getConnection()
          .then(() => {
          deviceConnect.start()
          listenDeviceStatusBle()
            })
              .catch(() => {
                if (pageIsAlive.value) {
                  globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
                  globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
              }
            })
        }
    }
  }

  const clearSuccess = () => {
    return new Promise((reslove) => {
      console.log("清零成功调用接口clear_success", {
        id: id.value,
        log: device.value.type == 2 ? `水表清零成功;清零前总水量:${deviceSouce.value.total},剩余水量:${deviceSouce.value.du}` : `电表清零成功;清零前总电量:${deviceSouce.value.total},剩余电量:${deviceSouce.value.du}`,
        before: deviceSouce.value.du,
      });
      
      request.post({
        url: 'device/clear_success',
        data: {
          id: id.value,
          log: device.value.type == 2 ? `水表清零成功;清零前总水量:${deviceSouce.value.total},剩余水量:${deviceSouce.value.du}` : `电表清零成功;清零前总电量:${deviceSouce.value.total},剩余电量:${deviceSouce.value.du}`,
          before: deviceSouce.value.du,
        },
        showToast: false,
      })
      .then(() => {
        Taro.setStorageSync('clearSucessTimeObj', JSON.stringify({
          time: Date.now(),
          id: id.value
        }))//记录清零成功时间
      }).finally(reslove)
    })
  }

const yesClearHandel = () => {
    globalStore.setIndexRecharegeId(null)
    globalStore.setIndexPageScrollTop(0)
    showClear.value = false
    if (device.value.net_type === 1 && !isNetNoSingle.value) {
      //设置总电量为0
      if (!device.value?.bus_params) {
        request.post({
          url: `device/${id.value}/total`,
          data: {
            value: 0,
          },
          showLoading:false,
          showToast:false
        })
      }
    deviceConnect.clear()
    listenDeviceClear()
    } else {
      deviceConnect.getConnection()
      .then(() => {
        Taro.showLoading({
          title: '正在清零中...',
          mask: true
        })
        deviceConnect.clear()
      listenDeviceClearBle()
      }).catch(() => {
        if (pageIsAlive.value) {
          globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
          globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
          }
      })
    }
  }

  const onClear = () => {
    // 2分钟只能清零一次-如果清零成功过
    const clearSucessTimeObj = Taro.getStorageSync('clearSucessTimeObj') ? JSON.parse(Taro.getStorageSync('clearSucessTimeObj')) : null
    if (clearSucessTimeObj && id.value == clearSucessTimeObj.id && (Date.now() - clearSucessTimeObj.time) < 1000 * 60 * 1) {
      return Taro.showToast({
        title: `清零频繁，请1分钟后再试`,
        icon: 'none'
      })
    }
    deviceSouce.value.du = device.value.du
    deviceSouce.value.total = device.value.total
    // clearSuccess()
    console.log(device.value.du * device.value.price);
    // if (device.value.du == 0) {
    //   return Taro.showToast({
    //     title: '当前电量为0',
    //     icon: 'none'
    //   })
    // }
    showClear.value = true
      // Taro.showModal({
      //   title: '电表清零',
      //   content: `当前剩余电费${(device.value.du * device.value.price).toFixed(2)}元，(总电量也同时清零，请谨慎操作!)`,
      //   confirmText:'确认清零',
      //   success: (res) => {
      //     if (res.confirm) {
      //       Taro.showLoading({
      //         title: '正在清零中...',
      //         mask: true
      //       })
      //       if (device.value.net_type === 1 && !isNetNoSingle.value) {
      //        deviceConnect.clear()
      //        listenDeviceClear()
      //       } else {
      //         deviceConnect.getConnection()
      //         .then(() => {
      //           deviceConnect.clear()
      //         listenDeviceClearBle()
      //         }).catch(() => {
      //           if (pageIsAlive.value) {
      //             globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
      //             globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
      //             }
      //         })
      //       }
      //     }
      //   }
      // })

  }

  const onDelete = () => {
    globalStore.setIndexRecharegeId(null)
    globalStore.setIndexPageScrollTop(0)
    Taro.showModal({
      title: '删除提示',
      content: '删除后设备不能再使用，是否确定删除设备？',
      success: (res) => {
        if (res.confirm) {
          request.delete({
            url: 'device/' + id.value
          }).then(_=> {
            Taro.navigateBack()
          })
        }
      }
    })
  }

  const onChangeHouse = () => {
    Taro.navigateTo({
      url: '/pages/device/rebind/rebind?id=' + id.value
    })
  }

const toLTempOrder = () => {
    Taro.navigateTo({
      url: "/pages/tenant/tempOrder/tempOrder?device_id="+device.value.id,
    });
  }

  //重启对应主表
const reloadMainHandel = () => {
    console.log('重启对应主表');
    Taro.showModal({
      title: '温馨提示',
      content: '是否确认刷新网络？',
      confirmText: '确认',
      success: res => {
        if (res.confirm) {
          Taro.showLoading({
            title: '刷新中...'
          })
          request.get({
            url: 'device/restart_master',
            showLoading:false,
            data: {
              mac: device.value.mac
            }
          }).then(res => {
            if (res.code == 200) {
              Taro.showToast({
                title: '重刷新成功',
                icon: "success",
                duration: 2000
              })
            }
          }).finally(() => {
            // Taro.hideLoading()
          }).catch(e => {

          })
        }
      }
    })
}

  // 重启4g设备
  const onNiteReloadHnadel = () => {
    Taro.showModal({
      title: '温馨提示',
      content: '是否确认重启设备？',
      confirmText: '确认',
      success: res => {
        if (res.confirm) {
          Taro.showLoading({
            title: '重启中...'
          })
          request.get({
            url: 'device/restart',
            showLoading:false,
            data: {
              mac: device.value.mac
            }
          }).then(res => {
            if (res.code == 200) {
              Taro.showToast({
                title: '重启成功',
                icon: "success",
                duration: 2000
              })
            }
          })
        }
      }
    })
  }

  const onQueryStatus = async() => {
    clearInterval(readDevTime.value)
    clearInterval(readSpeedTime.value)
    clearInterval(readSpeedStateTime.value)
    clearInterval(readSpeedStateFourTime.value)
    clearInterval(readSpeedStateZeroTime.value)
    clearInterval(readSpeedStateZeroBleTime.value)
    clearInterval(readSpeedSTateBleTime.value)
    if (device.value.net_type === 1 && !isNetNoSingle.value) {
      Taro.showToast({
        title: '4G表无需抄表',
        icon: 'none'
      })
      } else {
      deviceConnect.getConnection()
        .then(() => {
          deviceConnect.queryStatus()
        })
          .catch(() => {
            if (pageIsAlive.value) {
              clearInterval(readDevTime.value)
              globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
              globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
              // Taro.showModal({
              //   title: '提示',
              //   content: '抱歉！电表连接失败啦，请确保与电表保持3米距离重启小程序进行操作！',
              //   showCancel:true,
              //   cancelText: '查看教程',
              //   confirmText: '我知道了',
              //   success: function (res) {
              //     if (res.confirm) {
              //       console.log('用户点击我知道了')
              //     } else if (res.cancel) {
              //       console.log('用户点击查看使用教程')
              //       Taro.navigateTo({
              //         url: '/pages/qa/index'
              //       })
              //     }
              //   }
              // })
            }
        })
        Taro.showLoading({
          title: '正在读表中...',
          icon: 'none'
        })
        await listenDeviceReadBle()
        getDetail()
      }
  }

  /*const listenDeviceMessage = () => {
    if (globalStore.listenClient !== id.value) {
      return
    }
    setTimeout(() => {
      if (globalStore.device.message.length > 0) {
        let msg = globalStore.device.message.shift()
        console.log(msg, device.value.mac)
        if (msg.mac === device.value.mac) {
          console.log(msg, 'listenDeviceMessage')
          if (msg.du > -1) {
            device.value.du = msg.du
            device.value.read_at = formatTime(new Date())
            let postData = {
              du: msg.du,
            }
            if (msg.total > -1) {
              postData.total = msg.total
            }
            if (msg.power > -1) {
              postData.power = msg.power
            }
            request.post({
              url: 'device/' + id.value + '/read',
              showLoading: false,
              data: postData
            }).then(_=> {
              Taro.showToast({
                title: '电量已更新',
                duration: 3000,
                mask: true
              })
            })
          }
          if (msg.speed > -1) {
            device.value.speed = msg.speed
            speed.value = msg.speed / 10
            request.post({
              url: 'device/' + id.value + '/update',
              showLoading: false,
              data: {
                speed: msg.speed / 10
              }
            }).then(_=> {
              Taro.hideLoading()
              Taro.showToast({
                title: '操作成功！',
                duration: 3000,
                mask: true
              })
            })
          }
          if (msg.status > -1) {
            device.value.status = msg.status
            request.post({
              url: 'device/' + id.value + '/update',
              showLoading: false,
              data: {
                status: msg.status
              }
            }).then(_=> {
              if (msg.event === '0D') {
                Taro.hideLoading()
                Taro.showToast({
                  title: '操作成功！',
                  duration: 3000,
                  mask: true
                })
              }
            })
          }
        }
      }
      listenDeviceMessage()
    }, 1000)
  }*/

  const onClearLog = () => {
    Taro.navigateTo({
      url: '/pages/device/log/log?action=1&id=' + id.value
    })
  }
  const onRechargeLog = () => {
    Taro.navigateTo({
      url: '/pages/device/log/log?action=3&id=' + id.value
    })
  }

  const onQuerySpeed = () => {
    if (device.value.net_type === 1 && !isNetNoSingle.value) {
      deviceConnect.querySpeed()
      listenDeviceSpeed()
    } else {
      deviceConnect.getConnection()
      .then(() => {
        deviceConnect.querySpeed()
        listenDeviceSpeedBle()
      }).catch(() => {
        if (pageIsAlive.value) {
          globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
          globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
            // Taro.showModal({
            //   title: '提示',
            //   content: '抱歉！电表连接失败啦，请确保与电表保持3米距离重启小程序进行操作！',
            //   showCancel:true,
            //   cancelText: '查看教程',
            //   confirmText: '我知道了',
            //   success: function (res) {
            //     if (res.confirm) {
            //       console.log('用户点击我知道了')
            //     } else if (res.cancel) {
            //       console.log('用户点击查看使用教程')
            //       Taro.navigateTo({
            //         url: '/pages/qa/index'
            //       })
            //     }
            //   }
            // })
          }
       })
    }

    if (device.value.net_type === 1 && !isNetNoSingle.value) {
      // listenDeviceSpeed()
    } else {
      // listenDeviceSpeedBle()
    }
  }

  // 查询speed
  const listenDeviceSpeed = (flag = true) => {
    let s = 0
    return new Promise((resolve, reject) => {
      readSpeedTime.value = setInterval(() => {
        s ++
        request.get({
          url: 'device/' + id.value,
          showLoading: false
        }).then(res => {
          console.log('device:'+device.value.read_speed_at);
          console.log('data:'+res.data.read_speed_at);
          if (res.data.read_speed_at > (device.value.read_speed_at || '2000-01-01 00:00:00')) {
            if (flag) {
              device.value.speed = res.data.speed
              if (device.value?.type == 4 || device.value?.type == 6) {
                speed.value = res.data.speed / 1000
              } else {
                speed.value = res.data.speed / 10
              }
            }
            Taro.hideLoading()
            clearInterval(readSpeedTime.value)
            globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
            Taro.showToast({
              title: '操作成功',
              duration: 3000
            })
            resolve()
          }
          if (s >= 10) {
            Taro.hideLoading()
            clearInterval(readSpeedTime.value)
            Taro.showToast({
              title: '操作超时',
              icon: "error",
              duration: 2000
            })
            reject()
          }
        })
      }, 3000)
    })
  }

  // 查询speed
  const listenDeviceSpeedBle = () => {
    let s = 0
    return new Promise((resolve, reject) => {
      readSpeedStateTime.value = setInterval(() => {
        s ++
        if (globalStore.device.message.length > 0) {
          let msg = globalStore.device.message.shift()
          console.log(msg, device.value.mac)
          if (msg.mac === device.value.mac) {
            if (msg.speed > -1) {
              device.value.speed = msg.speed
              if (device.value?.type == 4 || device.value?.type == 6) {
                speed.value = msg.speed / 1000
              } else {
                speed.value = msg.speed / 10
              }
              Taro.hideLoading()
              clearInterval(readSpeedStateTime.value)
              Taro.showToast({
                title: '操作成功',
                duration: 3000
              })
              globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
              resolve()
            }
            if (s >= 15) {
              Taro.hideLoading()
              clearInterval(readSpeedStateTime.value)
              globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
              Taro.showToast({
                title: '操作超时',
                icon: "error",
                duration: 2000
              })
              reject()
            }
          }
        }

      }, 1000)
    })
  }

  // 查询清0
  const listenDeviceClear = () => {
    let s = 0
    return new Promise((resolve, reject) => {
      readSpeedStateZeroTime.value = setInterval(() => {
        s ++
        request.get({
          url: 'device/' + id.value,
          showLoading: false
        }).then(res => {
          //if (res.data.read_at > device.value.read_at && res.data.du <= 0) {
          if (res.data.du === 0  || res.data.recharge_callback_at > device.value.recharge_callback_at) {
            device.value.du = res.data.du
            device.value.total = res.data.total
            Taro.hideLoading()
            clearInterval(readSpeedStateZeroTime.value)
            // if (device.value.type == 2) {
            //   device.value.status = 2
            // }
            clearSuccess().then(() => {
              Taro.showToast({
                title: '操作成功',
                duration: 3000
              })
            })
            globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
            resolve()
          }
          if (s >= 10) {
            Taro.hideLoading()
            clearInterval(readSpeedStateZeroTime.value)
            Taro.showToast({
              title: '操作超时',
              icon: "error",
              duration: 3000
            })
            reject()
          }
        })
        }, 3000)
    })
  }


   // 拿到最新蓝牙设备的totalDu
  const listenDeviceReadBleTotal = () => {
  let s = 0
  s = 0
  return new Promise((resolve, reject) => {
    timerSet.value = setInterval(() => {
      s ++

      if (globalStore.device.message.length > 0) {
        let msg = globalStore.device.message.shift()
        console.log(msg, device.value.mac)
        if (msg.mac === device.value.mac) {
          if (msg.event === '0C') {
            resolve(msg.total)
            s = 0
            Taro.hideLoading()
            clearInterval(timerSet.value)
          }

          if (s >= 3) {
            s = 0
            Taro.hideLoading()
            clearInterval(timerSet.value)
            // Taro.showToast({
            //   title: '获取总电量超时',
            //   icon: 'warning',
            //   duration: 2000
            // })
            resolve(device.value.total)
          }
        }
      }

    }, 1000)
  })
  }

  const sleep = (timer) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve()
      }, timer)
    })
  }

  // 查询清0
  const listenDeviceClearBle = () => {
    let s = 0
    return new Promise((resolve, reject) => {
      readSpeedStateZeroBleTime.value = setInterval(async() => {
        s ++

        if (globalStore.device.message.length > 0) {
          let msg = globalStore.device.message.shift()
          console.log(msg, device.value.mac)
          if (msg.mac === device.value.mac) {
            if (msg.event === '0A' || msg?.du <= 0) {
              clearInterval(readSpeedStateZeroBleTime.value)
              globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
              device.value.du = msg.du
              device.value.read_at = formatTime(new Date())
              let postData = {
                du: msg.du,
                info: globalStore.who,
              }
              //设置总电量为0
              if (!device.value?.bus_params) {
                deviceConnect.setTotalDu(0)
              }
              await sleep(100)
              deviceConnect.queryStatus()//触发抄表
                  .catch(() => {
                  if (pageIsAlive.value) {
                    clearInterval(readDevTime.value)
                    globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
                    globalStore.setGlobDialog({show:true,type:'ConnectionFailed'})
                  }
              })
              const totalDu = await listenDeviceReadBleTotal()
              postData.total = totalDu
              device.value.total = totalDu
             await clearSuccess()
              request.post({
                url: 'device/' + id.value + '/read',
                showLoading: false,
                data: postData
              }).then(_=> {
                Taro.hideLoading()
                setTimeout(() => {
                  Taro.showToast({
                    title: '操作成功',
                    duration: 3000
                  })
                },0)
                // if (device.value.type == 2) {
                //   device.value.status = 2
                // }
                resolve()
              })
            }

          }
        }
        if (s >= 15) {
          Taro.hideLoading()
          clearInterval(readSpeedStateZeroBleTime.value)
          Taro.showToast({
            title: '操作超时',
            icon: "error",
            duration: 2000
          })
          reject()
        }

      }, 1000)
    })
  }

  // 操表
  const listenDeviceReadBle = (toast=true,str) => {
    let s = 0
    s=0
    clearInterval(readDevTime.value)
    return new Promise((resolve, reject) => {
      readDevTime.value = setInterval(() => {
        s ++

        if (globalStore.device.message.length > 0) {
          let msg = globalStore.device.message.shift()
          console.log(msg, device.value.mac)
          if (msg.mac === device.value.mac) {
            if (msg.event === '0C') {
              device.value.status = msg.status
              device.value.du = msg.du
              device.value.read_at = formatTime(new Date())
              let postData = {
                du: msg.du,
                status: msg.status,
                info: globalStore.who,
                log: str == 'noLog' ? 0 : 1
              }
              postData.total = msg.total
              postData.power = msg.power
              request.post({
                url: 'device/' + id.value + '/read',
                showLoading: false,
                data: postData,

              }).then(_=> {
                Taro.hideLoading()
                clearInterval(readDevTime.value)
                s=0
                setTimeout(() => {
                  if (toast) {
                  globalStore.setGlobDialog({show:false,type:'TimeLoading'})
                    Taro.showToast({
                    title: '抄表成功',
                    duration: 3000
                  })
                  }
                  },350)
                resolve()
              })
            }
            // if (s >= 20) {
            //   Taro.hideLoading()
            //   clearInterval(readDevTime.value)
            //   s=0
            //   Taro.showToast({
            //     title: '抄表超时',
            //     icon: 'error',
            //     duration: 2000
            //   })
            //   reject()
            // }
          }
        }
        if (s >= 30) {
              Taro.hideLoading()
              clearInterval(readDevTime.value)
              s=0
              Taro.showToast({
                title: '抄表超时',
                icon: 'error',
                duration: 2000
              })
              reject()
            }

      }, 1000)
    })
  }

  // 4G表更新状态查询结果
  const listenDeviceStatus = () => {
    let s = 0
    return new Promise((resolve, reject) => {
      readSpeedStateFourTime.value = setInterval(() => {
        s ++
        request.get({
          url: 'device/' + id.value,
          showLoading: false
        }).then(res => {
          if (res.data.status !== device.value.status) {
            device.value.status = res.data.status
            Taro.hideLoading()
            clearInterval(readSpeedStateFourTime.value)
            Taro.showToast({
              title: '操作成功',
              duration: 3000
            })
            lastOperationTime = Math.floor(Date.now() / 1000); // 转换为秒
            globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
            resolve()
          }
          if (s >= 10) {
            Taro.hideLoading()
            clearInterval(readSpeedStateFourTime.value)
            Taro.showToast({
              title: '操作超时',
              icon: "error",
              duration: 3000
            })
            reject()
          }
        })
        }, 3000)
    })
  }

  // ble表更新状态查询结果
  const listenDeviceStatusBle = () => {
    let s = 0
    s=0
    return new Promise((resolve, reject) => {
      readSpeedSTateBleTime.value = setInterval(() => {
        s ++
        console.log(s,"时间s");
        
        if (globalStore.device.message.length > 0) {
          let msg = globalStore.device.message.shift()
          console.log(msg, device.value.mac)
          if (msg.mac === device.value.mac) {
            if (msg.event === '0D') {
              device.value.status = msg.status
              device.value.read_at = formatTime(new Date())
              let postData = {
                status: msg.status,
                info: globalStore.who
              }
              request.post({
                url: 'device/' + id.value + '/read',
                showLoading: false,
                data: postData
              }).then(_=> {
                Taro.hideLoading()
                clearInterval(readSpeedSTateBleTime.value)
                s = 0
                Taro.showToast({
                  title: '操作成功',
                  duration: 3000
                })
                lastOperationTime = Math.floor(Date.now() / 1000); // 转换为秒
                globalStore.setGlobDialog({show:false,type:'BleConnectionTip'})
                resolve()
              })
            }

            // if (s >= 15) {
            //   Taro.hideLoading()
            //   s = 0
            //   clearInterval(readSpeedSTateBleTime.value)
            //   Taro.showToast({
            //     title: '操作超时',
            //     icon: "error",
            //     duration: 2000
            //   })
            //   reject()
            // }

          }
        }
        // if (s >= 20) {
        //     Taro.hideLoading()
        //     s = 0
        //     clearInterval(timer)
        //   setTimeout(() => {
        //     Taro.showToast({
        //       title: '操作超时',
        //       icon: "error",
        //       duration: 2000
        //     })
        //     },500)
        //     reject()
        //   }

        if (s >= 15) {
          Taro.hideLoading()
          s = 0
          clearInterval(readSpeedSTateBleTime.value)
          setTimeout(() => {
            Taro.showToast({
              title: '操作超时',
              icon: "error",
              duration: 2000
            })
            },500)
            reject()
        }
      }, 1000)
    })
  }

useShareAppMessage(() => {
  console.log('ssssss');
  if (device.value?.agent?.type == 2) {
    isShare.value = true
  }
    return {
      title: '' + device.value.house.estate_name + device.value.house.name,
      // path: '/pages/tenant/deviceDetail/deviceDetail?sn=' + device.value.sn + '&share=1' + '&wxNum=' + wxNum.value,
      path: '/pages/tenant/deviceDetail/deviceDetail?sn=' + device.value.sn + '&share=1'
    }
  })

</script>
